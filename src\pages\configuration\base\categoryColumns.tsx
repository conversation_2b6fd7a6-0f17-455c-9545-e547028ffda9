import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { PlusCircleTwoTone } from '@ant-design/icons';
import React from 'react';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { Space, DatePicker, Popconfirm, Switch, Tag, Tooltip } from 'antd';
import { UniSelect } from '@uni/components/src';
import IconBtn from '@uni/components/src/iconBtn';
import { useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';

export interface ConfigurationFormBaseProps {
  value?: any;
  onChange?: (value: any) => void;
  dataSource?: [];
  type?: string;
}

export const IcdeCategoryLevelSelect = (props: ConfigurationFormBaseProps) => {
  let dataSource = [];
  if (props.type === 'icde') {
    dataSource = [
      { Name: '类目', Code: 'Category' },
      { Name: '亚目', Code: 'SubCategory' },
    ];
  } else if (props.type === 'oper') {
    dataSource = [
      { Name: '类目', Code: 'Category' },
      { Name: '亚目', Code: 'SubCategory' },
      { Name: '细目', Code: 'SubSubCategory' },
    ];
  }
  return (
    <UniSelect
      dataSource={dataSource}
      placeholder="请选择分类"
      showSearch
      value={props?.value}
      optionNameKey={'Name'}
      optionValueKey={'Code'}
      allowClear={false}
      onChange={props?.onChange}
    />
  );
};

export const MainChapterSelect = (props: ConfigurationFormBaseProps) => {
  const [stdIcdeMainChapters, setStdIcdeMainChapters] = useState([]);

  useEffect(() => {
    getStdIcdeMainChaptersReq();
  }, []);

  const { run: getStdIcdeMainChaptersReq } = useRequest(
    () => {
      return uniCommonService(
        `Api/Sys/CodeSys/${
          props.type === 'icde'
            ? 'GetStdIcdeMainChapters'
            : 'GetStdOperMainChapters'
        }`,
        {
          method: 'POST',
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          setStdIcdeMainChapters(
            response?.data.map((str) => {
              return {
                Name: str,
                Code: str,
              };
            }),
          );
        } else {
          setStdIcdeMainChapters([]);
        }
      },
    },
  );

  return (
    <UniSelect
      dataSource={stdIcdeMainChapters || []}
      placeholder="请选择章节"
      showSearch
      value={props?.value}
      optionNameKey={'Name'}
      optionValueKey={'Code'}
      allowClear={false}
      onChange={props?.onChange}
    />
  );
};

const categoryBaseColumns = (type) => [
  {
    dataIndex: 'CategoryLevel',
    width: 60,
    align: 'center',
    order: 2,
    renderFormItem: () => <IcdeCategoryLevelSelect type={type} />,
  },
  {
    dataIndex: 'IcdeCode',
    width: 60,
  },
  {
    dataIndex: 'IcdeName',
    width: 200,
  },
  {
    dataIndex: 'SubChapter',
    visible: false,
    width: 200,
  },
  {
    dataIndex: 'SubSubChapter',
    visible: false,
    width: 200,
  },
  {
    dataIndex: 'Description',
    visible: false,
    width: 200,
  },
  {
    dataIndex: 'Excludes',
    width: 150,
    visible: false,
  },
  {
    dataIndex: 'Includes',
    width: 150,
    visible: false,
  },
];

export const categoryColumns = (type) => [
  {
    dataIndex: 'MainChapter',
    title: '章节',
    order: 1,
    width: 200,
    renderFormItem: () => <MainChapterSelect type={type} />,
  },
  ...categoryBaseColumns(type),
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => (
      <>
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.itemId);
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(ConfigurationEvents.DMR_ICDE_DELETE, {
                index,
                record,
              });
            }}
          />
        </Space>
      </>
    ),
  },
];
