import { useEffect, useRef } from 'react';

// 方案二：使用自定义Hook管理多个Select
const useSelectKeyboardControlPrecise = () => {
  const currentFocusedDropdown = useRef(null);

  const setFocusedDropdown = (id) => {
    currentFocusedDropdown.current = id;
    console.log('设置当前焦点下拉框:', id);
  };

  useEffect(() => {
    const handleKeyDown = (event) => {
      console.log('为下拉框', currentFocusedDropdown.current, event.key, event.defaultPrevented );

      if (
        currentFocusedDropdown.current !== null &&
        (event.key === 'PageUp' || event.key === 'PageDown') &&
        !event.defaultPrevented
      ) {
        console.log('为下拉框', currentFocusedDropdown.current, '阻止默认滚动');
        event.preventDefault();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return setFocusedDropdown;
};

export default useSelectKeyboardControlPrecise;
