import OperationDictionaryAdd from '@/pages/configuration/components/oper/add';
import { Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { MicroAppWithMemoHistory } from 'umi';
import { isEmptyValues } from '@uni/utils/src/utils';
import { history } from 'umi';

interface DmrAodData {
  hisId?: string;
  status?: boolean;

  [key: string]: any;

  parent?: {
    className?: string;
    mask?: boolean;
    width?: string;
    closable?: boolean;
    maskTransitionName?: string;
    transitionName?: string;
  };
}

export const DmrAod = (props: any) => {
  const dmrAodContainerRef = React.useRef(null);

  const dmrContainerRef = React.useRef(null);

  const [dmrAodOpen, setDmrAodOpen] = useState(false);

  const [dmrAodData, setDmrAodData] = useState<DmrAodData>({});

  useEffect(() => {
    (global?.window as any)?.eventEmitter?.on(
      'DMR_AOD_STATUS',
      (data: DmrAodData) => {
        setDmrAodOpen(data?.status ?? false);
        if (!isEmptyValues(data?.hisId)) {
          setDmrAodData(data);
        }
        if (!data?.status) {
          dmrContainerRef?.current?.closeAllModal();
          // 关闭 Modal的时候，恢复滚动条
          document.getElementById('content').style.overflowY = 'auto';
        }
      },
    );

    return () => {
      (global?.window as any)?.eventEmitter?.off('DMR_AOD_STATUS');
    };
  }, []);

  useEffect(() => {
    history.listen((location, action) => {
      console.log('History Listen', location, action);
      if (action === 'POP') {
        let AODStatus = (
          document?.querySelector('#dmr-aod-container .ant-modal-wrap') as any
        )?.style?.display;
        if (AODStatus !== undefined && AODStatus !== 'none') {
          setDmrAodOpen(false);
          dmrContainerRef?.current?.closeAllModal();
        }
      }
    });
  }, []);

  const cannotUseOperationInAod = ['NORMAL_SCREEN', 'CLEAR', 'NEXT'];

  return (
    <div
      id={'dmr-aod-container'}
      className={`dmr-aod-container ${dmrAodData?.parent?.className}`}
    >
      <Modal
        className={''}
        title=""
        open={dmrAodOpen}
        footer={false}
        zIndex={9999}
        mask={dmrAodData?.parent?.mask ?? true}
        closable={dmrAodData?.parent?.closable ?? true}
        width={dmrAodData?.parent?.width ?? '100%'}
        getContainer={() => document.getElementById('dmr-aod-container')}
        onCancel={() => {
          setDmrAodOpen(false);
          dmrContainerRef?.current?.closeAllModal();
          // 关闭 Modal的时候，恢复滚动条
          document.getElementById('content').style.overflowY = 'auto';
        }}
        maskTransitionName={dmrAodData?.parent?.maskTransitionName ?? undefined}
        transitionName={dmrAodData?.parent?.transitionName ?? undefined}
      >
        <MicroAppWithMemoHistory
          name="dmrIndex"
          url="/index"
          dmrAodProps={{
            dmrContainer: dmrContainerRef,
            extra: {
              type: 'AlwaysOnDisplay',
              refresh: true,
              isFullScreen: true,
              onOperationExtraProcess: (type: string) =>
                !cannotUseOperationInAod?.includes(type),
              commonContainerStyle: {
                marginTop: 36,
                height: 'calc(100% - 56px)',
                marginBottom: 20,
              },
              leftContainerShow: false,
              queryHeaderShow: false,
              containerRef: dmrAodContainerRef,
              ...dmrAodData,
            },
          }}
        />
      </Modal>
    </div>
  );
};
