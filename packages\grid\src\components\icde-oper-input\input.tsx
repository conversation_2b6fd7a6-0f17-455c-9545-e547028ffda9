import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { Checkbox, Form, Input, InputNumber, Tag, Tooltip } from 'antd';
// @ts-ignore
import { useModel } from '@@/plugin-model/useModel';
import {
  FormTableItemBaseProps,
  useDmrDragEditOnlyTableContext,
} from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import {
  getArrowUpDownEventKey,
  getSelectorDropdownContainerNode,
} from '../../utils';
import UniDmrSelect from '../dmr-select/UniDmrSelect';
import { isEmptyValues } from '@uni/utils/src/utils';
import { onReadonlyInputAndReadonlyItemClick } from '../../utils';
import dayjs from 'dayjs';
import './index.less';
import { TechDiagResultModal } from '../techDiagResult-modal/TechDiagResultModal';

const readonlyInputClickSelectAll =
  (window as any).externalConfig?.['dmr']?.readonlyInputClickSelectAll ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['dmr']
  ?.tableSelectorDropdownHeight;

interface IcdeOperCheckboxProps extends FormTableItemBaseProps {
  id: string;
  recordId: string;
  dataIndex: string;
  onChangeExtra?: (checked: boolean) => void;
  disabled?: boolean;
  dependencyKey?: string;
  dependencyValue?: any;
  form?: any;

  minimumChecked?: number;
}

export const IcdeOperCheckbox = (props: IcdeOperCheckboxProps) => {
  const dependencyFormValue =
    props?.recordId && props?.dependencyKey && props?.form
      ? Form.useWatch([props?.recordId, props?.dependencyKey], props?.form)
      : null;

  const icdeOperCheckboxValue = Form.useWatch(
    [props?.recordId, props?.dataIndex],
    props?.form,
  );

  let reachMinimumChecked = false;
  if (props?.minimumChecked !== undefined && props?.minimumChecked >= 0) {
    const IsReportedTrueCount = Form.useWatch(
      'IsReportedTrueCount',
      props?.form,
    );

    if (IsReportedTrueCount <= props?.minimumChecked) {
      reachMinimumChecked = true;
    }
  }

  return (
    <div
      className={'flex-row-center form-content-item-container'}
      style={{ justifyContent: 'center' }}
    >
      <Checkbox
        id={props?.id}
        checked={icdeOperCheckboxValue}
        disabled={
          (props?.dependencyValue
            ? dependencyFormValue === props?.dependencyValue
            : false) ||
          (reachMinimumChecked && icdeOperCheckboxValue)
        }
        onChange={(event) => {
          props?.onChange && props?.onChange(event?.target?.checked);
          props?.onChangeExtra && props?.onChangeExtra(event?.target?.checked);
        }}
      />
    </div>
  );
};

export interface OperIcdeExtraMapItem {
  display: string;
  label: string;
  color: string;
  className?: string;
  style?: React.CSSProperties;
}

interface OperationFieldInputProps extends FormTableItemBaseProps {
  containerClassName?: string;
  className?: string;
  recordId?: string;
  dataIndex: string;
  index: number;
  disabled?: boolean;
}
export const OperationFieldInput = (props: OperationFieldInputProps) => {
  return (
    <Tooltip title={props?.value}>
      <div
        className={`form-content-item-container ${props?.containerClassName}`}
      >
        <Input
          id={`formItem#${props?.dataIndex}#${props?.index}#Input`}
          className={`operation-input ${props?.className ?? ''}`}
          bordered={false}
          value={props?.value ?? ''}
          placeholder={'请输入'}
          contentEditable={true}
          disabled={props.disabled ?? false}
          onChange={(event) => {
            props?.onChange && props?.onChange(event?.target?.value);
          }}
        />
      </div>
    </Tooltip>
  );
};

interface IcdeOperationInputSelectorProps extends FormTableItemBaseProps {
  className?: string;
  recordId?: string;
  dataIndex: string;
  index: number;

  conditionDictionaryKey?: string;

  conditionDictionaryGroup?: string;

  optionNameKey?: string;
  optionValueKey?: string;
  optionTitleKey?: string;
  optionLabelProp?: string;

  dataSourceProcessor?: (dataSource: any[]) => any[];
  dataSource?: any[];

  disabled?: boolean;

  dropdownStyle?: React.CSSProperties;
  listHeight?: number;

  dropdownMatchSelectWidth?: boolean | number;
  nameFormat?: string;

  tableId?: string;

  onChangeExtra?: (value: any) => void;

  leftOneAutoSelect?: boolean;

  numberSelectItem?: boolean;

  // 表示当前 column item；
  extraItem?: any;

  parentNodeId?: string;
}

export const IcdeOperationInputSelector = (
  props: IcdeOperationInputSelectorProps,
) => {
  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateFromMaster',
  );

  const dmrTableContext = useDmrDragEditOnlyTableContext();

  const containerRef =
    dmrTableContext?.columnItemRefMapGetter?.(
      `${props?.dataIndex}~${props?.tableId}`,
    ) ?? React.createRef<any>();
  dmrTableContext?.columnItemRefMapSetter?.(
    `${props?.dataIndex}~${props?.tableId}`,
    containerRef,
  );

  const onValueChange = (currentValue) => {
    props?.onChangeExtra && props?.onChangeExtra(currentValue);

    props?.onChange && props?.onChange(currentValue);
  };

  const getPopupContainer = (trigger) => {
    return getSelectorDropdownContainerNode(props?.parentNodeId);
  };

  const onKeyDown = (event: any) => {
    if (props?.tableId && event?.hosted !== true && event?.ctrlKey === false) {
      if (event?.key === 'ArrowUp') {
        Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
          event: event,
          type: 'UP',
          trigger: 'selectkeydown',
        });
      }

      if (event?.key === 'ArrowDown') {
        Emitter.emit(getArrowUpDownEventKey(props?.tableId), {
          event: event,
          type: 'DOWN',
          trigger: 'selectkeydown',
        });
      }
    }
  };

  const dictModule =
    props?.extraItem?.conditionDictionaryKey ?? props?.conditionDictionaryKey;
  const dictModuleGroup =
    props?.extraItem?.conditionDictionaryGroup ??
    props?.conditionDictionaryGroup;

  const currentDataSources =
    props?.dataSource ??
    (dictModuleGroup
      ? globalState?.dictData?.[dictModuleGroup]?.[dictModule]
      : globalState?.dictData[dictModule]) ??
    [];

  const conditions = props?.dataSourceProcessor
    ? props?.dataSourceProcessor(currentDataSources || [])
    : currentDataSources || [];

  // console.log('IcdeOperationInputSelector conditions', conditions);

  return (
    <div className={'form-content-item-container'} style={{ width: '100%' }}>
      <UniDmrSelect
        containerRef={containerRef}
        className={props?.className}
        bordered={false}
        disabled={props?.disabled || false}
        formItemId={`formItem#${props?.dataIndex}#${props?.index}#Input${
          dictModule === 'Employee' ? '#Employee' : ''
        }${props?.tableId ? `#${props?.tableId}` : ''}`}
        dataSource={conditions}
        dropdownStyle={props?.dropdownStyle}
        listHeight={tableSelectorDropdownHeight ?? props?.listHeight}
        dropdownMatchSelectWidth={
          props?.dropdownMatchSelectWidth === undefined
            ? 200
            : props?.dropdownMatchSelectWidth
        }
        virtual={true}
        value={props?.value}
        optionNameKey={props?.optionNameKey}
        optionValueKey={props?.optionValueKey}
        optionTitleKey={props?.optionTitleKey}
        optionLabelProp={props?.optionLabelProp}
        nameFormat={props?.nameFormat}
        onChange={(value) => {
          if (isEmptyValues(value)) {
            onValueChange(value);
          }
        }}
        onSelect={onValueChange}
        getPopupContainer={getPopupContainer}
        leftOneAutoSelect={props?.leftOneAutoSelect ?? false}
        onKeyDown={onKeyDown}
        getTooltipPopupContainer={(triggerNode) => {
          return document?.querySelector(
            `#${props?.tableId} #tanstack-table-container tbody`,
          );
        }}
        numberSelectItem={props?.numberSelectItem ?? false}
        // 用于  表格内 自定义 展示
        labelFormat={props?.extraItem?.labelFormat ?? null}
      />
    </div>
  );
};

interface IcdeFieldInputProps extends FormTableItemBaseProps {
  recordId?: string;
  dataIndex: string;
  index: number;
  tableId: string;
}
export const IcdeFieldInput = (props: IcdeFieldInputProps) => {
  return (
    <Tooltip title={props?.value}>
      <div className={'form-content-item-container'}>
        <Input
          id={`formItem#${props?.dataIndex}#${props?.index}#Input#${props?.tableId}`}
          className={'icde-input icde-table-item'}
          bordered={false}
          value={props?.value ?? ''}
          onChange={(event) => {
            console.log('icde-input', event, event.target.value);
            props?.onChange && props?.onChange(event.target.value);
          }}
          contentEditable={true}
          // onChange={(event) => {
          //   setValue(event.target.value);
          // }}
        />
      </div>
    </Tooltip>
  );
};

interface IcuDurationInputProps extends FormTableItemBaseProps {
  recordId?: string;
  className?: string;
  dataIndex: string;
  index: number;
}

export const IcuDurationFieldInput = (props: IcuDurationInputProps) => {
  return (
    <div
      className={'form-content-item-container'}
      style={{ justifyContent: 'flex-start' }}
    >
      <InputNumber
        id={`formItem#${props?.dataIndex}#${props?.index}#IcuTable`}
        className={props?.className}
        bordered={false}
        value={props?.value ?? ''}
        min={1}
        precision={1}
        controls={false}
        keyboard={false}
        contentEditable={true}
        onChange={props?.onChange}
      />
    </div>
  );
};

interface PathologyIcdeFieldInputProps extends FormTableItemBaseProps {
  recordId: string;
  disabled?: boolean;
  dataIndex: string;
  index: number;
}
export const PathologyIcdeFieldInput = (
  props: PathologyIcdeFieldInputProps,
) => {
  const [modalVisible, setModalVisible] = useState(false);

  const handleDoubleClick = () => {
    // 只有当 dataIndex 是 TechDiagResult 时才打开 modal
    if (props.dataIndex === 'TechDiagResult') {
      setModalVisible(true);
    } else {
      console.log('doubleClick');
    }
  };

  const handleModalConfirm = (value: string) => {
    props?.onChange && props?.onChange(value);
    setModalVisible(false);
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };
  return (
    <div className={'form-content-item-container'}>
      <Input
        id={`formItem#${props?.dataIndex}#${props?.index}#Input#PathologyTable`}
        className={'icde-input icde-table-item'}
        style={props?.disabled === true ? { padding: '4px 0px' } : {}}
        bordered={false}
        readOnly={props?.disabled ?? false}
        value={props?.value ?? ''}
        onChange={(event) => {
          props?.onChange && props?.onChange(event?.target?.value);
        }}
        contentEditable={true}
        onClick={onReadonlyInputAndReadonlyItemClick}
        onDoubleClick={handleDoubleClick}
        // onChange={(event) => {
        //   setValue(event.target.value);
        // }}
      />
      {props?.dataIndex === 'TechDiagResult' && (
        <TechDiagResultModal
          visible={modalVisible}
          value={props?.value}
          onConfirm={handleModalConfirm}
          onCancel={handleModalCancel}
        />
      )}
    </div>
  );
};

interface IcdeOperationReadonlyItemProps {
  recordId?: string;
  dataIndex?: string;
  className?: string;
  style?: React.CSSProperties;
  value?: string;

  conditionDictionaryKey?: string;
  conditionDictionaryGroup?: string;
  extraItem?: any;
  type?: string;
}

// 单行省略 只对只读做特殊处理
const overflowStyle = {
  width: '100%',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
};

export const IcdeOperationReadonlyItem = (
  props: IcdeOperationReadonlyItemProps,
) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const dictModule =
    props?.extraItem?.conditionDictionaryKey ?? props?.conditionDictionaryKey;
  const dictModuleGroup =
    props?.extraItem?.conditionDictionaryGroup ??
    props?.conditionDictionaryGroup;

  const dictData = dictModuleGroup
    ? globalState?.dictData?.[dictModuleGroup]
    : globalState?.dictData;

  const label = dictModule
    ? dictData?.[dictModule]?.find(
        (item) => item?.Code?.toString() === props?.value?.toString(),
      )?.Name || '-'
    : props?.type === 'time'
    ? (() => {
        const date = dayjs(props?.value);
        return date.isValid() ? date.format('YYYY.MM.DD HH:mm:ss') : '-';
      })()
    : props?.value || '-';

  console.log('IcdeOperationReadonlyItem', props, dictData, label);

  return (
    <div className="icde-operation-readonly-item-container">
      <Tooltip title={label}>
        <span
          style={
            readonlyInputClickSelectAll
              ? {
                  ...overflowStyle,
                  ...(props?.style ?? {}),
                  userSelect: 'auto',
                }
              : { ...overflowStyle, ...(props?.style ?? {}) }
          }
          className={`icde-oper-readonly-item ${props?.className ?? ''}`}
          onClick={onReadonlyInputAndReadonlyItemClick}
        >
          {label}
        </span>
      </Tooltip>
    </div>
  );
};

export const operationExtraMap: { [key: string]: OperIcdeExtraMapItem } = {
  IsMicro: {
    display: '微',
    label: '微创手术',
    color: 'blue',
    isInsur: false,
  },
  InsurIsObsolete: {
    display: '灰',
    label: '置灰',
    color: '#eb5757',
    isInsur: true,
  },

  HqmsDegree: {
    display: '',
    label: '',
    color: '',
    isInsur: false,
  },

  DrgsDegree: {
    display: '',
    label: '',
    color: '',
    isInsur: false,
  },

  OperationCombo: {
    display: '手术组',
    label: '手术组',
    color: 'cyan',
    isInsur: false,
  },
};

// 分离表格的医保表格使用
export const InsurOnlyOperationExtraMap: {
  [key: string]: OperIcdeExtraMapItem;
} = {
  IsObsolete: {
    display: '灰',
    label: '置灰',
    color: '#eb5757',
    isInsur: true,
  },
};

export const hqmsDegreeMap = {
  '3': {
    color: 'red',
    display: '国三',
    label: '国三手术',
  },
  '4': {
    color: 'red',
    display: '国四',
    label: '国四手术',
  },
  '4-condition': {
    color: 'volcano',
    display: '条件四级',
    label: '条件四级',
  },
};

export const hqmsOperDetailTypeMap = {
  HqmsThirdOper: '3',
  HqmsFourthOper: '4',
};

export const drgsDegreeMap = {
  '1': {
    color: 'gold',
    display: '一',
    label: '一级手术',
  },
  '2': {
    color: 'gold',
    display: '二',
    label: '二级手术',
  },
  '3': {
    color: 'gold',
    display: '三',
    label: '三级手术',
  },
  '4': {
    color: 'gold',
    display: '四',
    label: '四级手术',
  },
};

interface OperationExtraTagsItemProps {
  inDropDown?: boolean;
  record?: any;
  value?: string[];
  eventName?: string;
  nameKey?: string;
  conditionDictionaryKey?: string;
  conditionDictionaryGroup?: string;
  form?: any;
  uniqueId?: string;
  extraMap?: { [key: string]: OperIcdeExtraMapItem };
}

export const OperationExtraTagsItem = (props: OperationExtraTagsItemProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [bundleCheckExtraOperDetail, setBundleCheckExtraOperDetail] =
    useState(null);

  useEffect(() => {
    if (!isEmptyValues(props?.uniqueId)) {
      Emitter.on(`BundleCheck-Extra-${props?.uniqueId}`, (data) => {
        setBundleCheckExtraOperDetail(data);
      });
    }

    return () => {
      if (!isEmptyValues(props?.uniqueId)) {
        Emitter.off(`BundleCheck-Extra-${props?.uniqueId}`);
      }
    };
  }, []);

  let value = Form.useWatch(
    [props?.record?.id, 'OperExtra'],
    props?.form,
  ) as any;
  let record = Form.useWatch(props?.record?.id, props?.form) as any;

  if (props?.inDropDown === true) {
    value = props?.value;
    record = props?.record;
  }

  const dictData = props?.conditionDictionaryGroup
    ? globalState?.dictData?.[props?.conditionDictionaryGroup]
    : globalState?.dictData;

  let valueKeys = value ? [...value] : [];

  // 如果传入了 extraMap，则使用 extraMap，否则使用默认的 operationExtraMap
  const targetMap = props?.extraMap || operationExtraMap;

  // 国考手术等级
  let hqmsDegree = record?.['HqmsDegree'];
  let recordData = true;
  if (!isEmptyValues(bundleCheckExtraOperDetail)) {
    if (!isEmptyValues(bundleCheckExtraOperDetail?.OperDetailType)) {
      hqmsDegree =
        hqmsOperDetailTypeMap[bundleCheckExtraOperDetail?.OperDetailType];
      recordData = false;
    }
  }

  if (hqmsDegree && hqmsDegreeMap[hqmsDegree]) {
    if (recordData === true) {
      if (hqmsDegree === '4' && !isEmptyValues(record?.['DegreeRemark'])) {
        hqmsDegree = '4-condition';
      }
    }

    targetMap['HqmsDegree']['display'] = hqmsDegreeMap[hqmsDegree]?.display;
    targetMap['HqmsDegree']['label'] =
      hqmsDegree === '4-condition'
        ? record?.['DegreeRemark']
        : hqmsDegreeMap[hqmsDegree]?.label;
    targetMap['HqmsDegree']['color'] = hqmsDegreeMap[hqmsDegree]?.color;
  } else {
    // 把degree剔除
    valueKeys = valueKeys?.filter((key) => key !== 'HqmsDegree');
  }

  // DRGS手术等级
  if (record?.['DrgsDegree'] && drgsDegreeMap[record?.['DrgsDegree']]) {
    // 使用 targetMap 而不是直接修改 operationExtraMap
    targetMap['DrgsDegree']['display'] =
      drgsDegreeMap[record?.['DrgsDegree']]?.display;
    targetMap['DrgsDegree']['label'] =
      drgsDegreeMap[record?.['DrgsDegree']]?.label;
    targetMap['DrgsDegree']['color'] =
      drgsDegreeMap[record?.['DrgsDegree']]?.color;
  } else {
    // 把degree剔除
    valueKeys = valueKeys?.filter((key) => key !== 'DrgsDegree');
  }

  // 过滤 valueKeys 数组，只保留存在于 targetMap 或 operationExtraMap 中的 key
  const filteredValueKeys =
    valueKeys?.filter((key) => {
      return targetMap[key];
    }) || [];

  return (
    <>
      {filteredValueKeys?.length > 0 ? (
        filteredValueKeys.map((key) => {
          const mapItem = targetMap[key];
          return (
            <Tooltip key={key} title={mapItem?.label}>
              <Tag
                style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
                color={mapItem?.color}
              >
                {mapItem?.display}
              </Tag>
            </Tooltip>
          );
        })
      ) : (
        <span>-</span>
      )}
    </>
  );
};

export const icdeExtraMap: { [key: string]: OperIcdeExtraMapItem } = {
  InsurIsObsolete: {
    display: '灰',
    label: '置灰',
    color: '#eb5757',
    isInsur: true,
  },
  IsCc: {
    display: 'CC',
    label: '并发症或合并症',
    color: '#ffc300',
    isInsur: true,
  },
  IsMcc: {
    display: 'MCC',
    label: '严重并发症或合并症',
    color: '#fb5607',
    isInsur: true,
  },
};

// 分离表格的医保表格使用
export const InsurOnlyIcdeExtraMap: { [key: string]: OperIcdeExtraMapItem } = {
  IsObsolete: {
    display: '灰',
    label: '置灰',
    color: '#eb5757',
    isInsur: true,
  },
  IsCc: {
    display: 'CC',
    label: '并发症或合并症',
    color: '#ffc300',
    isInsur: true,
  },
  IsMcc: {
    display: 'MCC',
    label: '严重并发症或合并症',
    color: '#fb5607',
    isInsur: true,
  },
};

interface IcdeExtraTagsItemProps {
  value?: string[];
  nameKey?: string;
  extraMap?: { [key: string]: OperIcdeExtraMapItem };
}

export const IcdeExtraTagsItem = (props: IcdeExtraTagsItemProps) => {
  // 如果传入了 extraMap，则使用 extraMap，否则使用默认的 icdeExtraMap
  const targetMap = props?.extraMap || icdeExtraMap;

  // 过滤 value 数组，只保留存在于 targetMap 或 icdeExtraMap 中的 key
  const filteredValue =
    props?.value && Array.isArray(props.value)
      ? props.value.filter((key) => targetMap[key])
      : [];

  return (
    <>
      {filteredValue?.length > 0 ? (
        filteredValue.map((key) => {
          // 优先使用 targetMap 中的配置，如果不存在则使用 icdeExtraMap 中的配置
          const mapItem = targetMap[key];
          return (
            <Tooltip title={mapItem?.label} key={key}>
              <Tag
                style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
                color={mapItem?.color}
              >
                {mapItem?.display}
              </Tag>
            </Tooltip>
          );
        })
      ) : (
        <span>-</span>
      )}
    </>
  );
};
