export const commonProps = {
  DateSelect: {
    style: {
      border: 'none',
      // width: 200,
      // maxWidth: 200,
    },
  },
  InputSuffix: {
    style: {
      border: 'none',
    },
  },
  Input: {
    bordered: false,
    onBlurValueChange: false,
    onPressEnterValueChange: false,
    contentEditable: true,
  },
  InputNumber: {
    bordered: false,
    onBlurValueChange: false,
    onPressEnterValueChange: false,
    controls: false,
    keyboard: false,
    contentEditable: true,
  },
  Select: {
    getPopupContainer: (trigger) => {
      return document.getElementById('dmr-content-container');
    },
  },
  ProvinceSeparateSelector: {
    style: {
      border: 'none',
    },
  },
  Table: {
    style: {
      border: 'none',
      // height: '100%',
    },
  },
  Null: {
    style: {
      border: 'none',
    },
  },
  IcdeDragTable: {
    style: {
      border: 'none',
      width: '100%',
      // height: '100%',
    },
  },
  IcdeMainTable: {
    style: {
      border: 'none',
      width: '100%',
      // height: '100%',
    },
  },
  IcdeInsurTable: {
    style: {
      border: 'none',
      width: '100%',
      // height: '100%',
    },
  },
  OperMainTable: {
    style: {
      border: 'none',
      width: '100%',
      // height: '100%',
    },
  },
  OperInsurTable: {
    style: {
      border: 'none',
      width: '100%',
      // height: '100%',
    },
  },
  OperationDragTable: {
    style: {
      border: 'none',
      width: '100%',
      // height: '100%',
    },
  },
  FeeChargeTable: {
    style: {
      border: 'none',
      width: '100%',
    },
  },
  BloodDragTable: {
    style: {
      border: 'none',
      width: '100%',
    },
  },
  IcuDragTable: {
    style: {
      border: 'none',
      width: '100%',
    },
  },
  TimeRangeStats: {
    style: {
      border: 'none',
    },
  },
  FeeItem: {
    style: {
      border: 'none',
    },
  },
  BabyAge: {
    style: {
      border: 'none',
    },
  },
};
