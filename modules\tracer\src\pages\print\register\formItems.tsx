import { ProFormSelect } from '@ant-design/pro-components';
import { Emitter } from '@uni/utils/src/emitter';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { Button, Col, Form, Image, Row, Select, Upload } from 'antd';
import { PRINT_EVENTS } from '../eventConstants';

export const PrintFormItems = (
  appRelationOpts,
  applicantIdCardTypeOpts,
  reasonOpts,
  printDocTypeOpts,
  cameraPics,
) => [
  {
    dataType: 'text',
    name: 'Applicant',
    title: '委托人',
    placeholder: '请输入委托人',
    colProps: { span: 8 },
  },
  {
    dataType: 'select',
    name: 'ApplicantRelation',
    title: '与患者关系',
    placeholder: '请选择与患者关系',
    fieldProps: {
      mode: 'single',
    },
    opts: appRelationOpts,
    colProps: { span: 8 },
  },
  // {
  //   dataType: 'FormGroup',
  //   colProps: { span: 8 },
  //   searchOpts: [
  //     {
  //       dataType: 'select',
  //       name: 'ApplicantIdCardType',
  //       title: '证件材料',
  //       placeholder: '请选择证件材料',
  //       fieldProps: {
  //         mode: 'single',
  //       },
  //       opts: applicantIdCardTypeOpts,
  //       colProps: { span: 16 },
  //     },

  //   ],
  // },
  {
    dataType: 'custom',
    // name: 'ApplicantIdCardType',
    // title: '证件材料',
    // placeholder: '请选择证件材料',
    // fieldProps: {
    //   mode: 'single',
    // },
    // opts: applicantIdCardTypeOpts,
    colProps: { span: 8 },
    render: (
      <Col span={8}>
        <Form.Item
          name="ApplicantIdCardType"
          label="证件材料"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
        >
          <Select
            placeholder="请选择证件材料"
            style={{ width: 'calc(100% - 95px)', marginRight: '7px' }}
            fieldNames={{ label: 'Name', value: 'Code' }}
            filterOption={(inputValue, option) => {
              return (
                (option &&
                  option.label
                    ?.toString()
                    ?.toLowerCase()
                    ?.indexOf(inputValue) !== -1) ||
                option.value?.toString()?.toLowerCase()?.indexOf(inputValue) !==
                  -1 ||
                pinyinInitialSearch(
                  option.label?.toString()?.toLowerCase(),
                  inputValue.toLowerCase(),
                )
              );
            }}
            options={applicantIdCardTypeOpts}
            onChange={(value) => {
              Emitter.emit(PRINT_EVENTS.APPLICANT_ID_CARD_TYPE_CHANGE, value);
            }}
          />
          <Button
            style={{ width: '88px' }}
            onClick={(e) => {
              (global?.window as any)?.eventEmitter?.emit(
                'CAMERA_STATUS_CHANGE',
                {
                  status: true,
                  onCameraOut: (pics) => {
                    console.log('CAMERA_STATUS_CHANGE Camera Out', pics);
                    // 这边的 在关闭的时候只会回传当前还在pic list里面的图的 data 复印登记需要记录
                    Emitter.emit(PRINT_EVENTS.CAMERA_OUT, pics);
                  },
                },
              );
            }}
          >
            证件拍照
          </Button>
        </Form.Item>
        {/* <ProFormSelect
          name="ApplicantIdCardType"
          label="证件材料"
          placeholder={'请选择证件材料'}
          showSearch
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 15 }}
          // options={item?.opts || []} width: 'calc(100% - 88px)'
          // 2.3.5的时候，如果opts为空，那么fieldProps必须为undefined
          fieldProps={{
            fieldNames: { label: 'Name', value: 'Code' },
            // 默认有
            filterOption: (inputValue, option) => {
              return (
                (option &&
                  option.label
                    ?.toString()
                    ?.toLowerCase()
                    ?.indexOf(inputValue) !== -1) ||
                option.value?.toString()?.toLowerCase()?.indexOf(inputValue) !==
                  -1 ||
                pinyinInitialSearch(
                  option.label?.toString()?.toLowerCase(),
                  inputValue.toLowerCase(),
                )
              );
            },
            // mode: 'single',

            // ...(props?.fieldProps ?? {}),
            options: applicantIdCardTypeOpts,
          }}
        /> */}
      </Col>
    ),
  },
  {
    dataType: 'text',
    name: 'ApplicantIdCard',
    title: '证件号码',
    placeholder: '请输入证件号码',
    colProps: { span: 8 },
    rules: [{ required: true, message: '请输入证件号码' }],
  },
  {
    dataType: 'text',
    name: 'ApplicantTel',
    title: '联系电话',
    placeholder: '请输入联系电话',
    colProps: { span: 8 },
    rules: [
      { required: true, message: '请输入联系电话' },
      {
        validator: (rule, value) => {
          const regExp = /^1[3-9]\d{9}$/;
          if (value && !regExp.test(value)) {
            return Promise.reject('请输入有效的中国大陆手机号码');
          }
          return Promise.resolve();
        },
      },
    ],
  },
  {
    dataType: 'custom',
    hidden: cameraPics?.length < 1,
    render: (
      <Col span={8}>
        <Row>
          <Col span={18} offset={6}>
            <Row style={{ width: '100%' }}>
              <Col xxl={6} md={8}>
                <Image
                  width={80}
                  preview={{ visible: false }}
                  src={cameraPics?.at(0)?.imgSrc}
                  onClick={() => Emitter.emit(PRINT_EVENTS.CAMERA_PICS_VIEW)}
                />
              </Col>
              {cameraPics?.length > 1 && (
                <Col xxl={6} md={8}>
                  <Image
                    width={80}
                    preview={{ visible: false }}
                    src={cameraPics?.at(1)?.imgSrc}
                    onClick={() => Emitter.emit(PRINT_EVENTS.CAMERA_PICS_VIEW)}
                  />
                </Col>
              )}
              {cameraPics?.length > 2 && (
                <Col xxl={6} md={8}>
                  <Button
                    type="dashed"
                    style={{ width: '88px', height: '45px', borderRadius: '0' }}
                    onClick={() => Emitter.emit(PRINT_EVENTS.CAMERA_PICS_VIEW)}
                  >
                    查看更多
                  </Button>
                </Col>
              )}
            </Row>
          </Col>
        </Row>
      </Col>
    ),
  },
  {
    dataType: 'text',
    name: 'ApplicantAddress',
    title: '联系地址',
    placeholder: '请输入联系地址',
    colProps: { span: 8 },
  },
  {
    dataType: 'number',
    name: 'CopyCnt',
    title: '复印份数',
    colProps: { span: 8 },
    placeholder: '请输入复印份数',
    fieldProps: {
      min: 1,
    },
    rules: [{ required: true, message: '请输入复印份数' }],
  },
  {
    dataType: 'select',
    name: 'Reason',
    title: '复印目的',
    fieldProps: {
      fieldNames: { label: 'Name', value: 'Code' },
    },
    opts: reasonOpts,
    // labelCol: { span: 3 },
    // wrapperCol: { span: 12 },
    colProps: { span: 8 },
    rules: [{ required: true }],
  },
  {
    dataType: 'checkboxGroup',
    name: 'PrintDocType',
    title: '复印内容',
    options: printDocTypeOpts?.map((d) => ({ label: d.Name, value: d.Code })),
    fieldProps: {
      className: 'print_doc_type',
    },
    labelCol: { span: 2 },
    wrapperCol: { span: 22 },
    colProps: { span: 24 },
    rules: [{ required: true }],
  },
];
