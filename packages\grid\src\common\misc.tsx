import { MenuProps } from 'antd';
import { PlusSquareTwoTone } from '@ant-design/icons';
import React from 'react';
import { isEmptyValues } from '@uni/utils/src/utils';
import omit from 'lodash/omit';

export const noDropdownComponents = [
  'FeeItem',
  'IcdeDragTable',
  'IcdeMainTable',
  'IcdeInsurTable',
  'OperMainTable',
  'OperInsurTable',
  'OperationDragTable',
  'IcuDragTable',
  'PathologyIcdeDragTable',
];

const items: any[] = [
  {
    icon: <PlusSquareTwoTone />,
    label: '添加批注',
    key: 'COMMENT_ADD',
    enable: (extra: any) => {
      return !isEmptyValues(extra?.detailCommentRef);
    },
  },
  {
    icon: <PlusSquareTwoTone />,
    label: '添加批注',
    key: 'PRE_COMMENT_ADD',
    enable: (extra: any) => {
      return !isEmptyValues(extra?.dmrPreCardCommentContainerRef);
    },
  },
];

export const commentAddProps = (props: any, extra: any) => {
  // TODO 费用？？？？ 表格？？？？？ 怎么来标识
  const menuItems =
    items
      ?.filter((item) => item?.enable(extra))
      ?.map((item) => {
        return omit(item, ['enable']);
      }) ?? [];
  return {
    key: `Dropdown-formItem#${props?.componentId}`,
    menu: {
      items: menuItems,
      onClick: (info: any) => {
        let data = {
          formKey: props?.formKey ?? props?.componentId,
          containerKey: props?.componentId,
          label: props?.data?.prefix,
        };

        // 直接唤起 首页批注 右侧 菜单
        if (isEmptyValues(extra?.detailCommentRef)) {
          extra?.gridContainerRef?.current?.showDmrPreComment?.();
        }

        setTimeout(() => {
          switch (info?.key) {
            case 'COMMENT_ADD':
              extra?.detailCommentRef?.current?.addComment(data);
              break;
            case 'PRE_COMMENT_ADD':
              extra?.dmrPreCardCommentContainerRef?.current?.addComment(data);
              break;
            default:
              break;
          }
        }, 200);
      },
    },
    trigger: ['contextMenu'],
    getPopupContainer: (triggerNode: any) => {
      return document.getElementById('dmr-content-container');
    },
    overlayStyle: {
      zIndex: 10050,
    },
  };
};
