import React, {
  forwardRef,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { Modal, Typography, Button, Space } from 'antd';
import { ExclamationCircleTwoTone } from '@ant-design/icons';

interface Conflict409ModalExpose {
  open: (opts: { errors?: string[]; onForce: () => Promise<void> }) => void;
}

interface Conflict409ModalState {
  open: boolean;
  errors: string[];
  onForce?: () => Promise<void>;
}

const Conflict409Modal = forwardRef<Conflict409ModalExpose>((props, ref) => {
  const [state, setState] = useState<Conflict409ModalState>({
    open: false,
    errors: [],
  });
  const [confirmLoading, setConfirmLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    open: ({ errors, onForce }) => {
      setState({ open: true, errors: errors ?? [], onForce });
    },
  }));

  const contentNodes = useMemo(() => {
    const lines = state.errors?.length
      ? state.errors
      : ['检测到数据冲突，请确认是否继续保存。'];
    return (
      <div
        style={{ maxHeight: 320, overflowY: 'auto', whiteSpace: 'pre-wrap' }}
      >
        {lines.map((line, idx) => (
          <Typography.Paragraph key={idx} style={{ marginBottom: 8 }}>
            {line}
          </Typography.Paragraph>
        ))}
        {/* <Typography.Paragraph type="secondary" style={{ marginTop: 8 }}>
          若选择“强制保存”，系统将使用当前最新时间覆盖本地的
          LastModificationTime 再次提交。
        </Typography.Paragraph> */}
      </div>
    );
  }, [state.errors]);

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleTwoTone twoToneColor="red" />
          <span>编辑出现冲突</span>
        </Space>
      }
      open={state.open}
      onCancel={() => setState((s) => ({ ...s, open: false }))}
      footer={[
        <Button
          key="cancel"
          onClick={() => setState((s) => ({ ...s, open: false }))}
        >
          取消
        </Button>,
        <Button
          key="force"
          type="primary"
          onClick={async () => {
            if (!state.onForce) {
              setState((s) => ({ ...s, open: false }));
              return;
            }
            try {
              // setConfirmLoading(true);

              // 直接关闭Modal 交给首页loading 应对可能存在的**多次冲突
              setState((s) => ({ ...s, open: false }));
              await state.onForce();
            } finally {
              setConfirmLoading(false);
            }
          }}
          loading={confirmLoading}
        >
          强制保存
        </Button>,
      ]}
      destroyOnClose
      mask={true}
      zIndex={10000}
    >
      {contentNodes}
    </Modal>
  );
});

export default Conflict409Modal;
