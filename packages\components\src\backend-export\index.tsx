import { exportExcel } from '@uni/utils/src/excel-export';
import { FileExcelOutlined } from '@ant-design/icons';
import { Button, Tooltip, TooltipProps, message } from 'antd';
import { exportExcelDictionaryModuleProcessor } from '../table/processor/data/export';
import _ from 'lodash';
import { commonBusinessDomain } from '@uni/services/src/commonService';
import { uniCommonService } from '@uni/services/src';
import {
  useDeepCompareEffect,
  useInterval,
  useRequest,
  useSafeState,
} from 'ahooks';
import { RespVO } from '@uni/commons/src/interfaces';
import { useEffect } from 'react';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import { ButtonType } from 'antd/lib/button';
import { EventConstant } from '@uni/utils/src/emitter';
import { isEmptyValues } from '@uni/utils/src/utils';

export interface IExportIconBtnProps {
  isBackend?: boolean;
  btnDisabled?: boolean;
  btnLoading?: boolean;
  btnText?: string;
  btnType?: ButtonType;
  backendObj?: {
    url?: string;
    method?: string;
    data?: any;
    fileName?: string;
    options?: any;
    customClkFunc?: Function;
  };
  frontendObj?: {
    columns: any[];
    dataSource: any[];
    fileName: string;
    options?: any;
    dictionaryData?: any;
    customExportFunc?: any;
  };
  existFileInfo?: any;
  origin?: 'REPORT' | 'OTHER';
  tooltipProps?: TooltipProps;

  getExternalExportConfig?: () => any;
}

const messageKey = 'exporting';

function removeFileExtension(fileName: string): string {
  return fileName.replace(/\.[^/.]+$/, '');
}

const ExportIconBtn = ({
  isBackend,
  btnDisabled,
  btnLoading,
  btnText,
  btnType,
  backendObj,
  frontendObj,
  tooltipProps,
  existFileInfo,
  origin,
  getExternalExportConfig,
}: IExportIconBtnProps) => {
  const [backendLoading, setBackendLoading] = useSafeState(false);
  // 新后端分页流程：
  // 1. 调url/exportxxx 告诉后端需要导出 获取对应的后端处理id
  // 2. 调Api​/Common​/ExportCenter​/GetExportRecord 获取处理进度 && 下载用id （轮询）
  // 3. 当2轮询100%时 调Api/Common/Blob/Download 下载

  // 前端分页流程不变

  const {
    data: exportStartData,
    loading: exportStartLoading,
    run: exportStart,
  } = useRequest(
    (backendObj) => {
      setBackendLoading(true);
      return uniCommonService(backendObj.url, {
        method: backendObj.method ?? 'POST',
        data: {
          // 默认
          DtParam: {
            Draw: 1,
            Start: 0,
            Length: 2147483647,
          },
          SkipCount: 0,
          MaxResultCount: 2147483647,
          ...backendObj.data,
        },
      });
    },
    {
      manual: true,
      //   formatResult: (res: RespVO<any>) => res,
      onSuccess(res, params) {
        console.log(res);
        if (res.response?.status === 202) {
          exportCheck(res.data.Id, params[0]?.fileName);
          message.loading({
            content: '正在导出...',
            key: messageKey,
            duration: 0,
          });
        } else {
          if (res?.code === 1) {
            setBackendLoading(false);
          }
        }
      },
    },
  );

  // 轮询
  const {
    data: exportRecord,
    loading: exportCheckLoading,
    run: exportCheck,
    cancel: cancelExportCheck,
  } = useRequest(
    (id, fileName) => {
      return uniCommonService('Api/Common/ExportCenter/GetExportRecord', {
        method: 'POST',
        params: { Id: id },
      });
    },
    {
      manual: true,
      pollingInterval: 1000,
      pollingWhenHidden: true,
      onSuccess(res, params) {
        if (res.data?.Status === '100') {
          cancelExportCheck();
          setBackendLoading(false);
          message.destroy(messageKey);
          message.success('导出成功');
          // 默认使用后端传的name 要确保那些.xlsx等后缀不被带上
          const fileName = res.data?.FileName ?? params[1];
          const sanitizedFileName = removeFileExtension(fileName);

          let a = document.createElement('a');
          a.target = '_blank';
          a.href = `${commonBusinessDomain}/Api/Common/Blob/Download?Id=${res.data.BlobId}&fileName=${sanitizedFileName}`;
          a.click();
          // 通知上游 文件ID
          if (origin === 'REPORT') {
            (global?.window as any)?.eventEmitter?.emit(
              'REPORT_FILE_ID',
              res.data,
            );
          }
        } else {
        }
      },
    },
  );

  return (
    <>
      {(backendObj || frontendObj || getExternalExportConfig) && (
        <Tooltip title="导出Excel" {...tooltipProps}>
          <Button
            type={btnType ?? 'text'}
            shape={btnText ? 'default' : 'circle'}
            icon={<FileExcelOutlined />}
            disabled={btnDisabled} //
            loading={btnLoading || backendLoading || false}
            onClick={() => {
              let isBackendExport = isBackend;
              let backendConfig = backendObj;
              let frontendConfig = frontendObj;

              if (getExternalExportConfig) {
                let externalExportConfig = getExternalExportConfig();

                if (isEmptyValues(externalExportConfig)) {
                  return;
                }

                isBackendExport = externalExportConfig?.isBackend;
                backendConfig = externalExportConfig?.backendObj;
                frontendConfig = externalExportConfig?.frontendObj;
              }

              if (isBackendExport) {
                // 后端分页
                if (backendConfig?.customClkFunc) {
                  backendConfig?.customClkFunc();
                } else if (backendConfig.url) {
                  if (existFileInfo) {
                    // 当存在 FileId的时候直接下载
                    message.destroy(messageKey);
                    message.success('导出成功');
                    // console.log(backendConfig, res.data?.FileName);
                    let a = document.createElement('a');
                    a.target = '_blank';
                    a.href = `${commonBusinessDomain}/Api/Common/Blob/Download?Id=${
                      existFileInfo?.BlobId
                    }&fileName=${
                      backendConfig?.fileName ?? existFileInfo?.FileName
                    }`;
                    a.click();
                  } else {
                    exportStart(backendConfig);
                  }
                }
              } else {
                // 前端分页
                try {
                  console.log('frontendConfig', frontendConfig);
                  if (frontendConfig?.customExportFunc) {
                    frontendConfig?.customExportFunc(
                      frontendConfig.columns.slice() as any[],
                      exportExcelDictionaryModuleProcessor(
                        frontendConfig.columns,
                        _.cloneDeep(frontendConfig.dataSource.slice()),
                        frontendConfig?.dictionaryData ?? {},
                      ),
                      frontendConfig.fileName,
                      frontendConfig?.options,
                    );
                  } else {
                    exportExcel(
                      frontendConfig.columns.slice() as any[],
                      exportExcelDictionaryModuleProcessor(
                        frontendConfig.columns,
                        _.cloneDeep(frontendConfig.dataSource.slice()),
                        frontendConfig?.dictionaryData ?? {},
                      ),
                      frontendConfig.fileName,
                    );
                  }

                  message.success('导出成功');
                } catch (error) {
                  message.error(error);
                }
              }
            }}
          >
            {btnText}
          </Button>
        </Tooltip>
      )}
    </>
  );
};

export default ExportIconBtn;
