import { PropertyItem } from '@/pages/configuration/interfaces';
import {
  icdeColumns,
  icuColumns,
  operationColumns,
  pathologyIcdeColumns,
  tcmIcdeColumns,
} from '@/pages/dmr/columns';
import { icdeInsurColumns } from '@/pages/dmr/components/icde-insur-separate-table/columns/icdeInsurColumns';
import { icdeMainColumnsBase } from '@/pages/dmr/components/icde-insur-separate-table/columns/icdeMainColumns';
import { operInsurColumns } from '@/pages/dmr/components/oper-insur-separate-table/columns/operInsurColumns';
import { operationMainColumnsBase } from '@/pages/dmr/components/oper-insur-separate-table/columns/operMainColumns';
import { departmentTransferColumns } from '@uni/grid/src/common/columns';

export const tableBasicProperties: PropertyItem[] = [
  {
    key: 'data.props.id',
    label: '表格id',
    component: 'Input',
    required: true,
    fieldProps: {
      disabled: true,
    },
  },
  {
    key: 'data.props.tableId',
    label: '表格id',
    component: 'Select',
    hidden: true,
    fieldProps: {},
  },
  {
    key: 'data.props.parentId',
    label: '上层ID',
    component: 'Input',
    hidden: true,
    fieldProps: {},
  },
];

export const IcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: icdeColumns,
      },
    },
  ],
];

export const IcdeMainTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: icdeMainColumnsBase,
      },
    },
  ],
];

export const IcdeInsurTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: icdeInsurColumns,
      },
    },
  ],
];

export const OperationDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: operationColumns,
      },
    },
  ],
];

export const OperMainTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: operationMainColumnsBase,
      },
    },
  ],
];

export const OperInsurTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: operInsurColumns,
      },
    },
  ],
];

export const IcuDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: icuColumns,
      },
    },
  ],
];

export const PathologyIcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: pathologyIcdeColumns,
      },
    },
  ],
];

export const TcmIcdeDragTableProperties: PropertyItem[][] = [
  tableBasicProperties,
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: tcmIcdeColumns,
      },
    },
  ],
];

export const DepartmentTransferTableProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.columns',
      label: '表格列',
      description: '表格中列相关配置',
      component: 'DragTable',
      required: true,
      fieldProps: {
        defaultColumns: departmentTransferColumns(false),
      },
    },
  ],
];
