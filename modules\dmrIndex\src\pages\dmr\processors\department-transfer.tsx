import cloneDeep from 'lodash/cloneDeep';
import {
  filterIdAddAndAllCellEmptyRow,
  icdeOperationTableNoDataAddOne,
} from '@/pages/dmr/processors/processors';
import { generateUniqueNumberId } from '@uni/utils/src/utils';

export const departmentTransferTableResponseProcessor = (
  formFieldValue,
  departmentTransfers: any,
) => {
  departmentTransfers?.forEach((item) => {
    item['InDeptHours'] = item['InDeptHours'] / 24;
    if (item?.Id) {
      item['id'] = item?.Id;
    } else {
      item['id'] = generateUniqueNumberId();
    }
  });

  formFieldValue['department-transfer-table'] = departmentTransfers?.sort(
    (a, b) => (a?.TransferSort ?? 0) - (b?.TransferSort ?? 0),
  );

  // 为了form item的刷新
  formFieldValue['departmentTransferTable'] = cloneDeep(
    formFieldValue['department-transfer-table']?.slice(),
  );
};

export const departmentTransferTableRequestParamProcessor = (
  formFieldValues,
  data,
  ignoreNull,
) => {
  // 小时转换
  let transDept = cloneDeep(formFieldValues?.['department-transfer-table']);

  transDept?.slice()?.forEach((item, index) => {
    item['TransferSort'] = index + 1;
    item['InDeptHours'] = parseInt(item['InDeptHours'] ?? 0) * 24;
  });

  data['CardTransfers'] = transDept || [];

  if (ignoreNull) {
    data['CardTransfers'] = data['CardTransfers']?.filter((item) => {
      return item['OutCliDept'] && item['InCliDept'];
    });
  }
};
