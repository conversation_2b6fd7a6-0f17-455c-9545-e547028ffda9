@import '~@uni/commons/src/style/variables.less';

.prompt_container {
  .ant-card-body {
    padding: 8px !important;
  }
}
.red-color {
  color: @red-color;
}
.yellow-color {
  color: #ffc300;
}
.orange-color {
  color: #fa8c16;
}

.warning_stats {
  .ant-statistic-content-prefix {
    float: none !important;
  }
  .ant-statistic-content {
    height: 100px;
    margin: 0;
    padding: 30px 0;
  }
}

.warning_chart_stats {
  .ant-statistic-content-prefix {
    display: block;
    float: none !important;
    width: 100%;
  }
  .ant-statistic-content-value {
    display: none !important;
  }
}

.stats_card_container {
  background-color: #f9f9f9;
  margin: 10px 0;
  .ant-card-body {
    padding: 12px;
  }
}
