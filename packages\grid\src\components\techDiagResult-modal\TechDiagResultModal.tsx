import React, { useState } from 'react';
import { Modal, Input, Button, Space, message } from 'antd';

const { TextArea } = Input;

interface TechDiagResultModalProps {
  visible: boolean;
  value?: string;
  onConfirm: (value: string) => void;
  onCancel: () => void;
}

export const TechDiagResultModal: React.FC<TechDiagResultModalProps> = ({
  visible,
  value = '',
  onConfirm,
  onCancel,
}) => {
  const [textValue, setTextValue] = useState(value);

  // 当 modal 打开时重置文本值
  React.useEffect(() => {
    if (visible) {
      setTextValue(value);
    }
  }, [visible, value]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(textValue);
      message.success('复制成功');
    } catch (error) {
      // 如果 clipboard API 不可用，使用传统方法
      const textArea = document.createElement('textarea');
      textArea.value = textValue;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success('复制成功');
    }
  };

  const handlePaste = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      setTextValue(clipboardText);
      message.success('粘贴成功');
    } catch (error) {
      message.error('粘贴失败，请手动粘贴或检查浏览器权限');
    }
  };

  const handleConfirm = () => {
    onConfirm(textValue);
  };

  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title="医技诊断结果"
      open={visible}
      onCancel={handleCancel}
      width={600}
      maskClosable={false}
      mask={false}
      footer={null}
      style={{ top: 100 }}
    >
      <div style={{ marginBottom: 16 }}>
        <TextArea
          value={textValue}
          onChange={(e) => setTextValue(e.target.value)}
          rows={8}
          placeholder="请输入技术诊断结果"
          style={{ resize: 'vertical' }}
        />
      </div>

      <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
        <Button onClick={handleCopy} disabled={!textValue}>
          复制
        </Button>
        <Button onClick={handlePaste}>粘贴</Button>
        <Button onClick={handleCancel}>取消</Button>
        <Button type="primary" onClick={handleConfirm}>
          确认
        </Button>
      </Space>
    </Modal>
  );
};

export default TechDiagResultModal;
