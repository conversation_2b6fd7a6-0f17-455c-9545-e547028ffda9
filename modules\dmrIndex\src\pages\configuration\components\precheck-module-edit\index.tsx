import {
  Divider,
  Form,
  Input,
  message,
  Modal,
  Row,
  Spin,
  Switch,
  Tag,
} from 'antd';
import React, { useEffect, useRef } from 'react';
import {
  DmrConfigurationConstants,
  <PERSON>ag<PERSON><PERSON><PERSON>,
} from '@/pages/configuration/constants';
import { Emitter } from '@uni/utils/src/emitter';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import './index.less';
import merge from 'lodash/merge';
import {
  ProForm,
  ProFormDependency,
  ProFormSwitch,
  ProFormText,
} from '@uni/components/src/pro-form';
import {
  chsDrgDipProps,
  chsSettleResultProps,
  drgProps,
} from '@/pages/dmr/components/right-container';

const defaultPreCheckModuleConfig = {
  performancePreGroup: true,
  chsPreGroup: true,
  chsPreAdjustGroup: true,
  chsFeeTypeFeesTable: true,
  chsSettleResult: true,

  cardReview: true,
  cardReviewDmrTrouble: true,
  cardReviewCodeTrouble: true,

  ...drgProps.reduce((a, v) => ({ ...a, [`DRG-${v?.key}`]: true }), {}),
  ...chsDrgDipProps['Drg'].reduce(
    (a, v) => ({ ...a, [`CHS-${v?.key}`]: true }),
    {},
  ),
  ...chsSettleResultProps.reduce(
    (a, v) => ({ ...a, [`CHS-SETTLE-${v?.key}`]: true }),
    {},
  ),
};

const PreCheckModuleEdit = () => {
  const [preCheckModuleEditOpen, setPreCheckModuleEditOpen] =
    React.useState<any>(false);

  const [form] = Form.useForm();

  useEffect(() => {
    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_PRE_CHECK_MODULE_EDIT,
      (status) => {
        setPreCheckModuleEditOpen(true);
      },
    );

    return () => {
      Emitter.off(
        DmrConfigurationConstants.DMR_CONFIGURATION_PRE_CHECK_MODULE_EDIT,
      );
    };
  }, []);

  useEffect(() => {
    if (preCheckModuleEditOpen) {
      dmrPreCheckModuleConfigReq();
    }
  }, [preCheckModuleEditOpen]);

  const {
    loading: dmrPreCheckModuleConfigLoading,
    run: dmrPreCheckModuleConfigReq,
  } = useRequest(
    () => {
      let data = {
        IdentityCode: 'Global',
        IdentityType: 'Global',
        configModules: ['DmrPreCheckModules'],
      };
      return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        let savedValues = response.data?.DmrPreCheckModules;
        form.setFieldsValue(
          merge({}, defaultPreCheckModuleConfig, savedValues ?? {}),
        );
      },
    },
  );

  const {
    loading: dmrPreCheckModuleSaveLoading,
    run: dmrPreCheckModuleSaveReq,
  } = useRequest(
    (values) => {
      let data = {
        IdentityCode: 'Global',
        IdentityType: 'Global',
        ConfigModule: 'DmrPreCheckModules',
        FullReplace: true,
        values: values,
      };
      return uniCommonService('Api/Sys/ClientKitSys/SetValue', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('保存成功');
        }
        return response;
      },
    },
  );

  return (
    <Modal
      title="预分组模块修改"
      open={preCheckModuleEditOpen}
      okText={'确定'}
      cancelText={'取消'}
      wrapClassName={'pre-check-module-edit-container'}
      onOk={async (event) => {
        dmrPreCheckModuleSaveReq(form.getFieldsValue(true));
        setPreCheckModuleEditOpen(false);
      }}
      onCancel={(event) => {
        setPreCheckModuleEditOpen(false);
      }}
      destroyOnClose={true}
      confirmLoading={dmrPreCheckModuleSaveLoading}
      getContainer={document.getElementById('dmr-configuration-wrapper')}
    >
      <Spin spinning={dmrPreCheckModuleConfigLoading}>
        <ProForm
          form={form}
          name="dmrPreCheckModule"
          wrapperCol={{ span: 16 }}
          autoComplete="off"
          submitter={false}
          grid={true}
        >
          <ProFormSwitch
            name="performancePreGroup"
            label="显示绩效预分组"
            fieldProps={{
              onChange: (checked: boolean) => {
                if (checked === false) {
                  form.setFieldsValue({
                    ...drgProps.reduce(
                      (a, v) => ({ ...a, [`DRG-${v?.key}`]: false }),
                      {},
                    ),
                  });
                }
              },
            }}
          />

          <ProFormDependency name={['performancePreGroup']}>
            {(record) => {
              if (record['performancePreGroup']?.toString() === 'true') {
                return (
                  <ProForm.Group
                    title={'绩效预分组单项'}
                    direction={'horizontal'}
                  >
                    {drgProps?.map((item) => {
                      return (
                        <ProFormSwitch
                          name={`DRG-${item?.key}`}
                          label={item?.title}
                          colProps={{
                            span: 6,
                          }}
                        />
                      );
                    })}
                  </ProForm.Group>
                );
              }
              return <></>;
            }}
          </ProFormDependency>

          <Divider className={'separator'} />

          <ProForm.Group direction={'horizontal'}>
            <ProFormSwitch
              label="显示医保预分组"
              name="chsPreGroup"
              colProps={{
                span: 7,
              }}
              fieldProps={{
                onChange: (checked: boolean) => {
                  if (checked === false) {
                    form.setFieldsValue({
                      chsPreAdjustGroup: false,
                      chsFeeTypeFeesTable: false,
                      ...chsDrgDipProps['Drg'].reduce(
                        (a, v) => ({ ...a, [`CHS-${v?.key}`]: false }),
                        {},
                      ),
                    });
                  }
                },
              }}
            />

            <ProFormDependency name={['chsPreGroup']}>
              {(record) => {
                if (record['chsPreGroup']?.toString() === 'true') {
                  return (
                    <>
                      <ProFormSwitch
                        label="显示医保可调整组"
                        name="chsPreAdjustGroup"
                        colProps={{
                          span: 7,
                        }}
                      />
                      <ProFormSwitch
                        label="显示分项费用标杆"
                        name="chsFeeTypeFeesTable"
                        colProps={{
                          span: 7,
                        }}
                      />
                    </>
                  );
                }
                return <></>;
              }}
            </ProFormDependency>
          </ProForm.Group>

          <ProFormDependency name={['chsPreGroup']}>
            {(record) => {
              if (record['chsPreGroup']?.toString() === 'true') {
                return (
                  <>
                    <ProForm.Group
                      title={'医保预分组单项'}
                      direction={'horizontal'}
                    >
                      {chsDrgDipProps['Drg']?.map((item) => {
                        return (
                          <ProFormSwitch
                            name={`CHS-${item?.key}`}
                            label={item?.title}
                            colProps={{
                              span: 6,
                            }}
                          />
                        );
                      })}
                    </ProForm.Group>
                  </>
                );
              }
              return <></>;
            }}
          </ProFormDependency>

          <Divider className={'separator'} />

          <ProFormSwitch
            label="医保反馈结果"
            name="chsSettleResult"
            colProps={{
              span: 6,
            }}
            fieldProps={{
              onChange: (checked: boolean) => {
                if (checked === false) {
                  form.setFieldsValue({
                    chsSettleResult: false,
                    ...chsSettleResultProps.reduce(
                      (a, v) => ({ ...a, [`CHS-SETTLE-${v?.key}`]: false }),
                      {},
                    ),
                  });
                }
              },
            }}
          />

          <ProFormDependency name={['chsSettleResult']}>
            {(record) => {
              if (record['chsSettleResult']?.toString() === 'true') {
                return (
                  <>
                    <ProForm.Group
                      title={'医保反馈结果单项'}
                      direction={'horizontal'}
                    >
                      {chsSettleResultProps?.map((item) => {
                        return (
                          <ProFormSwitch
                            name={`CHS-SETTLE-${item?.key}`}
                            label={item?.title}
                            colProps={{
                              span: 6,
                            }}
                          />
                        );
                      })}
                    </ProForm.Group>
                  </>
                );
              }
              return <></>;
            }}
          </ProFormDependency>

          <Divider className={'separator'} />

          <ProForm.Group direction={'horizontal'}>
            <ProFormSwitch
              label="显示质控审核"
              name="cardReview"
              colProps={{
                span: 6,
              }}
              fieldProps={{
                onChange: (checked: boolean) => {
                  if (checked === false) {
                    form.setFieldsValue({
                      cardReviewDmrTrouble: false,
                      cardReviewCodeTrouble: false,
                    });
                  }
                },
              }}
            />
            <ProFormDependency name={['cardReview']}>
              {(record) => {
                if (record['cardReview']?.toString() === 'true') {
                  return (
                    <>
                      <ProFormSwitch
                        label="显示首页问题"
                        name="cardReviewDmrTrouble"
                        colProps={{
                          span: 6,
                        }}
                      />
                      <ProFormSwitch
                        label="显示编码问题"
                        name="cardReviewCodeTrouble"
                        colProps={{
                          span: 6,
                        }}
                      />
                    </>
                  );
                }
                return <></>;
              }}
            </ProFormDependency>
          </ProForm.Group>
        </ProForm>
      </Spin>
    </Modal>
  );
};

export default PreCheckModuleEdit;
