import { getOperInfoWithStrictMode } from '@/pages/dmr/network/get';
import {
  operationExtraMap,
  InsurOnlyOperationExtraMap,
} from '@uni/grid/src/components/icde-oper-input/input';
import {
  filterIdAddAndAllCellEmptyRow,
  icdeOperationTableNoDataAddOne,
} from '@/pages/dmr/processors/processors';
import { IcdeOperResp } from '@/pages/dmr/network/interfaces';
import isNil from 'lodash/isNil';
import { v4 as uuidv4 } from 'uuid';
import cloneDeep from 'lodash/cloneDeep';
import { generateUniqueNumberId } from '@uni/utils/src/utils';

export const operationTableResponseProcessor = async (
  formFieldValue,
  operationItems: any[],
  insuranceOperationItems: any[],
  hqmsOperationItems: any[],
  operMetaData?: IcdeOperResp,
) => {
  let operTableData = [];

  if (operationItems?.length > 0) {
    let operInfosWithExtra =
      operMetaData ??
      (
        await getOperInfoWithStrictMode(
          operationItems?.map((item) => item?.OperCode),
        )
      )?.data;

    for (let item of operationItems?.slice()) {
      item['id'] = item['OperId'] ?? item['Id'];

      let operInfoWithExtra = operInfosWithExtra?.Data?.find(
        (itemWithExtra) => itemWithExtra?.Code === item?.OperCode,
      );

      if (operInfoWithExtra) {
        // 合并一份出来
        item = {
          ...operInfoWithExtra,
          ...item,
        };
      }

      // 医保
      let insuranceOperItem = insuranceOperationItems.find(
        (insuranceItem) => insuranceItem?.UniqueId === item.UniqueId,
      );
      if (insuranceOperItem) {
        item['InsurCode'] = insuranceOperItem?.OperCode;
        item['InsurName'] = insuranceOperItem?.OperName;
        item['IsMain'] = insuranceOperItem?.IsMain;
        item['IsReported'] =
          insuranceOperItem?.IsMain == true
            ? true
            : insuranceOperItem?.IsReported;

        // 现在就一个这个有用
        item['InsurIsObsolete'] =
          insuranceOperItem?.insuranceMetaData?.IsObsolete;
      }

      // hqms
      let hqmsOperItem = hqmsOperationItems.find(
        (hqmsItem) => hqmsItem?.UniqueId === item.UniqueId,
      );
      if (hqmsOperItem) {
        item['HqmsCode'] = hqmsOperItem?.OperCode;
        item['HqmsName'] = hqmsOperItem?.OperName;
      }

      // OperExtra 处理一下
      item['OperExtra'] = Object.keys(operationExtraMap)?.filter(
        (key) => item?.[key] ?? false,
      );

      operTableData.push(item);
    }
  }

  // TODO 是否存在 愈合 等级 拆分

  formFieldValue['operation-table'] = operTableData?.sort(
    (a, b) => (a?.OperSort ?? 1) - (b?.OperSort ?? 1),
  );

  formFieldValue['operation-table'] = icdeOperationTableNoDataAddOne(
    formFieldValue['operation-table'],
  );

  // 为了form item的刷新
  formFieldValue['operationTable'] = cloneDeep(
    formFieldValue['operation-table']?.slice(),
  );
};

export const operationTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  let operationTableData = (formFieldValues?.['operation-table'] || [])
    ?.slice()
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item));

  if (operationTableData?.length === 0) {
    data['CardOpers'] = [];
  }

  // 手术
  if (operationTableData?.length > 0) {
    data['CardOpers'] = operationTableData
      ?.map((item, index) => {
        item['OperSort'] = index + 1;

        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        return item;
      })
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['OperCode']);
        } else {
          return true;
        }
      });
  }
};

// 分离表格模式的响应处理器（处理Get返回的数据）
export const operationInsurSeparateTableResponseProcessor = async (
  formFieldValue: any,
  operationItems: any[],
  insuranceOperationItems: any[],
  hqmsOperationItems: any[],
  operMetaData?: IcdeOperResp,
) => {
  // 直接基于 operationItems/hqmsOperationItems 构建 MainTable，基于 insuranceOperationItems 构建 InsurTable
  const mainTableData: any[] = [];

  // 预取 operMetaData，提供扩展字段
  let operInfosWithExtra =
    operMetaData ??
    (await getOperInfoWithStrictMode(operationItems?.map((d) => d?.OperCode)))
      ?.data;

  // 构建 MainTable
  if (operationItems?.length > 0) {
    for (let item of operationItems) {
      const copy: any = { ...item };
      copy['id'] = copy['OperId'] ?? copy['Id'];

      // 合并扩展信息
      const operInfoWithExtra = operInfosWithExtra?.Data?.find(
        (e) => e?.Code === copy?.OperCode,
      );
      if (operInfoWithExtra) {
        Object.assign(copy, operInfoWithExtra);
      }

      // hqms 合并
      const hqmsOperItem = hqmsOperationItems?.find(
        (hq) => hq?.UniqueId === copy?.UniqueId,
      );
      if (hqmsOperItem) {
        copy['HqmsCode'] = hqmsOperItem?.OperCode;
        copy['HqmsName'] = hqmsOperItem?.OperName;
      }

      // 计算 OperExtra 标签
      copy['OperExtra'] = Object.keys(operationExtraMap)?.filter(
        (key) => copy?.[key] ?? false,
      );

      // 不在主表填 Insur 字段
      delete copy['InsurCode'];
      delete copy['InsurName'];

      mainTableData.push(copy);
    }
  }

  const sortedMain = mainTableData?.sort(
    (a, b) => (a?.OperSort ?? 1) - (b?.OperSort ?? 1),
  );

  // 构建 InsurTable：来源 insuranceOperationItems
  const mainMap = new Map<string, any>();
  sortedMain?.forEach((m) => {
    if (m?.UniqueId) mainMap.set(m.UniqueId, m);
  });

  const insurTableData: any[] = (insuranceOperationItems || []).map((ins) => {
    const obj: any = { ...ins };
    obj['id'] = ins?.OperId ?? ins?.Id ?? generateUniqueNumberId();
    obj['UniqueId'] = ins?.UniqueId;
    // 医保
    obj['InsurCode'] = ins?.OperCode;
    obj['InsurName'] = ins?.OperName;
    // 首页（若能匹配到）
    const mainItem = ins?.UniqueId ? mainMap.get(ins.UniqueId) : undefined;
    if (mainItem) {
      obj['OperCode'] = mainItem?.OperCode;
      obj['OperName'] = mainItem?.OperName;
    }
    // 合并扩展信息
    const operInfoWithExtra = operInfosWithExtra?.Data?.find(
      (e) => e?.InsurCode === obj?.InsurCode,
    );
    if (operInfoWithExtra) {
      Object.assign(obj, operInfoWithExtra);
    }
    // 计算 OperExtra 标签
    // 计算有问题，因为
    obj['OperExtra'] = Object.keys(InsurOnlyOperationExtraMap)?.filter(
      (key) => obj?.[key] ?? false,
    );
    console.log('operInfoWithExtra', operInfoWithExtra);

    return obj;
  });

  // 设置分离后的表格数据
  formFieldValue['operation-main-table'] = sortedMain;
  // formFieldValue['operation-main-table'] = icdeOperationTableNoDataAddOne(
  //   formFieldValue['operation-main-table'],
  // );
  formFieldValue['operationMainTable'] = cloneDeep(sortedMain?.slice());

  formFieldValue['operation-insur-table'] = insurTableData;
  // formFieldValue['operation-insur-table'] = icdeOperationTableNoDataAddOne(
  //   formFieldValue['operation-insur-table'],
  // );
  formFieldValue['operationInsurTable'] = cloneDeep(insurTableData?.slice());
};

// 分离表格模式的Submit/Check请求参数处理器（MainTable）
export const operationInsurSeparateTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // 取出两侧表格数据（过滤掉 ADD 和空行）
  const mainTableData: any[] = (formFieldValues?.['operation-main-table'] || [])
    ?.slice()
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item));

  const insurTableData: any[] = (
    formFieldValues?.['operation-insur-table'] || []
  )
    ?.slice()
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item));

  // 1) 双向匹配：按 UniqueId 进行关联，仅当两边都存在相同 UniqueId 才进行合并
  const insurSelectedMap = new Map<string, any>();
  for (const insurItem of insurTableData) {
    if (insurItem?.UniqueId) {
      insurSelectedMap.set(insurItem.UniqueId, insurItem);
    }
  }

  // 2) 生成 CardOpers（主表），OperSort 按照原有处理器的方式（过滤后顺序设定 + 1）
  if (mainTableData?.length === 0) {
    data['CardOpers'] = [];
    return;
  }

  const mergedMainItems = mainTableData
    ?.map((mainItem, index) => {
      // 合并医保侧（仅当存在且被选中时）
      const matchedInsur = mainItem?.UniqueId
        ? insurSelectedMap.get(mainItem.UniqueId)
        : undefined;

      const result: any = { ...mainItem };
      result['OperSort'] = index + 1;

      if (isNil(result['UniqueId'])) {
        result['UniqueId'] = uuidv4();
      }

      if (matchedInsur) {
        result['InsurCode'] = matchedInsur?.InsurCode;
        result['InsurName'] = matchedInsur?.InsurName;
      }

      if (typeof result['id'] === 'string' || result['id'] instanceof String) {
        delete result['id'];
      }

      return result;
    })
    ?.filter((item) => {
      if (ignoreNull) {
        return !isNil(item['OperCode']);
      }
      return true;
    });

  data['CardOpers'] = mergedMainItems ?? [];
};
