// TODO: 4.0 - codemod should help to change `filterOption` to support node props.

import classNames from 'classnames';
import type { SelectProps as RcSelectProps } from '@uni/rc-select/lib';
import RcSelect, { BaseSelectRef, OptGroup, Option } from '@uni/rc-select/lib';
import { OptionProps } from '@uni/rc-select/lib/Option';
import type {
  BaseOptionType,
  DefaultOptionType,
} from '@uni/rc-select/lib/Select';
import omit from 'rc-util/lib/omit';
import React from 'react';
import { useContext } from 'react';
import { ConfigContext } from 'antd/lib/config-provider';
import defaultRenderEmpty from 'antd/lib/config-provider/defaultRenderEmpty';
import DisabledContext from 'antd/lib/config-provider/DisabledContext';
import type { SizeType } from 'antd/lib/config-provider/SizeContext';
import SizeContext from 'antd/lib/config-provider/SizeContext';
import { FormItemInputContext } from 'antd/lib/form/context';
import type { SelectCommonPlacement } from 'antd/lib/_util/motion';
import {
  getTransitionDirection,
  getTransitionName,
} from 'antd/lib/_util/motion';
import type { InputStatus } from 'antd/lib/_util/statusUtils';
import {
  getMergedStatus,
  getStatusClassNames,
} from 'antd/lib/_util/statusUtils';
import getIcons from './utils/iconUtil';
import { useCompactItemContext } from 'antd/lib/space/Compact';

const forceMousedownOptionOpen =
  (window as any).externalConfig?.['common']?.forceMousedownOptionOpen ?? false;

type RawValue = string | number;

export {
  OptionProps,
  BaseSelectRef as RefSelectProps,
  BaseOptionType,
  DefaultOptionType,
};

export interface LabeledValue {
  key?: string;
  value: RawValue;
  label: React.ReactNode;
}

export type SelectValue =
  | RawValue
  | RawValue[]
  | LabeledValue
  | LabeledValue[]
  | undefined;

export interface InternalSelectProps<
  ValueType = any,
  OptionType extends BaseOptionType | DefaultOptionType = DefaultOptionType,
> extends Omit<RcSelectProps<ValueType, OptionType>, 'mode'> {
  suffixIcon?: React.ReactNode;
  size?: SizeType;
  disabled?: boolean;
  mode?: 'multiple' | 'tags' | 'SECRET_COMBOBOX_MODE_DO_NOT_USE';
  bordered?: boolean;
}

export interface SelectProps<
  ValueType = any,
  OptionType extends BaseOptionType | DefaultOptionType = DefaultOptionType,
> extends Omit<
    InternalSelectProps<ValueType, OptionType>,
    | 'inputIcon'
    | 'mode'
    | 'getInputElement'
    | 'getRawInputElement'
    | 'backfill'
    | 'placement'
  > {
  placement?: SelectCommonPlacement;
  mode?: 'multiple' | 'tags';
  status?: InputStatus;
  /**
   * @deprecated `dropdownClassName` is deprecated which will be removed in next major
   *   version.Please use `popupClassName` instead.
   */
  dropdownClassName?: string;
  popupClassName?: string;
  optionLabelTooltip?: boolean;
  optionLabelTooltipProps?: any;
}

const SECRET_COMBOBOX_MODE_DO_NOT_USE = 'SECRET_COMBOBOX_MODE_DO_NOT_USE';

const InternalSelect = <
  OptionType extends BaseOptionType | DefaultOptionType = DefaultOptionType,
>(
  {
    prefixCls: customizePrefixCls,
    bordered = true,
    className,
    getPopupContainer,
    dropdownClassName,
    popupClassName,
    listHeight = 256,
    placement,
    listItemHeight = 32,
    size: customizeSize,
    disabled: customDisabled,
    notFoundContent,
    status: customStatus,
    showArrow,
    contentEditable,
    optionLabelTooltip,
    optionLabelTooltipProps,
    ...props
  }: SelectProps<OptionType>,
  ref: React.Ref<BaseSelectRef>,
) => {
  const {
    getPopupContainer: getContextPopupContainer,
    getPrefixCls,
    renderEmpty,
    direction,
    virtual,
    dropdownMatchSelectWidth,
  } = React.useContext(ConfigContext);
  const size = React.useContext(SizeContext);

  const prefixCls = getPrefixCls('select', customizePrefixCls);
  const rootPrefixCls = getPrefixCls();
  const { compactSize, compactItemClassnames } = useCompactItemContext(
    prefixCls,
    direction,
  );

  const mode = React.useMemo(() => {
    const { mode: m } = props as InternalSelectProps<OptionType>;

    if ((m as any) === 'combobox') {
      return undefined;
    }

    if (m === SECRET_COMBOBOX_MODE_DO_NOT_USE) {
      return 'combobox';
    }

    return m;
  }, [props.mode]);

  const isMultiple = mode === 'multiple' || mode === 'tags';
  const mergedShowArrow =
    showArrow !== undefined
      ? showArrow
      : props.loading || !(isMultiple || mode === 'combobox');

  // ===================== Form Status =====================
  const {
    status: contextStatus,
    hasFeedback,
    isFormItemInput,
    feedbackIcon,
  } = useContext(FormItemInputContext);
  const mergedStatus = getMergedStatus(contextStatus, customStatus);

  // ===================== Empty =====================
  let mergedNotFound: React.ReactNode;
  if (notFoundContent !== undefined) {
    mergedNotFound = notFoundContent;
  } else if (mode === 'combobox') {
    mergedNotFound = null;
  } else {
    mergedNotFound = (renderEmpty || defaultRenderEmpty)('Select');
  }

  // ===================== Icons =====================
  const { suffixIcon, itemIcon, removeIcon, clearIcon } = getIcons({
    ...props,
    multiple: isMultiple,
    hasFeedback,
    feedbackIcon,
    showArrow: mergedShowArrow,
    prefixCls,
  });

  const selectProps = omit(props as typeof props & { itemIcon: any }, [
    'suffixIcon',
    'itemIcon',
  ]);

  const rcSelectRtlDropdownClassName = classNames(
    popupClassName || dropdownClassName,
    {
      [`${prefixCls}-dropdown-${direction}`]: direction === 'rtl',
    },
  );

  const mergedSize = compactSize || customizeSize || size;

  // ===================== Disabled =====================
  const disabled = React.useContext(DisabledContext);
  const mergedDisabled = customDisabled ?? disabled;

  const mergedClassName = classNames(
    {
      [`${prefixCls}-lg`]: mergedSize === 'large',
      [`${prefixCls}-sm`]: mergedSize === 'small',
      [`${prefixCls}-rtl`]: direction === 'rtl',
      [`${prefixCls}-borderless`]: !bordered,
      [`${prefixCls}-in-form-item`]: isFormItemInput,
    },
    getStatusClassNames(prefixCls, mergedStatus, hasFeedback),
    compactItemClassnames,
    className,
  );

  // ===================== Placement =====================
  const getPlacement = () => {
    if (placement !== undefined) {
      return placement;
    }
    return direction === 'rtl'
      ? ('bottomRight' as SelectCommonPlacement)
      : ('bottomLeft' as SelectCommonPlacement);
  };

  return (
    <RcSelect<any, any>
      ref={ref as any}
      virtual={virtual}
      dropdownMatchSelectWidth={dropdownMatchSelectWidth}
      {...selectProps}
      transitionName={getTransitionName(
        rootPrefixCls,
        getTransitionDirection(placement),
        props.transitionName,
      )}
      optionLabelTooltip={optionLabelTooltip ?? false}
      optionLabelTooltipProps={optionLabelTooltipProps ?? {}}
      contentEditable={contentEditable ?? false}
      enterSwitch={props?.enterSwitch ?? false}
      listHeight={listHeight}
      listItemHeight={listItemHeight}
      mode={mode as any}
      prefixCls={prefixCls}
      placement={getPlacement()}
      direction={direction}
      inputIcon={suffixIcon}
      menuItemSelectedIcon={itemIcon}
      removeIcon={removeIcon}
      clearIcon={clearIcon}
      notFoundContent={mergedNotFound}
      className={mergedClassName}
      getPopupContainer={getPopupContainer || getContextPopupContainer}
      dropdownClassName={rcSelectRtlDropdownClassName}
      showArrow={hasFeedback || showArrow}
      disabled={mergedDisabled}
      dumbOnComposition={props?.dumbOnComposition ?? false}
      mousedownOptionOpen={
        forceMousedownOptionOpen === true
          ? true
          : selectProps?.mousedownOptionOpen ?? true
      }
      leftRightSwitchPage={props?.leftRightSwitchPage ?? false}
      onLeftRightKeyDownSwitchPage={props?.onLeftRightKeyDownSwitchPage}
      dropdownAlign={props?.dropdownAlign}
    />
  );
};

const Select = React.forwardRef(InternalSelect) as unknown as (<
  ValueType = any,
  OptionType extends BaseOptionType | DefaultOptionType = DefaultOptionType,
>(
  props: React.PropsWithChildren<SelectProps<ValueType, OptionType>> & {
    ref?: React.Ref<BaseSelectRef>;
  },
) => React.ReactElement) & {
  SECRET_COMBOBOX_MODE_DO_NOT_USE: string;
  Option: typeof Option;
  OptGroup: typeof OptGroup;
};

Select.SECRET_COMBOBOX_MODE_DO_NOT_USE = SECRET_COMBOBOX_MODE_DO_NOT_USE;
Select.Option = Option;
Select.OptGroup = OptGroup;

export default Select;
