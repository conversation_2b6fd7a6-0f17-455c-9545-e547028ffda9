import { useEffect, useRef } from 'react';

// 方案二：使用自定义Hook管理多个Select
const useSelectKeyboardControl = () => {
  const openDropdowns = useRef(new Set());

  const registerDropdown = (id, isOpen) => {
    if (isOpen) {
      openDropdowns.current.add(id);
    } else {
      openDropdowns.current.delete(id);
    }
  };

  useEffect(() => {
    const handleKeyDown = (event) => {
      // 只在有dropdown打开且事件没有被内部处理时才阻止
      if (
        openDropdowns.current.size > 0 &&
        (event.key === 'PageUp' || event.key === 'PageDown') &&
        !event.defaultPrevented
      ) {
        // 检查是否已被阻止
        event.preventDefault();
      }
    };

    // 使用冒泡阶段，让内部处理器先执行
    document.addEventListener('keydown', handleKeyDown, { passive: false });
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return registerDropdown;
};

export default useSelectKeyboardControl;
