import { Emitter, EventConstant } from '@uni/utils/src/emitter';

/**
 * 处理主表第一条数据编码变化时的 focus 跳转功能
 * @param form 表单实例
 * @param currentFirstIcdeCode 当前第一条数据的编码
 * @param prevFirstIcdeCode 之前的第一条数据的编码
 * @param mainIcdeC00D48FocusChange C00-D48 focus 标志
 * @param mainIcdeSorTInitialFocusChange S或T开头 focus 标志
 */
export const handleMainIcdeFocusChange = (
  form: any,
  currentFirstIcdeCode: string,
  prevFirstIcdeCode: string,
  mainIcdeC00D48FocusChange?: boolean,
  mainIcdeSorTInitialFocusChange?: boolean,
) => {
  console.log(
    'handleMainIcdeFocusChange',
    form,
    currentFirstIcdeCode,
    prevFirstIcdeCode,
    mainIcdeC00D48FocusChange,
    mainIcdeSorTInitialFocusChange,
  );
  // 如果编码没有变化，直接返回
  if (currentFirstIcdeCode === prevFirstIcdeCode) {
    return;
  }

  // C00-D48 的正则表达式：^C|(D([0-3][0-9]|4[0-8])).*
  const c00d48Regex = /^C|(D([0-3][0-9]|4[0-8])).*/;

  // S或T开头的正则表达式
  const sorTRegex = /^[ST].*/;

  // 处理 pathologicalDiagnosisTable focus (当编码匹配 C00-D48 范围时)
  if (mainIcdeC00D48FocusChange && c00d48Regex.test(currentFirstIcdeCode)) {
    handlePathologicalDiagnosisFocus(form);
  }

  // 处理 IcdeDamgsItem focus (当编码以S或T开头时)
  if (mainIcdeSorTInitialFocusChange && sorTRegex.test(currentFirstIcdeCode)) {
    handleIcdeDamgsItemFocus();
  }
};

/**
 * 处理病理诊断表格的 focus
 */
const handlePathologicalDiagnosisFocus = (form: any) => {
  const pathologicalData = form?.getFieldValue('pathological-diagnosis-table');

  // 为pathology-table发送专门的跳转准备事件
  Emitter.emit('DMR_PATHOLOGY_TABLE_JUMP_PREPARE', {
    targetType: 'pathology-table',
  });

  // 为IcdeMainTable发送回跳准备事件
  Emitter.emit('DMR_PATHOLOGY_BACK_JUMP_PREPARE', {
    targetType: 'diagnosis-main-table',
  });

  if (!pathologicalData || pathologicalData.length === 0) {
    // 如果表格内没有数据，先创建一条数据，并设置为 MCode 类型
    Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_ADD, {
      focusId: undefined,
      defaultMCode: true,
    });

    // 等待数据创建完成后再 focus
    // setTimeout(() => {
    //   focusPathologicalDiagnosisByIndex(0);
    // }, 200);
  } else {
    // 查找是否有 PathoType 为 MCode 的数据
    const mcodeIndex = pathologicalData.findIndex(
      (item) => item?.PathoType === 'MCode',
    );

    if (mcodeIndex > -1) {
      // 如果有 MCode 数据，focus 到该条数据的 IcdeSelect input
      focusPathologicalDiagnosisByIndex(mcodeIndex);
    } else {
      // 查找空行数据（只有id和UniqueId的数据）
      const emptyRowIndex = pathologicalData.findIndex((item) => {
        // 检查是否为空行：只有 id 和 UniqueId 字段，其他关键字段都为空或未定义
        const hasOnlyBasicFields =
          item.id &&
          item.UniqueId &&
          !item.PathoType &&
          !item.DiagAccord &&
          !item.PathologyIcdeCode &&
          !item.PathologyIcdeName;
        return hasOnlyBasicFields;
      });

      if (emptyRowIndex > -1) {
        // 找到空行，发送事件让 pathology-table 处理设置默认值
        Emitter.emit('DMR_PATHOLOGY_SET_EMPTY_ROW_DEFAULT', {
          emptyRowIndex: emptyRowIndex,
        });

        // focus 到这一行
        setTimeout(() => {
          focusPathologicalDiagnosisByIndex(emptyRowIndex);
        }, 200);
      } else {
        // 没有空行，创建一条新数据，并设置为 MCode 类型
        Emitter.emit(EventConstant.DMR_PATHOLOGY_ICDE_ADD, {
          focusId: undefined,
          defaultMCode: true,
        });

        // 等待数据创建完成后再 focus
        setTimeout(() => {
          const newData = form?.getFieldValue('pathological-diagnosis-table');
          focusPathologicalDiagnosisByIndex(newData?.length - 1);
        }, 200);
      }
    }
  }
};

/**
 * Focus 到病理诊断表格指定 index 的 IcdeSelect input
 */
const focusPathologicalDiagnosisByIndex = (index: number) => {
  const icdeSelectInput = document.getElementById(
    `formItem#PathologyIcdeCode#${index}#IcdeSelect`,
  );
  if (icdeSelectInput) {
    setTimeout(() => {
      icdeSelectInput.focus();
    }, 100);
  }
};

/**
 * 处理 IcdeDamgsItem 的 focus
 */
const handleIcdeDamgsItemFocus = () => {
  // 为IcdeDamgsItem（icde-select组件）发送专门的跳转准备事件
  Emitter.emit('DMR_ICDE_DAMGS_JUMP_PREPARE', {
    targetType: 'icde-damgs-item',
  });

  // 为IcdeMainTable发送回跳准备事件
  Emitter.emit('DMR_PATHOLOGY_BACK_JUMP_PREPARE', {
    targetType: 'diagnosis-main-table',
  });

  const icdeDamgsItem = document.getElementById('IcdeDamgsItem');
  if (icdeDamgsItem) {
    const input = icdeDamgsItem.querySelector('input');
    if (input) {
      setTimeout(() => {
        input.focus();
      }, 100);
    }
  }
};
