import { BatchDeleteButton } from '@/pages/dmr/components/batch-delete-button';
import IconBtn from '@uni/components/src/iconBtn';
import { RowSelectionHeader } from '@uni/grid/src/components/row-selection-header';
import { RowSelectionCheckbox } from '@uni/grid/src/components/row-selection';
import {
  DragHandler,
  extraTitle,
  nonAddCell,
  operationColumns,
} from '@/pages/dmr/columns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { PlusCircleTwoTone } from '@ant-design/icons';
import OperationSelect from '../../oper-select/index';
import {
  InsurOnlyOperationExtraMap,
  IcdeOperationInputSelector,
  IcdeOperationReadonlyItem,
  OperationExtraTagsItem,
} from '@uni/grid/src/components/icde-oper-input/input';
import { noNeedReadOnlyColumns, readOnlyTextCenterColumns } from './constants';
import _ from 'lodash';
import DateSelect from '@uni/grid/src/components/date-select/index';
import { employeeDataSourceProcessor } from '@/pages/dmr/utils';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['dmr']?.tableOnlyAddIconTrigger ?? false;
const enableTableDropdownNG =
  (window as any).externalConfig?.['dmr']?.enableTableDropdownNG ?? false;
const icdeOperRowSelection =
  (window as any).externalConfig?.['dmr']?.icdeOperRowSelection ?? false;

// 医保表格专用的列定义（只包含医保相关列和关联显示列）
const operInsurColumnsBase = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'OperExtra',
    title: extraTitle(
      _.pickBy(InsurOnlyOperationExtraMap, (item) => item.isInsur === true),
      {
        IsObsolete: {
          prompt: '医保置灰',
        },
      },
    ),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'operation-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_OPER_INSUR_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-operationInsurTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_OPER_INSUR_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return (
            <OperationExtraTagsItem
              record={record}
              uniqueId={record?.UniqueId}
              eventName={`${EventConstant.DMR_OPER_INSUR_INSUR_SELECT_ADD}#${record?.id}`}
              nameKey={'OperExtra'}
              conditionDictionaryKey={'SSJB'}
              conditionDictionaryGroup={'Dmr'}
              form={form}
              extraMap={_.pickBy(
                InsurOnlyOperationExtraMap,
                (item) => item.isInsur === true,
              )}
            />
          );
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 14,
        };
      }

      return {};
    },
  },
  // {
  //   key: 'rowSelection',
  //   dataIndex: 'RowSelection',
  //   title: (
  //     <RowSelectionHeader
  //       tableId="operationInsurTable"
  //       onSelectAll={(checked) => {
  //         console.log('全选/反选:', checked);
  //       }}
  //     />
  //   ),
  //   visible: icdeOperRowSelection || false,
  //   align: 'center',
  //   width: 44,
  //   fixed: 'left',
  //   readonly: false,
  //   renderColumnFormItem: (node, record, index, dataIndex) => {
  //     return (
  //       <RowSelectionCheckbox
  //         id={`formItem#RowSelection#${index}#IcdeInsurTable`}
  //         recordId={record?.id}
  //         dataIndex={dataIndex}
  //         onChangeExtra={(checked) => {
  //           console.log('asddsadadasdacheckbox');
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  {
    key: 'sort',
    dataIndex: 'OperSort',
    title: '序',
    visible: true,
    align: 'center',
    width: 70,
    fixed: 'left',
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      let labelNode = (
        <span
          style={{ whiteSpace: 'nowrap' }}
          className={'operation-index'}
        >{`${index + 1}`}</span>
      );
      const SortDragHandler = DragHandler(labelNode);
      return <SortDragHandler />;
    },
  },
  // {
  //   dataIndex: 'IsMain',
  //   title: '医保主诊',
  //   visible: true,
  //   width: 50,
  //   align: 'center',
  //   renderColumnFormItem: (node, record, index, dataIndex) => {
  //     return (
  //       <IcdeOperCheckbox
  //         id={`formItem#IsMain#${index}#IcdeInsurTable`}
  //         recordId={record?.id}
  //         dataIndex={dataIndex}
  //         onChangeExtra={(checked) => {
  //           Emitter.emit(EventConstant.DMR_ICDE_INSURE_MAIN, {
  //             id: record?.id,
  //             values: {
  //               IsMain: checked,
  //             },
  //             index: index,
  //           });
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  // {
  //   dataIndex: 'IsReported',
  //   title: '医保上报',
  //   visible: true,
  //   width: 50,
  //   align: 'center',
  //   renderColumnFormItem: (node, record, index, dataIndex, form) => {
  //     return (
  //       <IcdeOperCheckbox
  //         id={`formItem#IsReported#${index}#IcdeInsurTable`}
  //         recordId={record['id']}
  //         dataIndex={dataIndex}
  //         dependencyKey={'IsMain'}
  //         dependencyValue={true}
  //         form={form}
  //         minimumChecked={1}
  //         onChangeExtra={(checked) => {
  //           Emitter.emit(EventConstant.DMR_ICDE_REPORT, checked);
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  {
    dataIndex: 'InsurCode',
    title: '医保编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <OperationSelect
          columnType={'Insur'}
          tableId={'operationInsurTable'}
          componentId={`InsurOperCode#${index}`}
          interfaceUrl={'Api/Insur/InsurSearch/Oper'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          // disabled={!entity['IsObsolete']}
          // disabled={!entity['OperExtra']?.includes('IsObsolete')}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
            OperExtra: 'OperExtra',
          }}
          // instantSelect={true}
          value={{
            value: record['InsurCode'],
          }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG
              ? {
                  points: ['tl', 'bl'], // 下拉对齐到输入框的左上角 (tl) → 下拉的左下角 (bl)
                  offset: [-124, 4], // y 方向下移 4px，避免顶到输入框
                  overflow: {
                    adjustY: false, // 纵向溢出时自动反向
                  },
                }
              : undefined
          }
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurName',
    title: '医保手术名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'Operator',
    title: '手术者',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      // return (
      //   <OperationFieldInput
      //     record={entity}
      //     dataIndex={'Operator'}
      //     index={index}
      //   />
      // )
      return (
        <IcdeOperationInputSelector
          tableId={'operationInsurTable'}
          className={'operation-input'}
          dataIndex={'Operator'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          numberSelectItem={enableTableDropdownNG}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnaType',
    title: '麻醉方式',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'MZFS',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationInsurTable'}
          className={'operation-input'}
          dataIndex={'AnaType'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'MZFS'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnaDoc',
    title: '麻醉医师',
    visible: true,
    align: 'center',
    width: 80,
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationInsurTable'}
          className={'operation-input'}
          dataIndex={'AnaDoc'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          leftOneAutoSelect={true}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'OprnOprtBegntime',
    title: '手术及操作日期',
    visible: true,
    align: 'center',
    width: 166,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#OprnOprtBegntime#${index}#CompactInput`}
          formKey={'OprnOprtBegntime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'OprnOprtEndtime',
    title: '手术截止日期',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#OprnOprtEndtime#${index}#CompactInput`}
          formKey={'OprnOprtEndtime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'AnstBegntime',
    title: '手术麻醉时间',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#AnstBegntime#${index}#CompactInput`}
          formKey={'AnstBegntime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'AnstEndtime',
    title: '麻醉结束时间',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#AnstEndtime#${index}#CompactInput`}
          formKey={'AnstEndtime'}
          dataTableIndex={index}
          value={record['AnstEndtime']}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'operation',
    title: icdeOperRowSelection ? (
      <BatchDeleteButton tableId="operationInsurTable" />
    ) : (
      ''
    ),
    visible: true,
    align: 'center',
    width: 100,
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_OPER_INSUR_COPY, {
                UniqueId: record?.UniqueId,
                index: index,
              });
            }}
          />
          <IconBtn
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_OPER_INSUR_DELETE, index);
            }}
          />
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

// 兼容：保留原导出名，指向基础列
export const operInsurColumns = operInsurColumnsBase;

export const getOperInsurColumns = (insurSeparateTableLogic?: {
  canEditInsurTable?: boolean;
}) => {
  const canEditInsurTable = insurSeparateTableLogic?.canEditInsurTable ?? true;

  const processed = (operInsurColumnsBase as any[])
    .map((col) => {
      if (!canEditInsurTable) {
        if (
          noNeedReadOnlyColumns?.findIndex((d) => d === col?.dataIndex) === -1
        ) {
          const base: any = { ...col, readonly: true };
          return {
            ...base,
            renderColumnFormItem: (
              node: any,
              record: any,
              index: number,
              dataIndex: string,
              form: any,
              extraItem: any,
            ) => (
              <IcdeOperationReadonlyItem
                conditionDictionaryKey={base?.conditionDictionaryKey}
                conditionDictionaryGroup={base?.conditionDictionaryGroup}
                extraItem={extraItem}
                style={
                  readOnlyTextCenterColumns?.findIndex(
                    (d) => d === col?.dataIndex,
                  ) !== -1
                    ? { justifyContent: 'center' }
                    : {}
                }
                type={
                  base?.dataIndex?.toLowerCase()?.indexOf('time') > -1
                    ? 'time'
                    : null
                }
              />
            ),
          };
        }
      }
      return col;
    })
    .filter((c) => !(c?.dataIndex === 'operation' && !canEditInsurTable));

  // 当无编辑权限时，禁用“序”列的拖拽手柄，仅显示标签
  const updated = processed.map((col: any) => {
    if (col?.key === 'sort' || col?.dataIndex === 'OperSort') {
      if (!canEditInsurTable) {
        return {
          ...col,
          renderColumnFormItem: (node: any, record: any, index: number) => {
            let labelNode = (
              <span
                style={{ whiteSpace: 'nowrap' }}
                className={'operation-index'}
              >{`${index + 1}`}</span>
            );
            // 无权限时不包裹 DragHandler，避免可拖拽
            return labelNode;
          },
        };
      }
    }
    return col;
  });

  return updated;
};
