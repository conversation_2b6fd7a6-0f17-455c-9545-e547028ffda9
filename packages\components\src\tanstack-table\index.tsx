import './index.less';
import './tanstack-antd.less';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  RowData,
  ColumnDef,
  ColumnResizeMode,
  ColumnSort,
  SortingState,
  ColumnFiltersState,
  PaginationState,
  ColumnSizingState,
  ColumnSizingInfoState,
  ColumnOrderState,
  RowSelectionState,
} from '@tanstack/react-table';
import React, {
  CSSProperties,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
} from 'react';
import classNames from 'classnames';
import { TanstackTableMiddleware } from './middlewares';
import isNumber from 'lodash/isNumber';
import SorterItemProcessor, {
  SorterItemWithDirectoryProcessor,
} from './processor/sorter';
import FilterItemProcessor from './processor/filter';
import ColumnSorter, { toolTipLabel } from './components/column-sorter';
import ColumnFilter from './components/column-filter';
import { Empty, Pagination, Spin, Tooltip } from 'antd';
import {
  bodyTheadStyle,
  getMergeHeaderGroups,
  getTextAlign,
  getTotalLeft,
  getTotalRight,
  cellStyleProcessor,
  cellClassNameProcessor,
} from './utils';
import { isEmptyValues } from '@uni/utils/src/utils';
import isNil from 'lodash/isNil';
import sorter from './processor/sorter';
import {
  antSorterToTanstackSorter,
  filterProcessor,
  sorterProcessor,
} from './processor';
import { v4 as uuidv4 } from 'uuid';
import {
  DataSourceProcessorProps,
  processTableDataSource,
} from './processor/dataSource';
import {
  ColumnProcessorProps,
  TanstackColumnProcessor,
} from './processor/columns';
import { useVirtualizer } from '@tanstack/react-virtual';
import { RowProps } from './interfaces';
import { CommonRow, CommonCell, CellWrapper } from './components/row-cell';
import {
  SortableBodyWrapper,
  SortableRow,
  SortableTableWrapper,
  SortableTableWrapperProps,
  TableWrapperProps,
} from './components/column-drag';
import omit from 'lodash/omit';
import pick from 'lodash/pick';
import { DragEndEvent, DragOverlay } from '@dnd-kit/core';
import cloneDeep from 'lodash/cloneDeep';
import { arrayMove, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { getLineArrowUpDownEventKey } from '../drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { externalPaginationProcessor } from '../table/pagination';
import { TanstackDictionaryProcessor } from './processor/dictionary';
import {
  calculateDataThreshold,
  fetchMoreOnBottomReached,
  InfiniteScrollProps,
  onInfiniteScrollWithNoSense,
} from './processor/infinite-scroll';
import { useDeepCompareEffect, useUpdateEffect } from 'ahooks';
import { headerTextStyleProcessor } from './processor/text-style';
import { HeaderItem, TableHeader } from './components/header';
import debounce from 'lodash/debounce';
import { HeaderSorterContainer } from './components/header-drag';
import { ColumnWidthDetectProcessor } from './processor/width-detect-after-dict';

export const EmptyWrapper = (props: any) => {
  return <>{props?.children}</>;
};

declare module '@tanstack/react-table' {
  interface ColumnMeta<TData extends RowData, TValue> {
    render?: (node, record, index) => React.ReactNode;
    renderFormItem?: any;
    visible?: boolean;
    align?: any;
  }
}

interface ColumnResizingProps {
  enableColumnResizing?: boolean;
  onColumnSizingEnd?: (columnSizing: any) => void;
}

interface ColumnEllipseProps {
  enableContentEllipse?: boolean;
  enableHeaderEllipse?: boolean;
}

interface TanstackTableProps
  extends DataSourceProcessorProps,
    ColumnProcessorProps,
    RowProps,
    SortableTableWrapperProps,
    InfiniteScrollProps,
    ColumnResizingProps,
    ColumnEllipseProps {
  containerWidth?: number;

  columns: any[];
  dataSource: any[];

  id?: string;
  formKey?: string; // 用于DMR表格中需要CTRL + UP/ DOWN 来切换位置用
  className?: string;
  rowKey?: string;
  scroll?: { x?: string | number; y?: string | number };
  backendPagination?: boolean;
  bordered?: boolean;
  loading?: boolean;
  clickable?: boolean;
  pagination?: any;
  onTableChange?: any;
  editable?: any;
  size?: 'small' | 'middle' | 'large' | undefined;
  showPagination?: boolean;

  // Row Selection
  enableRowSelection?: boolean;
  rowSelection?: RowSelectionState;
  onRowSelectionChange?: (rowSelection: RowSelectionState) => void;
  defaultSelectAll?: boolean;

  // estimate Height
  estimateSize?: number;
  virtualized?: boolean;

  // 拖动
  enableDragging?: boolean;
  enableHeaderDragging?: boolean;

  // 病案首页下的 form item className
  formItemContainerClassName?: string;

  emptyDataYHeight?: boolean;

  enableHeaderNoWrap?: boolean;

  // 用于组合查询的 group
  onCustomAggregableClick?: (metaData: any) => void;

  renderSummary?: (tableColumns: any[]) => React.ReactNode;

  virtualizeOverscan?: number;

  // thead style
  theadStyle?: React.CSSProperties;
  widthDetectAfterDictionary?: boolean;
}

let TableWrapper = (props: any) => <>{props?.children}</>;
let BodyWrapper = (props: any) => <>{props?.children}</>;

let CellDragAlongWrapper = (props: any) => <>{props?.children}</>;

const UniTableNG = (props: TanstackTableProps) => {
  //The virtualizer needs to know the scrollable container element
  const tableContainerRef = React.useRef<HTMLDivElement>(null);

  const rowRefs = useRef({});
  const visibleItemIndices = useRef(new Set());
  const processedThresholds = useRef(new Set());

  const intersectionFetchNextPageRef = useRef(false);

  const latestScrollTop = useRef(0);
  const isScrollingDown = useRef(true);

  const columnSizingInfoRef = React.useRef<ColumnSizingInfoState>({
    columnSizingStart: [],
    deltaOffset: null,
    deltaPercentage: null,
    isResizingColumn: false,
    startOffset: null,
    startSize: null,
  });

  const [data, setData] = React.useState([]);

  const [columns, setColumns] = React.useState<any>([]);

  const [tableWidth, setTableWidth] = React.useState(undefined);

  const [columnOrder, setColumnOrder] = React.useState<ColumnOrderState>([]);
  const [tableSorter, setSortTableSorter] = React.useState<SortingState>([]);
  const [tableFilter, setTableFilter] = React.useState<ColumnFiltersState>([]);
  const [tablePagination, setTablePagination] = React.useState<PaginationState>(
    {
      pageIndex: 0,
      pageSize: props?.infiniteScroll
        ? props?.infiniteScrollPageSize ?? 100
        : 10,
    },
  );
  const [columnSizing, setColumnSizing] = React.useState<ColumnSizingState>({});

  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(
    props?.rowSelection ?? {},
  );

  const scrollTopRef = useRef(0);

  let paginationProps = {};
  let paginationStateProps = {};

  if (props?.backendPagination !== true && props?.pagination) {
    paginationProps = {
      getPaginationRowModel: getPaginationRowModel(),
    };
  }

  if (props?.infiniteScroll === true) {
    paginationProps = {
      getPaginationRowModel: getPaginationRowModel(),
      onPaginationChange: setTablePagination,
    };

    paginationStateProps = {
      pagination: tablePagination,
    };
  }

  const tableInstance = useReactTable({
    manualPagination: props?.backendPagination,
    data: data,
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
    ...paginationProps,
    // autoResetPageIndex: true,
    autoResetPageIndex: false,
    autoResetExpanded: false,
    debugTable: process.env.NODE_ENV === 'development',
    debugHeaders: process.env.NODE_ENV === 'development',
    debugColumns: process.env.NODE_ENV === 'development',
    enableHiding: true,
    enablePinning: true,
    enableRowSelection: props?.enableRowSelection ?? false,
    enableColumnResizing: props?.enableColumnResizing ?? false,
    columnResizeMode: 'onChange',
    onSortingChange: setSortTableSorter,
    onColumnFiltersChange: setTableFilter,
    onColumnSizingChange: setColumnSizing,
    onColumnOrderChange: setColumnOrder,
    onRowSelectionChange: (updatedRowSelection) => {
      setRowSelection(updatedRowSelection);
      // Handle both function and direct value updates
      const finalSelection =
        typeof updatedRowSelection === 'function'
          ? updatedRowSelection(rowSelection)
          : updatedRowSelection;
      props?.onRowSelectionChange?.(finalSelection);
    },
    state: {
      sorting: tableSorter,
      columnFilters: tableFilter,
      columnSizing: columnSizing,
      // columnSizingInfo: columnSizingInfoRef?.current,
      ...paginationStateProps,
      columnOrder: columnOrder,
      rowSelection: rowSelection,
    },
  });

  const { rows } = props?.virtualized
    ? tableInstance?.getCoreRowModel()
    : props?.backendPagination
    ? tableInstance?.getCoreRowModel()
    : tableInstance.getPaginationRowModel();

  const rowVirtualizer = useVirtualizer({
    count: data.length,
    estimateSize: () => props?.estimateSize ?? 50, //estimate row height for accurate scrollbar dragging
    getScrollElement: () => tableContainerRef.current,
    //measure dynamic row height, except in firefox because it measures table border height incorrectly
    measureElement:
      typeof window !== 'undefined' &&
      navigator.userAgent.indexOf('Firefox') === -1
        ? (element) => element?.getBoundingClientRect().height
        : undefined,
    overscan: props?.virtualizeOverscan ?? 10,
    useAnimationFrameWithResizeObserver: true,
  });

  useEffect(() => {
    Emitter.on(EventConstant.TABLE_COLUMN_SORTER_EDIT, (sorterInfo) => {
      setSortTableSorter(antSorterToTanstackSorter(sorterInfo));
    });

    return () => {
      Emitter.off(EventConstant.TABLE_COLUMN_SORTER_EDIT);
    };
  }, []);

  const handleWidthChange = (width) => {
    console.log('Table container width changed to:', width);
    setTableWidth(width);
  };

  const debouncedHandleWidthChange = debounce(
    (width) => {
      handleWidthChange(width);
    },
    100,
    {
      leading: false,
      trailing: true,
    },
  );

  // 宽度监听
  useEffect(() => {
    const tableContainer = document.getElementById('tanstack-table-container');
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        // Get the new width
        const width = entry.contentRect.width;
        debouncedHandleWidthChange(width);
      }
    });

    if (tableContainer) {
      resizeObserver.observe(tableContainer);
    }

    return () => {
      resizeObserver.unobserve(tableContainer);
    };
  }, []);

  // columnsSizing
  useDeepCompareEffect(() => {
    let visibleColumns = tableInstance.getVisibleFlatColumns();
    let columnSizeItem: any = {};
    visibleColumns?.forEach((item) => {
      columnSizeItem[item?.id] = item.getSize();
    });
    props?.onColumnSizingEnd && props?.onColumnSizingEnd?.(columnSizeItem);
  }, [columnSizing]);

  useUpdateEffect(() => {
    console.log('table sort filter', tableSorter, tableFilter);
    let sorter = sorterProcessor(tableSorter);
    let filters = filterProcessor(tableFilter);

    if (props?.backendPagination ?? !isNil((props?.pagination as any)?.total)) {
      global['tableParameters'] = {
        filters: filters,
        sorter: sorter,
      };

      props?.onTableChange &&
        props?.onTableChange(props?.pagination, filters, sorter, {
          action: 'sort',
          currentDataSource: data,
        });
    }

    if (props?.infiniteScroll === true) {
      props?.infiniteScrollSorterFilter &&
        props?.infiniteScrollSorterFilter(sorter, filters);
    }
  }, [tableSorter, tableFilter]);

  useEffect(() => {
    if (tableInstance) {
      if (props?.backendPagination !== false) {
        if (props?.pagination) {
          tableInstance.setPageSize(props?.pagination?.pageSize);
          tableInstance.setPageIndex(
            Math.max(props?.pagination?.current - 1, 0),
          );
        }
      }
    }
  }, [props?.pagination, tableInstance]);

  useEffect(() => {
    let dictionaryProceedColumns = TanstackDictionaryProcessor(
      cloneDeep(columns),
      props?.dictionaryData,
      props?.enableMaxCharNumberEllipses ?? false,
      props?.noMetaProperty === true ? null : 'meta',
      props?.maxEllipseCharNumber,
      props?.enableColumnResizing ?? false,
    )?.slice();

    // 按照Name排序
    let dictionarySorterProceedColumns = SorterItemWithDirectoryProcessor(
      cloneDeep(dictionaryProceedColumns),
      props?.backendPagination,
      props?.infiniteScroll,
      props?.dictionaryData,
    )?.slice();

    setColumns(dictionarySorterProceedColumns);
  }, [props?.dictionaryData]);

  const columnSizingWidthCalculateByColumnItems = (columns: any[]) => {
    let visibleColumns = columns?.filter((item) => item.hideInTable !== true);
    if (!isEmptyValues(props?.containerWidth)) {
      const existWidth = visibleColumns?.reduce(
        (sum, item) => sum + (item?.width ?? 0),
        0,
      );
      let contentWidth = props?.containerWidth - existWidth - 10;
      return Math.max(
        contentWidth /
          (visibleColumns?.length -
            visibleColumns?.filter((item) => !isEmptyValues(item?.width))
              ?.length),
        100,
      );
    }

    return 'auto';
  };

  const antColumnsToTanStackColumnsProcessor = (
    columns: any[],
    middleWareInstance: any,
  ): ColumnDef<any>[] => {
    let tantableColumns = [];

    columns?.map((item) => {
      const mergedStyle = headerTextStyleProcessor(item) as CSSProperties;

      let defaultColumnSize =
        (item?.enableResizing ?? props?.enableColumnResizing ?? false) === true
          ? columnSizingWidthCalculateByColumnItems(columns)
          : 'auto';

      // console.log('ColumnItem', item, mergedStyle);

      let tantableColumnItem = {
        id: item?.dataIndex ?? uuidv4(),
        accessorKey: item?.dataIndex,
        header: () => <span style={mergedStyle}>{item?.title}</span>,
        size: item?.width ?? defaultColumnSize ?? 'auto',
        minSize:
          (item?.enableResizing ?? props?.enableColumnResizing ?? false) ===
          true
            ? 70
            : item?.minWidth ?? item?.width ?? 70,
        meta: item,
        enableResizing:
          item?.enableResizing ?? props?.enableColumnResizing ?? false,
        // sort
        ...SorterItemProcessor(
          item,
          props?.backendPagination,
          props?.infiniteScroll,
        ),
        ...FilterItemProcessor(
          item,
          props?.backendPagination,
          props?.infiniteScroll,
        ),
      };

      if (!isEmptyValues(item?.children)) {
        tantableColumnItem['columns'] = antColumnsToTanStackColumnsProcessor(
          item?.children,
          middleWareInstance,
        );
        tantableColumnItem['rowSpan'] = item?.rowSpan ?? item?.children?.length;
      }

      middleWareInstance.process(tantableColumnItem);

      tantableColumns.push(tantableColumnItem);
    });

    // console.log('tantableColumns', tantableColumns);

    return tantableColumns;
  };

  useEffect(() => {
    let middleWareInstance = new TanstackTableMiddleware(tableInstance);
    let columnProcessorInstance = new TanstackColumnProcessor(
      props,
      props?.columns,
      columns,
    );

    let preProcessorColumns = columnProcessorInstance.processTableColumns();

    let transformedColumns = antColumnsToTanStackColumnsProcessor(
      preProcessorColumns,
      middleWareInstance,
    );

    // default sorter order process
    middleWareInstance.sortMiddleware(transformedColumns);

    // console.log(
    //   'transformedColumns',
    //   transformedColumns,
    //   columns.map((c) => c.id!),
    // );
    setColumns(transformedColumns);
    middleWareInstance.commit();

    // 设定ColumnOrder
    setColumnOrder(columns.map((c) => c.id!));
  }, [props?.columns]);

  useEffect(() => {
    // console.log('columns changed', columns);
  }, [columns]);

  useEffect(() => {
    processTableDataSource(props?.dataSource, props, setData, data);
  }, [props?.dataSource]);

  // Handle defaultSelectAll and external rowSelection changes
  useEffect(() => {
    if (props?.defaultSelectAll && data.length > 0) {
      const allRowSelection: RowSelectionState = {};
      data.forEach((item, index) => {
        const rowId = item[props?.rowKey ?? 'id'] ?? index.toString();
        allRowSelection[rowId] = true;
      });
      setRowSelection(allRowSelection);
      props?.onRowSelectionChange?.(allRowSelection);
    }
  }, [props?.defaultSelectAll, data, props?.rowKey]);

  // Sync external rowSelection changes
  useEffect(() => {
    if (props?.rowSelection !== undefined) {
      setRowSelection(props.rowSelection);
    }
  }, [props?.rowSelection]);

  // 用于CTRL + UP / DOWN 变更顺序用
  useEffect(() => {
    if (props?.formKey) {
      Emitter.on(getLineArrowUpDownEventKey(props?.formKey), (payload) => {
        let oldIndex = payload?.oldIndex;
        let newIndex = payload?.newIndex;

        if (!isEmptyValues(oldIndex) && !isEmptyValues(newIndex)) {
          if (
            newIndex < 0 ||
            newIndex > data?.filter((item) => item?.id !== 'ADD')?.length - 1
          ) {
            return;
          }

          let oldItem = data?.at(oldIndex);
          let newItem = data?.at(newIndex);

          onInternalDragEnd({
            active: { id: oldItem?.id },
            over: { id: newItem?.id },
            focusId: payload?.focusId,
          } as any);
        }
      });
    }

    return () => {
      if (props?.formKey) {
        Emitter.off(getLineArrowUpDownEventKey(props?.formKey));
      }
    };
  }, [data]);

  // Set up IntersectionObserver for all items
  useEffect(() => {
    if (!tableContainerRef.current) return;

    const options = {
      root: tableContainerRef.current,
      rootMargin: '0px',
      threshold: 0.5, // Item is considered visible when 50% is in view
    };

    const handleIntersection = (entries) => {
      if (props?.infiniteScroll === false) {
        return;
      }

      entries.forEach((entry) => {
        const index = parseInt(entry.target.dataset.index, 10);

        // console.log(
        //   'handleIntersection',
        //   entry.isIntersecting,
        //   index,
        //   Math.floor(data?.length * 0.7),
        //   intersectionFetchNextPageRef.current
        // );
        if (
          entry.isIntersecting &&
          intersectionFetchNextPageRef.current === false
        ) {
          if (
            index >= Math.floor(data?.length * 0.7) &&
            isScrollingDown.current === true
          ) {
            intersectionFetchNextPageRef.current = true;
            setTablePagination({
              pageIndex: tablePagination?.pageIndex + 1,
              pageSize:
                props?.infiniteScrollPageSize ??
                tablePagination?.pageSize ??
                10,
            });

            if (!isEmptyValues(props?.fetchNextPage)) {
              props?.fetchNextPage(
                tablePagination?.pageIndex + 1,
                props?.infiniteScrollPageSize ??
                  tablePagination?.pageSize ??
                  10,
                () => {
                  setTimeout(() => {
                    intersectionFetchNextPageRef.current = false;
                  }, 200); // callback 后延迟解锁，防止短时间内重复触发
                },
              );
            }
          }
        }
      });
    };

    // console.log('rowRefs.current', rowRefs.current);
    const observer = new IntersectionObserver(handleIntersection, options);

    // Observe all row items that have refs
    if (props?.infiniteScroll === true) {
      Object.values(rowRefs.current).forEach((ref: any) => {
        if (ref) {
          observer.observe(ref);
        }
      });

      return () => {
        Object.values(rowRefs.current).forEach((ref: any) => {
          if (ref) {
            observer.unobserve(ref);
          }
        });
      };
    }
  }, [data, props?.infiniteScroll]);

  // console.log('dataSource', props?.dataSource, tableInstance.getRowModel());

  // console.log(
  //   'getHeaderGroups',
  //   props?.id,
  //   tableInstance.getTotalSize(),
  //   tableContainerRef?.current?.offsetWidth,
  //   tableInstance.getHeaderGroups(),
  //   getMergeHeaderGroups(tableInstance.getHeaderGroups()),
  // );

  const rowItems = props?.virtualized
    ? rowVirtualizer?.getVirtualItems()
    : (props?.backendPagination
        ? tableInstance?.getCoreRowModel()
        : props?.pagination
        ? tableInstance.getPaginationRowModel()
        : tableInstance?.getCoreRowModel()
      )?.rows;

  const virtualizationProps = bodyTheadStyle(
    rowVirtualizer,
    props?.virtualized,
  );

  let TableRow = CommonRow;
  const draggableItems = useMemo(() => {
    return data
      ?.slice()
      ?.filter((item) => item?.[props?.rowKey ?? 'id'] !== 'ADD')
      ?.map((i) => i?.[props?.rowKey]);
  }, [data]);

  if (props?.enableDragging) {
    TableWrapper = SortableTableWrapper;
    BodyWrapper = SortableBodyWrapper;
    TableRow = SortableRow;
  }

  if (props?.enableHeaderDragging === true) {
    TableWrapper = HeaderSorterContainer;
  }

  const onInternalDragEnd = async (event: DragEndEvent) => {
    let { active, over } = event;
    if (active && over) {
      if (active?.id !== over?.id) {
        const oldIndex = data.findIndex(
          (item) => item?.[props?.rowKey] === active?.id,
        );
        const newIndex = data.findIndex(
          (item) => item?.[props?.rowKey] === over?.id,
        );
        if (!isEmptyValues(props?.onDragEndPre)) {
          let result = await props?.onDragEndPre(
            {
              id: active.id,
              oldIndex: oldIndex,
              newIndex: newIndex,
            },
            {
              id: over.id,
              oldIndex: newIndex,
              newIndex: oldIndex,
            },
          );
          if (result === false) {
            return;
          }
        }

        let newData = arrayMove(data, oldIndex, newIndex);

        props?.onDragExtra && props?.onDragExtra(newData);

        setData(newData);

        props?.onDragEnd &&
          props?.onDragEnd(event, newData, (event as any)?.focusId);
      }
    }
  };

  const onHeaderDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active && over && active.id !== over.id) {
      setColumnOrder((columnOrder) => {
        const oldIndex = columnOrder.indexOf(active.id as string);
        const newIndex = columnOrder.indexOf(over.id as string);
        return arrayMove(columnOrder, oldIndex, newIndex); //this is just a splice util
      });
    }
  };

  const externalPaginationConfig = externalPaginationProcessor();

  const emptyDataHeightStyle =
    props?.emptyDataYHeight && rowItems?.length === 0
      ? { height: props?.scroll?.y }
      : {};

  return (
    <div
      id={props?.id ?? `uni-table-ng-${uuidv4()}}`}
      className={classNames(
        'tanstack-table-container',
        props?.className,
        props?.clickable ? 'clickable' : '',
      )}
      style={{
        width: isNumber(props?.scroll?.x) ? props?.scroll?.x : '100%',
        // overflowX: 'auto',
      }}
    >
      <Spin spinning={props?.loading ?? false}>
        <div
          className={classNames(
            'ant-table',
            `ant-table-${props?.size ?? 'middle'}`,
            props?.bordered ? 'ant-table-bordered' : '',
            tableInstance?.getLeftLeafHeaders()?.length > 0
              ? 'ant-table-ping-left'
              : '',
            tableInstance?.getRightLeafHeaders()?.length > 0
              ? 'ant-table-ping-right'
              : '',
          )}
        >
          <div
            id={'tanstack-table-container'}
            className={'ant-table-container overflow-x-auto'}
            ref={tableContainerRef}
            onScroll={(e) => {
              const scrollTop = tableContainerRef?.current?.scrollTop;
              isScrollingDown.current = scrollTop > latestScrollTop.current;
              latestScrollTop.current = scrollTop;
            }}
            style={{
              ...emptyDataHeightStyle,
              maxHeight: props?.scroll?.y,
              overflowY: 'auto',
              position: 'relative',
            }}
          >
            <TableWrapper
              {...pick(props, TableWrapperProps)}
              onDragEnd={onInternalDragEnd}
              onHeaderDragEnd={onHeaderDragEnd}
            >
              <table
                {...{
                  style: {
                    width: tableInstance.getTotalSize()
                      ? tableContainerRef?.current?.offsetWidth >
                        tableInstance.getTotalSize()
                        ? '100%'
                        : tableInstance.getTotalSize()
                      : '100%',
                    ...(virtualizationProps?.table?.style ?? {}),
                  },
                }}
              >
                <TableHeader
                  columnSizingState={columnSizing}
                  tableInstance={tableInstance}
                  virtualizationProps={virtualizationProps}
                  onCustomAggregableClick={props?.onCustomAggregableClick}
                  headerDragging={props?.enableHeaderDragging}
                  columnOrder={columnOrder}
                  theadStyle={props?.theadStyle}
                />
                <BodyWrapper items={draggableItems}>
                  <tbody
                    className={'ant-table-tbody'}
                    {...virtualizationProps?.tbody}
                  >
                    {isEmptyValues(data) && (
                      <tr>
                        <td colSpan={columns?.length}>
                          <div
                            className={'empty-container'}
                            style={{
                              width:
                                tableContainerRef?.current?.offsetWidth - 20,
                            }}
                          >
                            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                          </div>
                        </td>
                      </tr>
                    )}
                    {rowItems.map((rowItem, rowIndex) => {
                      const row = props?.virtualized
                        ? rows[rowItem.index]
                        : rowItem;
                      let virtualizeProps = {};
                      if (props?.virtualized) {
                        virtualizeProps['data-index'] = rowItem.index;
                        virtualizeProps['style'] = {
                          position: 'absolute',
                          transform: `translateY(${rowItem.start}px)`, //this should always be a `style` as it changes on scroll
                        };
                      }

                      return (
                        <TableRow
                          rowRefs={rowRefs}
                          {...pick(props, [
                            'canRowSortable',
                            'rowBaseSort',
                            'onRowClick',
                          ])}
                          formKey={props?.formKey}
                          rowKey={props?.rowKey}
                          row={row}
                          rowClassName={props?.rowClassName}
                          virtualizeProps={virtualizeProps}
                          formItemContainerClassName={
                            props?.formItemContainerClassName
                          }
                          rowVirtualizer={rowVirtualizer}
                          tableWidth={tableWidth}
                        >
                          {row.getVisibleCells().map((cell) => {
                            let headerWidth =
                              document.getElementById(
                                `th-${cell?.column?.columnDef?.accessorKey}`,
                              )?.offsetWidth ?? 0;

                            return (
                              <CellDragAlongWrapper
                                key={cell.id}
                                items={columnOrder}
                                strategy={horizontalListSortingStrategy}
                              >
                                <CellWrapper
                                  {...props}
                                  columnSizingState={columnSizing}
                                  // columnSize={cell?.column?.getSize()}
                                  columnSize={headerWidth}
                                  cell={cell}
                                  tableInstance={tableInstance}
                                  row={row}
                                  rowItem={rowItem}
                                  tableWidth={tableWidth}
                                />
                              </CellDragAlongWrapper>
                            );
                          })}
                        </TableRow>
                      );
                    })}
                  </tbody>

                  {props?.renderSummary &&
                    props?.renderSummary(tableInstance.getLeafHeaders())}
                </BodyWrapper>
              </table>
            </TableWrapper>
          </div>
        </div>
      </Spin>

      {props?.pagination && (
        <Pagination
          className={
            'ant-pagination ant-table-pagination ant-table-pagination-right'
          }
          defaultPageSize={
            props?.pagination?.defaultPageSize ??
            externalPaginationConfig?.defaultPageSize ??
            10
          }
          total={props?.pagination?.total ?? data?.length}
          pageSizeOptions={
            props?.pagination?.pageSizeOptions ??
            externalPaginationConfig?.pageSizeOptions ?? ['10, 20']
          }
          pageSize={props?.pagination?.pageSize}
          current={props?.pagination?.current}
          showSizeChanger
          showTotal={(total, range) =>
            `第${range[0]}-${range[1]} 条/总共${total}条`
          }
          onChange={(page, pageSize) => {
            props?.onTableChange &&
              props?.onTableChange(
                {
                  current: page,
                  pageSize: pageSize,
                },
                null,
                null,
                null,
              );
          }}
          onShowSizeChange={(current, size) => {
            props?.onTableChange &&
              props?.onTableChange(
                {
                  current: current,
                  pageSize: size,
                },
                null,
                null,
                null,
              );
          }}
        />
      )}
    </div>
  );
};

export default UniTableNG;
