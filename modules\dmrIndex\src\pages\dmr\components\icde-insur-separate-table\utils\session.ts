export const INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY = 'insurSeparateTableLogic';

export function setSessionJSON(key: string, value: any) {
  try {
    if (typeof window === 'undefined') return;
    sessionStorage.setItem(key, JSON.stringify(value));
  } catch (e) {
    // swallow
    console.error('setSessionJSON error', e);
  }
}

export function getSessionJSON<T = any>(
  key: string,
  defaultValue: T | null = null,
): T | null {
  try {
    if (typeof window === 'undefined') return defaultValue;
    const raw = sessionStorage.getItem(key);
    return raw ? (JSON.parse(raw) as T) : defaultValue;
  } catch (e) {
    console.error('getSessionJSON error', e);
    return defaultValue;
  }
}
