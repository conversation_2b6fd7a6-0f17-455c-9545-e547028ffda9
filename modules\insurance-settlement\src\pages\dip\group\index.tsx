import { Card, Col, Row, Tabs, Slider, Space, Select, InputNumber } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import FeeChargeDistribution from '@/components/feeChargeDistribution';
import FeeChargeStatsDistribution from '@/components/feeChargeStatsDestribution';
import ChsBmFeeTable from './components/chsBm';
import './index.less';
import DetailTableModal from '@uni/components/src/detailed-table-modal';
import StatisticAnalysis from './components/statisticAnalysis';
import {
  SettleCompStatsByCliDeptColumns,
  SettleCompStatsByGrpColumns,
  SettleCompStatsByMedTeamColumns,
  TabCommonItems,
} from '../constants';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { isEmptyValues } from '@uni/utils/src/utils';
import { SearchOutlined } from '@ant-design/icons';
import IconBtn from '@uni/components/src/iconBtn/index';
import GradientChartAndTable from '@/components/gradientChartAndTable/index';
import { GrpDefaultOpts, GrpQuadrantAxisOpts } from '../optsConstants';
import DrawerCardInfo from '../components/drawerCardInfo/index';
import { mergeTableClickParams } from '@/utils/utils';
import { UniSelect } from '@uni/components/src/index';

const DipGroupAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, insurType } = globalState?.searchParams;
  const [selectedTableItem, setSelectedTableItem] = useState(undefined);
  const [activeKey, setActiveKey] = useState('Dip');
  const [selectOpts, setSelectOpts] = useState([]);
  const [requestParams, setRequestParams] = useState(null);

  const [drawerVisible, setDrawerVisible] = useState(undefined);

  // table click
  // useEffect(() => {
  //   Emitter.on(EventConstant.TABLE_ROW_CLICK, async ({ record, index }) => {
  //     setSelectedTableItem(record);
  //   });
  //   return () => {
  //     Emitter.off(EventConstant.TABLE_ROW_CLICK);
  //   };
  // }, [selectedTableItem, dateRange, hospCodes]);

  // tab 使用下拉框数据
  const {
    loading: getSettleCompStatsByEntityLoading,
    run: getSettleCompStatsByEntityReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        'Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByGrp',
        {
          method: 'POST',
          data: {
            ...data,
            DtParam: {
              Draw: 1,
              Start: 0,
              Length: 10000000,
            },
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.data?.length) {
            setSelectOpts(
              res.data.data?.map((d) => ({
                ...d,
                label: `${d?.ChsDrgCode} ${d?.ChsDrgName ?? '-'}`,
              })),
            );
            // 默认把第一个设置为selected
            if (!selectedTableItem) {
              setSelectedTableItem(
                _.maxBy(res?.data?.data, function (o) {
                  return o.PatCnt;
                }),
              );
            }
          } else {
            setSelectOpts([]);
          }
        }
      },
    },
  );

  useEffect(() => {
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(requestParams) &&
        globalState?.searchParams?.dateRange?.length)
    ) {
      let params = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        insurType,
      };
      setRequestParams(params);
      setTimeout(() => {
        getSettleCompStatsByEntityReq(params);
      }, 500);
    }
  }, [globalState?.searchParams]);

  useEffect(() => {
    Emitter.on(EventConstant.SEARCH_DOWN_DRILL, (value) => {
      if (value) {
        setSelectedTableItem(
          selectOpts?.find((d) => d?.VersionedChsDrgCode === value),
        );
        // 并且切换tab
        setActiveKey('statistic');
      }
    });
    return () => {
      Emitter.off(EventConstant.SEARCH_DOWN_DRILL);
    };
  }, [selectOpts, selectedTableItem]);

  const tabs = [
    {
      key: 'Dip',
      label: 'DIP病组构成',
      children: (
        <>
          <GradientChartAndTable
            args={{
              level: 'hosp',
              // type: 'dip',
              isDrgTable: true,
              clickable: false,
              cols: 'col-xl-24',
              title: '病组效率',
              category: 'ChsDrgName',
              columns: requestParams
                ? [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 60,
                      align: 'center',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <Space>
                            <IconBtn
                              type="details"
                              customIcon={<SearchOutlined />}
                              title="下钻"
                              onClick={(e) => {
                                e.stopPropagation();
                                Emitter.emit(
                                  EventConstant.SEARCH_DOWN_DRILL,
                                  record?.VersionedChsDrgCode,
                                );
                              }}
                            />
                            <IconBtn
                              type="details"
                              onClick={(e) => {
                                e.stopPropagation();
                                Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                  id: 'drg-group-settle-stats-by-chsdrg', // 要匹配到对应的DetailTableModal id
                                  title: record?.ChsDrgName,
                                  args: {
                                    ...requestParams,
                                    VersionedChsDrgCodes:
                                      record?.VersionedChsDrgCode
                                        ? [record?.VersionedChsDrgCode]
                                        : undefined,
                                  },
                                  type: 'dip',
                                  detailsUrl:
                                    'FundSupervise/LatestDipSettleStats/SettleDetails',
                                  dictData: globalState?.dictData, // 传入
                                });
                              }}
                            />
                          </Space>
                        );
                      },
                    },
                    ...SettleCompStatsByGrpColumns,
                  ]
                : [],
              defaultAxisOpt: GrpDefaultOpts,
              axisOpts: GrpQuadrantAxisOpts,
              api: 'Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByGrp',
              emitter: EventConstant.DRG_TABLE_ROW_CLICK,
            }}
            noChart
            requestParams={requestParams}
            // 后端分页
            isBackendPagi={true}
          />
        </>
      ),
    },
    {
      key: 'statistic',
      label: '支付指标统计',
      children: (
        <StatisticAnalysis type="grp" selectedTableItem={selectedTableItem} />
      ),
    },
    {
      key: 'fee_analysis',
      label: '费用构成分析',
      children: (
        <Tabs
          tabPosition="left"
          items={[
            {
              key: 'statsItems',
              label: '统计类别',
              children: (
                <>
                  <FeeChargeStatsDistribution
                    id="HospDrgFeePie"
                    level={'grp'}
                    title={'统计类别'}
                    selectedTableItem={selectedTableItem}
                    requestParams={{
                      ...requestParams,
                      VersionedChsDrgCodes:
                        selectedTableItem?.VersionedChsDrgCode
                          ? [selectedTableItem?.VersionedChsDrgCode]
                          : ['%'],
                    }}
                    api={`Api/FundSupervise/LatestDipSettleStats/FeeChargeDistribution`}
                  />
                  <ChsBmFeeTable
                    groupCode={selectedTableItem?.ChsDrgCode}
                    chargeTypeMode={'Stats'}
                  />
                </>
              ),
            },
            {
              key: 'tarItems',
              label: '收费项目类别',
              children: (
                <>
                  <FeeChargeDistribution
                    id="HospDrgFeePie"
                    level={'grp'}
                    title={'收费项目类别'}
                    selectedTableItem={selectedTableItem}
                    requestParams={{
                      ...requestParams,
                      VersionedChsDrgCodes:
                        selectedTableItem?.VersionedChsDrgCode
                          ? [selectedTableItem?.VersionedChsDrgCode]
                          : ['%'],
                    }}
                    api={`Api/FundSupervise/LatestDipSettleStats/FeeChargeDistribution`}
                  />
                  <ChsBmFeeTable
                    groupCode={selectedTableItem?.ChsDrgCode}
                    chargeTypeMode={'Med'}
                  />
                </>
              ),
            },
          ]}
        />
      ),
    },
    // dip 默认隐藏
    // {
    //   key: TabCommonItems.majorPerfDeptAnalysis.key,
    //   label: '学科横向对比',
    //   children: (
    //     <>
    //       <Row gutter={[16, 16]}>
    //         <Col span={24}>
    //           <GradientChartAndTable
    //             args={{
    //               level: 'hospCode',
    //               clickable: false,
    //               cols: 'col-xl-24',
    //               title: '学科对比分析',
    //               category: 'MajorPerfDeptName',
    //               columns: [
    //                 {
    //                   dataIndex: 'operation',
    //                   visible: true,
    //                   width: 40,
    //                   align: 'center',
    //                   fixed: 'left',
    //                   order: 1,
    //                   title: '',
    //                   render: (node, record, index) => {
    //                     return (
    //                       <IconBtn
    //                         type="details"
    //                         onClick={(e) => {
    //                           e.stopPropagation();
    //                           Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
    //                             title: `${record?.MajorPerfDeptName}`,
    //                             args: {
    //                               // 学科 特殊处理 需要按照record里面与requestParams合并传参
    //                               ...mergeTableClickParams(
    //                                 requestParams,
    //                                 record,
    //                                 ['HospCode'],
    //                               ),
    //                               versionedChsDrgCodes:
    //                                 selectedTableItem?.VersionedChsDrgCode
    //                                   ? [selectedTableItem?.VersionedChsDrgCode]
    //                                   : [],
    //                               MajorPerfDepts: record?.MajorPerfDept
    //                                 ? [record?.MajorPerfDept]
    //                                 : ['%'],
    //                             },
    //                             type: 'dip',
    //                             detailsUrl:
    //                               'FundSupervise/LatestDipSettleStats/SettleDetails',
    //                             dictData: globalState?.dictData, // 传入
    //                           });
    //                         }}
    //                       />
    //                     );
    //                   },
    //                 },
    //                 ...SettleCompStatsByCliDeptColumns,
    //               ],
    //               type: 'dip',
    //               selectedTableItem,
    //               api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByMajorPerfDept`,
    //             }}
    //             noChart={true}
    //             requestParams={requestParams}
    //           />
    //         </Col>
    //       </Row>
    //     </>
    //   ),
    // },
    {
      key: 'dept_analysis',
      label: '科室横向对比',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <GradientChartAndTable
              args={{
                level: 'hospCode',
                clickable: false,
                cols: 'col-xl-24',
                title: '科室横向对比',
                category: 'CliDeptName',
                columns: [
                  {
                    dataIndex: 'operation',
                    visible: true,
                    width: 40,
                    align: 'center',
                    fixed: 'left',
                    order: 1,
                    title: '',
                    render: (node, record, index) => {
                      return (
                        <IconBtn
                          type="details"
                          onClick={(e) => {
                            e.stopPropagation();
                            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                              title: `${record?.CliDeptName}`,
                              args: {
                                ...requestParams,
                                versionedChsDrgCodes:
                                  selectedTableItem?.VersionedChsDrgCode
                                    ? [selectedTableItem?.VersionedChsDrgCode]
                                    : ['%'],
                                CliDepts: record?.CliDept
                                  ? [record?.CliDept]
                                  : ['%'],
                              },
                              type: 'dip',
                              detailsUrl:
                                'FundSupervise/LatestDipSettleStats/SettleDetails',
                              dictData: globalState?.dictData, // 传入
                            });
                          }}
                        />
                      );
                    },
                  },
                  ...SettleCompStatsByCliDeptColumns,
                ],
                type: 'dip',
                selectedTableItem,
                api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByCliDept`,
              }}
              noChart={true}
              requestParams={{
                ...requestParams,
                versionedChsDrgCodes: selectedTableItem?.VersionedChsDrgCode
                  ? [selectedTableItem?.VersionedChsDrgCode]
                  : ['%'],
              }}
            />
          </Col>
        </Row>
      ),
    },
    {
      key: TabCommonItems.medTeamAnalysis.key,
      label: '医疗组横向对比',
      children: (
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <GradientChartAndTable
              args={{
                clickable: false,
                cols: 'col-xl-24',
                title: '医疗组对比分析',
                category: 'MedTeamName',
                columns: [
                  {
                    dataIndex: 'operation',
                    visible: true,
                    width: 40,
                    align: 'center',
                    fixed: 'left',
                    order: 1,
                    title: '',
                    render: (node, record, index) => {
                      return (
                        <IconBtn
                          type="details"
                          onClick={(e) => {
                            e.stopPropagation();
                            Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                              id: 'dip-med-team-settle-stats-by-med-team', // 要匹配到对应的DetailTableModal id
                              title: record?.MedTeamName,
                              args: {
                                ...requestParams,
                                versionedChsDrgCodes:
                                  selectedTableItem?.VersionedChsDrgCode
                                    ? [selectedTableItem?.VersionedChsDrgCode]
                                    : [],
                                MedTeams: record?.MedTeam
                                  ? [record?.MedTeam]
                                  : [''],
                              },
                              type: 'dip',
                              detailsUrl:
                                'FundSupervise/LatestDipSettleStats/SettleDetails',
                              dictData: globalState?.dictData, // 传入
                            });
                          }}
                        />
                      );
                    },
                  },
                  ...SettleCompStatsByMedTeamColumns,
                ],
                extraColumnsProcessor: (totalColumns) => {
                  return totalColumns?.map((d) => ({
                    ...d,
                    onCell: (record, rowIndex) => {
                      if (record?.KeyFactorName === d?.data) {
                        return {
                          style: {
                            color: '#1890ff',
                            fontWeight: 'bold',
                          },
                        };
                      }
                    },
                  }));
                },
                api: `Api/FundSupervise/LatestDipSettleStats/SettleCompStatsByMedTeam`,
                level: 'hospCode',
                type: 'dip',
                selectedTableItem,
              }}
              noChart={true}
              requestParams={{
                ...requestParams,
                versionedChsDrgCodes: selectedTableItem?.VersionedChsDrgCode
                  ? [selectedTableItem?.VersionedChsDrgCode]
                  : ['%'],
              }}
            />
          </Col>
        </Row>
      ),
    },
  ];

  return (
    <>
      <Card>
        <Tabs
          items={tabs}
          activeKey={activeKey}
          onChange={(activeKeys) => {
            setActiveKey(activeKeys);
          }}
          tabBarExtraContent={{
            right: activeKey !== 'Dip' && (
              <>
                <div
                  className="d-flex"
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  病组：
                  <UniSelect
                    width={300}
                    showSearch={true}
                    dataSource={selectOpts}
                    value={selectedTableItem?.VersionedChsDrgCode}
                    onChange={(value) => {
                      setSelectedTableItem(
                        selectOpts?.find(
                          (d) => d?.VersionedChsDrgCode === value,
                        ),
                      );
                    }}
                    allowClear={false}
                    optionNameKey={'label'}
                    optionValueKey={'VersionedChsDrgCode'}
                    enablePinyinSearch={true}
                  />
                </div>
              </>
            ),
          }}
        />
      </Card>
      <DetailTableModal
        dictData={globalState.dictData}
        detailAction={(record) => {
          // 这里替代内部 操作 onClick
          setDrawerVisible({
            hisId: record?.HisId,
            type: 'dip',
          });
        }}
      />
      <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </>
  );
};

export default DipGroupAnalysis;
