import dayjs from 'dayjs';
import Constants from './constants';
import { DictionaryItem } from '@uni/commons/src/interfaces';
import { notification } from 'antd';
import uniq from 'lodash/uniq';

export const getHeaderIds = (access: any, menuData: any[], headerMenu: any) => {
  let hasAccessUrls = Object.keys(access || {})?.filter(
    (item) => access[item] === true,
  );
  const accessHeaderMenuKeys = uniq(
    menuData
      ?.filter((item) => hasAccessUrls.includes(item?.route))
      ?.map((item) => item?.headerMenuKey)
      ?.filter((item) => item),
  );

  return headerMenuKeysToHeaderIds(accessHeaderMenuKeys, headerMenu);
};

export const headerMenuKeysToHeaderIds = (
  headerMenuKeys: string[],
  headerMenus: any[],
) => {
  let headerIds = [];
  headerMenuKeys?.forEach((key) => {
    let currentHeaderMenu = headerMenus?.find((item) =>
      item?.key?.includes(key),
    );

    if (currentHeaderMenu) {
      headerIds.push(currentHeaderMenu?.id);
    }
  });

  return uniq(headerIds);
};

const capitalizeFirstLetter = (string) => {
  if (string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  return string;
};

export function valueFormatter(value, dataType = undefined, scale = 2) {
  if (value === null || value === undefined) return value;

  switch (capitalizeFirstLetter(dataType)) {
    case Constants.DateFormatType.double: {
      return parseFloat((+value).toFixed(scale));
    }
    case Constants.DateFormatType.int32:
      return value;
    case Constants.DateFormatType.string:
      return value;

    case Constants.DateFormatType.Boolean:
      return value ? '是' : '否';

    case Constants.DateFormatType.DateTime:
    case Constants.DateFormatType.Datetime: {
      return dayjs(value).format('YYYY年MM月DD日 HH:mm:ss');
    }

    case Constants.DateFormatType.Date: {
      return dayjs(value).format('YYYY年MM月DD日');
    }

    case Constants.DateFormatType.DateByDash: {
      return dayjs(value).format('YYYY-MM-DD');
    }

    case Constants.DateFormatType.DateHourByDash: {
      return dayjs(value).format('YYYY-MM-DD HH:mm');
    }

    case Constants.DateFormatType.DateTimeByDash: {
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
    }

    case Constants.DateFormatType.MonthDate:
    case Constants.DateFormatType.Month: {
      return dayjs(value).format('YYYY年MM月');
    }

    case Constants.DateFormatType.Decimal: {
      // return parseFloat((+value).toFixed(scale));
      return (+value).toFixed(scale);
    }

    case Constants.DateFormatType.Currency: {
      if (Math.abs(value) >= 10000) {
        return (+value / 10000).toFixed(3) + '万';
      }
      return (+value).toFixed(scale);
    }

    case Constants.DateFormatType.Percent: {
      return (+value * 100).toFixed(scale) + '%';
    }

    case Constants.DateFormatType.Permillage: {
      return (+value * 1000).toFixed(scale) + '‰';
    }
    case Constants.DateFormatType.String:
      return value;
    case Constants.DateFormatType.CurrencyWithoutSuffix: {
      if (Math.abs(value) >= 10000) {
        return (+value / 10000).toFixed(3);
      }
      return (+value).toFixed(scale);
    }

    case Constants.DateFormatType.RMB: {
      return '￥' + parseFloat((+value).toFixed(scale));
    }

    default:
      return value;
  }
}

export function valueNullOrUndefinedReturnDash(
  value?: any,
  dataType?: string,
  dataScale?: number,
  exportNoTranslateTypes?: string[],
) {
  if (value === null || value === undefined) {
    return '-';
  }

  if (dataType) {
    if (!isEmptyValues(exportNoTranslateTypes)) {
      if (exportNoTranslateTypes?.includes(dataType)) {
        return value;
      }
    }

    return valueFormatter(value, dataType, dataScale);
  }

  return value;
}

export const getDefaultItemByDataSource = (dataSource?: DictionaryItem[]) => {
  if (dataSource && dataSource?.length > 0) {
    return dataSource?.find((item) => item?.IsDefault);
  }

  return undefined;
};

export const isEmptyValues = (value) => {
  return (
    value === undefined ||
    value === null ||
    (typeof value === 'number' && isNaN(value)) ||
    (typeof value === 'object' && Object.keys(value).length === 0) ||
    (typeof value === 'string' && value.trim()?.length) === 0
  );
};

export const showNotification = (key: string, title: string) => {
  notification.warning({
    message: title,
    description: '请检查筛选参数',
    key: key,
  });
};

const dataItemWithColumnModuleProcessor = (
  dictionaryData: any,
  module,
  dataItem,
) => {
  if (dictionaryData?.[module]) {
    let dictionaryItem = dictionaryData?.[module]?.find(
      (item) => item.Code === dataItem,
    );
    if (dictionaryItem) {
      return dictionaryItem?.Name || dataItem;
    }
  }

  return dataItem;
};

export function valueNullOrUndefinedReturnDashWithDictionaryModule(
  record: any,
  columnItem: any,
  dictionaryData: any,
) {
  let dataItem = record[columnItem.dataIndex];
  if (columnItem?.isExtraProperty === true) {
    dataItem = record?.['ExtraProperties']?.[columnItem.dataIndex];
  }
  if (columnItem?.dictionaryModule) {
    let currentDictionaryData = dictionaryData;
    if (columnItem?.dictionaryModuleGroup) {
      currentDictionaryData =
        dictionaryData?.[columnItem?.dictionaryModuleGroup];
    }

    dataItem = dataItemWithColumnModuleProcessor(
      currentDictionaryData,
      columnItem?.dictionaryModule,
      dataItem,
    );
  }

  return valueNullOrUndefinedReturnDash(
    dataItem,
    columnItem['dataType'],
    columnItem['scale'],
  );
}

export const getAllLeafMenuItem = (menuData: any[], headerRoute?: string) => {
  let leafItem = [];
  function traverse(acc: string[], node: any) {
    if (node.children) return node.children.reduce(traverse, acc);
    acc.push(node?.route);
    return acc;
  }

  menuData
    ?.filter((item) => {
      if (headerRoute) {
        return item?.route === headerRoute;
      } else {
        return true;
      }
    })
    ?.forEach((nodeItem) => {
      leafItem = leafItem.concat(traverse([], nodeItem));
    });

  return leafItem?.filter((route) => {
    if (headerRoute) {
      return route !== headerRoute;
    } else {
      return true;
    }
  });
};

export const clearScrollingTitleTimeout = () => {
  if (global['scrollingTitleTimeout']) {
    clearTimeout(global['scrollingTitleTimeout']);
  }
};
// 滚动document.title
export const scrollTitle = (title: string, timing: number = 500) => {
  document.title = title;
  global['scrollingTitleTimeout'] = setTimeout(() => {
    scrollTitle(title.substring(1) + title.substring(0, 1));
  }, timing);
};

// 判断是否系带指定的值 有的话将其输出 (现在只支持 string 作为 targetValue)
export const getKeysWithTargetValue = (
  obj: { [key: string]: any },
  targetValue: string,
): string[] => {
  console.log(obj, targetValue);
  if (isEmptyValues(obj)) {
    return [];
  }
  let keys: string[] = [];

  const checkValue = (value: any, parentKey: string) => {
    if (typeof value === 'string' && value === targetValue) {
      keys.push(parentKey);
    } else if (Array.isArray(value)) {
      if (value.includes(targetValue)) {
        keys.push(parentKey);
      } else {
        value.forEach((item) => {
          if (typeof item === 'object' && item !== null) {
            checkValue(item, parentKey);
          }
        });
      }
    } else if (typeof value === 'object' && value !== null) {
      if (Object.values(value).some((val) => val === targetValue)) {
        keys.push(parentKey);
      } else {
        Object.entries(value).forEach(([key, val]) =>
          checkValue(val, `${parentKey}.${key}`),
        );
      }
    }
  };

  Object.entries(obj).forEach(([key, value]) => checkValue(value, key));

  return keys;
};

export const generateUniqueNumberId = (() => {
  let counter = 0;

  return () => {
    const timestamp = Date.now(); // 13位
    const randomPart = Math.floor(Math.random() * 1000); // 3位随机数
    const counterPart = ++counter % 1000; // 3位计数器

    // 组合成一个19位的大数字
    return timestamp * 1000000 + randomPart * 1000 + counterPart;
  };
})();
