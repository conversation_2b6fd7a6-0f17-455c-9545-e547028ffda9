import { UniTable } from '@uni/components/src';
import { categoryColumns } from '@/pages/configuration/base/categoryColumns';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Space,
  TableProps,
  message,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useRequest, useSelector } from 'umi';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import CategoryAdd from '@/pages/configuration/base/components/icde-oper-category-add';
import { OperDictionaryItem } from '@/pages/configuration/base/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import UniEditableTable from '@uni/components/src/table/edittable';
import { useDebounce } from 'ahooks';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { useUpdateEffect } from 'ahooks';

interface OperationDictionaryProps {}

const OperationDictionary = (props?: OperationDictionaryProps) => {
  const [form] = Form.useForm();
  const ref = useRef<any>();

  const dictData = useSelector((state) => state?.uniDict?.dictData);
  // keywords
  const [searchKeywords, setSearchKeywords] = useState('');
  const debouncedKeywords = useDebounce(searchKeywords, { wait: 1000 });

  // const {
  //   globalState: { dictData },
  // } = useModel('@@qiankunStateForSlave');

  // const dictData = globalState?.dictData?.['Dmr'];

  const [operDictionaryAdd, setOperDictionaryAdd] = useState(false);

  const [operDictionaryTableDataSource, setOperDictionaryTableDataSource] =
    useState([]);

  const [operDictionaryColumns, setOperDictionaryColumns] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    if (operDictionaryColumns.length) {
      operationDictionaryReq(
        backPagination?.current || 1,
        backPagination?.pageSize || 10,
      );
    }
  }, [debouncedKeywords, operDictionaryColumns]);

  useEffect(() => {
    if (dictData && !operDictionaryColumns.length) {
      operationConfigurationColumnsReq();
    }
  }, [dictData]);

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    operationDictionaryReq(pagi.current, pagi.pageSize);
  };
  // handle dmrCardsSummarySelectedKey
  const dmrCardsSummarySelectedKeyHandler = (data) => {
    if (dmrCardsSummarySelectedKey && dmrCardsSummarySelectedKey !== '999') {
      data['CategoryLevel'] = dmrCardsSummarySelectedKey;
    }
    return data;
  };
  const { loading: operationDictionaryLoading, run: operationDictionaryReq } =
    useRequest(
      (current, pageSize) => {
        let data = {
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          Keyword: debouncedKeywords,
        };
        data = dmrCardsSummarySelectedKeyHandler(data);
        return uniCommonService('Api/Sys/CodeSys/GetStdIcdCategorysByPage', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (
          response: RespVO<TableResp<any, OperDictionaryItem>>,
        ) => {
          if (response.code === 0 && response?.statusCode === 200) {
            let tableDataSource = response?.data?.data.slice();
            setOperDictionaryTableDataSource(
              tableDataSource.map((record) => {
                record['itemId'] = uuidv4();
                return record;
              }),
            );

            setBackPagination({
              ...backPagination,
              total: response?.data?.recordsFiltered || 0,
            });
          } else {
            setOperDictionaryTableDataSource([]);
          }
        },
      },
    );

  const { run: operationConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetStdIcdCategorysByPage', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setOperDictionaryColumns(
            tableColumnBaseProcessor(
              categoryColumns('icde'),
              response?.data?.Columns,
            ),
          );
        } else {
          setOperDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: operationUpsertLoading, run: operationUpsertReq } =
    useRequest(
      (values) => {
        let data = {};
        data = {
          ...values,
        };
        return uniCommonService('Api/Sys/CodeSys/UpsertStdIcdCategory', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.statusCode === 200) {
            setOperDictionaryAdd(false);

            operationDictionaryReq(
              backPagination?.current,
              backPagination?.pageSize,
            );
          }
        },
      },
    );

  const onOperationDictionaryItemAdd = (values: any) => {
    operationUpsertReq(values);
  };

  const { run: icdeDeleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        Id: values.Id,
      };
      return uniCommonService('Api/Sys/CodeSys/DeleteStdIcdCategory', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
        }
      },
      onSuccess: (response, params) => {
        operationDictionaryReq(
          backPagination?.current,
          backPagination?.pageSize,
        );
      },
    },
  );

  useEffect(() => {
    Emitter.on(ConfigurationEvents.DMR_ICDE_DELETE, (data) => {
      if (data?.index > -1) {
        icdeDeleteReq(data.record);
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.DMR_ICDE_DELETE);
    };
  }, [operDictionaryTableDataSource]);

  interface DmrManagementSummaryItemProps {
    className?: string;
    label?: string;
    count?: number;
    itemKey?: string;
    onClick?: () => any;
  }

  const DmrManagementSummaryItem = (props: DmrManagementSummaryItemProps) => {
    return (
      <div
        className={`dmr-management-summary-item-container ${props?.className}`}
        onClick={() => {
          props?.onClick && props?.onClick();
        }}
      >
        <span className={'label'}>{props?.label}</span>
      </div>
    );
  };

  const [dmrCardsSummarySelectedKey, setDmrCardsSummarySelectedKey] =
    useState('999');

  useUpdateEffect(() => {
    let pagination = {
      ...backPagination,
      current: 1,
      total: 0,
    };
    setBackPagination(pagination);
    operationDictionaryReq(pagination?.current, pagination?.pageSize);
  }, [dmrCardsSummarySelectedKey]);

  return (
    <>
      <Card
        title="诊断类目亚目列表"
        extra={
          <Space>
            <ConfigExcelTemplateHandler
              downloadTemplateApiObj={{
                apiUrl: 'Api/Sys/CodeSys/GetStdIcdCategoryExcelTemplate',
              }}
              downloadPostData={{
                exportName: '诊断类目亚目列表',
              }}
              uploadXlsxApiObj={{
                apiUrl: 'Api/Sys/CodeSys/UploadStdIcdCategoryExcelFile',
                onSuccess: () => {
                  operationDictionaryReq(
                    backPagination?.current,
                    backPagination?.pageSize,
                  );
                },
              }}
              uploadPostData={{}}
            />
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                setOperDictionaryAdd(true);
                form.setFieldValue('IsValid', true);
              }}
            >
              新增
            </Button>
          </Space>
        }
      >
        <UniEditableTable
          actionRef={ref}
          id={`operation-dictionary-table`}
          className={'operation-dictionary-table'}
          rowKey={'itemId'}
          scroll={{ x: 'max-content', y: 540 }}
          headerTitle={
            <Input.Search
              allowClear
              placeholder="请输入编码或名称"
              value={searchKeywords}
              onChange={(e) => setSearchKeywords(e.target.value)}
            />
          }
          toolBarRender={() => [
            <div className="dmr-management-summary-container d-flex">
              {
                // dictData?.CategoryLevel &&
                [
                  { Name: '全部', Code: '999' },
                  { Name: '类目', Code: 'Category' },
                  { Name: '亚目', Code: 'SubCategory' },
                  // ...dictData?.CategoryLevel,
                ]?.map((item) => {
                  return (
                    <DmrManagementSummaryItem
                      className={
                        item?.Code === dmrCardsSummarySelectedKey
                          ? 'card-selected'
                          : ''
                      }
                      label={item?.Name}
                      itemKey={item?.Code}
                      onClick={() => setDmrCardsSummarySelectedKey(item?.Code)}
                    />
                  );
                })
              }
            </div>,
          ]}
          backendPagination
          dictionaryData={dictData}
          bordered={true}
          loading={operationDictionaryLoading || operationUpsertLoading}
          columns={operDictionaryColumns}
          value={operDictionaryTableDataSource}
          clickable={false}
          pagination={backPagination}
          onTableChange={backTableOnChange}
          recordCreatorProps={false}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              console.log(rowKey, data, row);
              operationUpsertReq(data);
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />
      </Card>
      <Modal
        title="新增"
        open={operDictionaryAdd}
        onOk={() => {
          onOperationDictionaryItemAdd(form.getFieldsValue());
        }}
        onCancel={() => {
          setOperDictionaryAdd(false);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById('operation-dictionary-table')}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <CategoryAdd form={form} type="icde" />
      </Modal>
    </>
  );
};

export default OperationDictionary;
