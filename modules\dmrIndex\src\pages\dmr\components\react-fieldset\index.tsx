import React, {
  ReactNode,
  FieldsetHTMLAttributes,
  isValidElement,
  cloneElement,
  useMemo,
} from 'react';

type Mode = 'all' | 'partial' | 'none';

const INTERACTIVE_TAGS = new Set(['input', 'textarea', 'select', 'button']);

interface MyFieldsetProps
  extends Omit<FieldsetHTMLAttributes<HTMLFieldSetElement>, 'disabled'> {
  /** = viewMode：true 时无条件全禁用（白名单也禁用） */
  forceAllDisabled?: boolean;
  /** = (!viewMode && canEditMainTable === false)：白名单容器内可编辑，其余禁用 */
  partialDisabled?: boolean;
  /** 白名单容器 id（这些容器及后代在 partial 模式下可编辑） */
  whitelistIds?: string[];
  children?: ReactNode;
}

/** 非字符串（函数/类）组件：默认当作“可交互”——大多数 antd/自定义控件都吃 disabled */
function isCompositeInteractive(el: React.ReactElement): boolean {
  return typeof el.type !== 'string';
}

/** DOM 交互元素判定 */
function isDomInteractive(el: React.ReactElement): boolean {
  return typeof el.type === 'string' && INTERACTIVE_TAGS.has(el.type);
}

/** DOM 是否 contentEditable */
function isContentEditable(el: React.ReactElement): boolean {
  const ce = (el.props as any)?.contentEditable;
  return ce === '' || ce === true || ce === 'true';
}

function patchDisabled(
  children: ReactNode,
  mode: Mode,
  insideWhitelist: boolean,
  whitelist: Set<string>,
): ReactNode {
  if (mode === 'none') return children;

  return React.Children.map(children, (child) => {
    if (!isValidElement(child)) return child;

    const props = child.props ?? {};
    const currentId: string | undefined = props.id;
    const nowInsideWhitelist =
      insideWhitelist || (currentId ? whitelist.has(currentId) : false);

    // 先递归 children，确保能深入到最深层控件
    let nextChildren = props.children as ReactNode;
    if (nextChildren !== undefined) {
      const patched = patchDisabled(
        nextChildren,
        mode,
        nowInsideWhitelist,
        whitelist,
      );
      if (patched !== nextChildren) nextChildren = patched;
    }

    // 根据模式计算目标禁用值
    const shouldDisable =
      mode === 'all' ? true : mode === 'partial' ? !nowInsideWhitelist : false;

    // 针对不同节点类型决定如何施加禁用
    const isComposite = isCompositeInteractive(child);
    const isDomCtrl = isDomInteractive(child);
    const isCE =
      !isDomCtrl && typeof child.type === 'string' && isContentEditable(child);

    const extra: Record<string, unknown> = {};
    let changed = false;

    if (isDomCtrl) {
      if ((props as any).disabled !== shouldDisable) {
        extra.disabled = shouldDisable;
        changed = true;
      }
    } else if (isComposite) {
      // 把 disabled 强行塞给复合组件（antd/自定义多数会消费它）
      if ((props as any).disabled !== shouldDisable) {
        extra.disabled = shouldDisable;
        changed = true;
      }
    } else if (isCE) {
      // 兜底：contentEditable → 关闭编辑、去焦点、语义禁用
      if ((props as any).contentEditable !== false) {
        extra.contentEditable = false;
        changed = true;
      }
      if ((props as any)['aria-disabled'] !== shouldDisable) {
        (extra as any)['aria-disabled'] = shouldDisable;
        changed = true;
      }
      if (shouldDisable && (props as any).tabIndex !== -1) {
        extra.tabIndex = -1;
        changed = true;
      }
    }

    // children 变化也需要 clone
    if (nextChildren !== props.children) {
      extra.children = nextChildren;
      changed = true;
    }

    if (!changed) return child;
    return cloneElement(child, extra);
  });
}

const ReactFieldset: React.FC<MyFieldsetProps> = React.memo(
  ({
    forceAllDisabled = false,
    partialDisabled = false,
    whitelistIds = ['diagnosisInsurTable', 'operationInsurTable'],
    children,
    ...rest
  }) => {
    const mode: Mode = forceAllDisabled
      ? 'all'
      : partialDisabled
      ? 'partial'
      : 'none';
    const whitelistSet = useMemo(() => new Set(whitelistIds), [whitelistIds]);

    const processedChildren = useMemo(
      () => patchDisabled(children, mode, false, whitelistSet),
      [children, mode, whitelistSet],
    );

    // 这里非常关键：不要把原生 disabled 给到 fieldset
    return <fieldset {...rest}>{processedChildren}</fieldset>;
  },
);

export default ReactFieldset;
