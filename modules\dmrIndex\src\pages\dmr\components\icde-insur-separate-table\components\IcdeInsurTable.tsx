import React, { useContext, useEffect, useState } from 'react';
import { Form, Modal } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { getIcdeInsurColumns } from '../columns/icdeInsurColumns';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import { IcdeOperationReadonlyItem } from '@uni/grid/src/components/icde-oper-input/input';
import { isEmptyValues, generateUniqueNumberId } from '@uni/utils/src/utils';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import GridItemContext from '@uni/commons/src/grid-context';
import {
  getSessionJSON,
  INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY,
} from '../utils/session';

interface IcdeInsurTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];
  underConfiguration?: boolean;
  onChange?: (value: any) => void;
}

const icdeCopyKeys =
  (window as any).externalConfig?.['dmr']?.icdeCopyKeys ?? [];

const copyKeys = !isEmptyValues(icdeCopyKeys)
  ? icdeCopyKeys
  : ['IcdeCond', 'IcdeOutcome'];

const icdeOperFirstMain =
  (window as any).externalConfig?.['dmr']?.icdeOperFirstMain ?? false;

const icdeCopyFocusKey =
  (window as any).externalConfig?.['dmr']?.icdeCopyFocusKey ?? undefined;
const icdeDeleteConfirm =
  (window as any).externalConfig?.['dmr']?.icdeDeleteConfirm ?? false;
const icdeMoveMainConfirm =
  (window as any).externalConfig?.['dmr']?.icdeMoveMainConfirm ?? false;

const setFirstItemIsMainIcde = (tableData) => {
  if (tableData?.length > 0) {
    tableData?.forEach((item, index) => {
      if (index === 0) {
        item['IsMain'] = true;
      } else {
        item['IsMain'] = false;
      }
    });
  }
};

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_ICDE_INSUR_ADD, event.target.id);
  },
  COPY: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);
    let index = parseInt(indexString);

    if (index >= 0) {
      Emitter.emit(EventConstant.DMR_ICDE_INSUR_COPY, {
        id: undefined,
        index: index,
      });
    }
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_ICDE_INSUR_DELETE, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#diagnosisInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('diagnosisInsurTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    console.log('DOWN', event);
    Emitter.emit(getArrowUpDownEventKey('diagnosisInsurTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};

const IcdeInsurTable = (props: IcdeInsurTableProps) => {
  const itemRef = React.useRef<any>();
  const [form] = Form.useForm();

  // 用于触发强制rerender
  const [currentTime, setCurrentTime] = useState(undefined);

  // 获取联动逻辑状态
  const gridContext = useContext(GridItemContext);
  const insurSeparateTableLogic =
    getSessionJSON(INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY) ??
    gridContext?.extra?.insurSeparateTableLogic;
  const viewMode = getSessionJSON('dmrViewMode') ?? gridContext?.viewMode;
  // 监听医保表格数据变化（用于触发重新渲染）
  const insurTableWatchData =
    Form.useWatch('diagnosisInsurTable', props?.form) ?? [];

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);
  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // 更新行选择状态的函数
  const updateRowSelectionState = () => {
    const tableData = props?.form?.getFieldValue('diagnosis-insur-table') || [];
    const validRows = tableData.filter((row) => row.id !== 'ADD');

    const selectedRows = validRows.filter((row) => {
      const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
      return rowSelectionValue === true;
    });

    const allSelected =
      selectedRows.length === validRows.length && validRows.length > 0;
    const indeterminate =
      selectedRows.length > 0 && selectedRows.length < validRows.length;
    const hasSelection = selectedRows.length > 0;

    Emitter.emit(`DMR_ROW_SELECTION_STATE_UPDATE_diagnosisInsurTable`, {
      allSelected: allSelected,
      indeterminate: indeterminate,
    });

    // 通知批量删除按钮状态
    Emitter.emit(`DMR_ROW_SELECTION_BATCH_UPDATE_diagnosisInsurTable`, {
      hasSelection: hasSelection,
    });
  };

  useEffect(() => {
    updateRowSelectionState();
    // setTableDataSourceSize(insurTableWatchData?.length);
  }, [insurTableWatchData]);

  const lineUpDownEvents = {
    LINE_UP: (event) => {
      console.log('LINE_UP', event);
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('diagnosisInsurTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue(
        'diagnosis-insur-table',
      ).length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('diagnosisInsurTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  // 使用医保表格专用的列定义
  useEffect(() => {
    const base = getIcdeInsurColumns(insurSeparateTableLogic);
    const columns = mergeColumnsInDmrTable(
      props?.columns,
      base,
      'IcdeInsurTable',
    );
    setTableColumns(columns);
  }, [props?.columns, insurSeparateTableLogic?.canEditInsurTable]);

  useEffect(() => {
    if (insurSeparateTableLogic?.canEditInsurTable) {
      setTimeout(() => {
        waitFocusElementRefocusBySelector(waitFocusId);
      }, 100);
    }
  }, [insurSeparateTableLogic?.canEditInsurTable, waitFocusId]);

  useEffect(() => {
    // 监听主表格同步事件，用于触发医保表格重新渲染
    Emitter.on(EventConstant.DMR_ICDE_INSUR_TABLE_SYNC, (data) => {
      console.log('收到主表格同步事件:', data);
      // 触发重新渲染 - 通过更新状态来强制重新渲染
      setCurrentTime(Date.now());
      // 这里可以通过更新form来触发重新渲染，或者使用forceUpdate等方式
    });

    // delete事件
    Emitter.on(getDeletePressEventKey('diagnosisInsurTable'), (itemId) => {
      // key 包含 index 和 其他的东西
      console.log('diagnosisInsurTable', itemId);
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);

      let clearKeys = [key];
      // if (clearKeysMap[key]) {
      //   clearKeys = clearKeysMap[key];
      // }
      clearValuesByKeys(clearKeys, index);

      // 定位到当前这个
      setTimeout(() => {
        document.getElementById(itemId)?.focus();
      }, 100);
    });

    Emitter.on(EventConstant.DMR_ICDE_INSUR_ADD, (focusId?: string) => {
      let rowData = {
        id: generateUniqueNumberId(),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      let tableData = props?.form?.getFieldValue('diagnosis-insur-table');

      tableData.splice(tableData.length, 0, rowData);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      props?.form?.setFieldValue('diagnosis-insur-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-insur-table');

      setWaitFocusId(
        `div[id=diagnosisInsurTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setCurrentTime(Date.now());
    });

    // 处理全选/反选事件
    Emitter.on(`DMR_ROW_SELECTION_SELECT_ALL_diagnosisInsurTable`, (data) => {
      const tableData =
        props?.form?.getFieldValue('diagnosis-insur-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 批量更新所有行的选中状态
      validRows.forEach((row) => {
        form.setFieldValue([row.id, 'RowSelection'], data.checked);
      });

      // 触发表单值变化事件
      triggerFormValueChangeEvent('diagnosis-insur-table');

      // 通知选中状态更新
      updateRowSelectionState();
    });

    // 处理单个行选择变化事件
    Emitter.on(`DMR_ROW_SELECTION_ITEM_CHANGE_diagnosisInsurTable`, (data) => {
      // 延迟更新状态，确保表单值已经更新
      setTimeout(() => {
        updateRowSelectionState();
      }, 150);
    });

    // 处理批量删除事件
    Emitter.on(`DMR_BATCH_DELETE_diagnosisInsurTable`, () => {
      const tableData =
        props?.form?.getFieldValue('diagnosis-insur-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 获取选中的行索引
      const selectedIndexes = [];
      validRows.forEach((row, index) => {
        const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
        if (rowSelectionValue === true) {
          selectedIndexes.push(index);
        }
      });

      if (selectedIndexes.length > 0) {
        // 从后往前删除，避免索引变化问题
        const sortedIndexes = selectedIndexes.sort((a, b) => b - a);
        let newTableData = [...tableData];

        sortedIndexes.forEach((index) => {
          newTableData.splice(index, 1);
        });

        // TODO 设定主诊为第一个
        if (icdeOperFirstMain && newTableData.length > 0) {
          setFirstItemIsMainIcde(newTableData);
        }

        // 更新form
        props?.form?.setFieldValue(
          'diagnosis-insur-table',
          cloneDeep(newTableData),
        );
        triggerFormValueChangeEvent('diagnosis-insur-table');
        setCurrentTime(Date.now());

        // 延迟更新选择状态
        setTimeout(() => {
          updateRowSelectionState();
        }, 100);
      }
    });

    Emitter.on(EventConstant.DMR_ICDE_INSUR_DELETE, (index) => {
      if (icdeDeleteConfirm) {
        Modal.confirm({
          title: `确定删除第${index + 1} 条诊断数据？`,
          content: '',
          onOk: () => {
            onIcdeItemDelete(index);
          },
          getContainer: () => document.getElementById('dmr-main-container'),
        });
      } else {
        onIcdeItemDelete(index);
      }
    });

    Emitter.on(EventConstant.DMR_ICDE_INSUR_COPY, (payload) => {
      let tableData = props?.form?.getFieldValue('diagnosis-insur-table');
      let currentCopyItem = payload?.id
        ? form.getFieldValue(payload?.id)
        : tableData?.[payload?.index];
      let index = payload?.index;

      let copiedItem = {
        id: generateUniqueNumberId(),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      copyKeys?.forEach((key) => {
        copiedItem[key] = currentCopyItem[key];
      });

      tableData.splice(index + 1, 0, copiedItem);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      if (!isEmptyValues(icdeCopyFocusKey)) {
        setWaitFocusId(
          `div[id=diagnosisInsurTable] tbody > tr:nth-child(${
            index + 2
          }) > td input[id*=${icdeCopyFocusKey}]`,
        );
      } else {
        setWaitFocusId(
          `div[id=diagnosisInsurTable] tbody > tr:nth-child(${
            index + 2
          }) > td input`,
        );
      }
      // 更新form
      props?.form?.setFieldValue('diagnosis-insur-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-insur-table');
      setCurrentTime(Date.now());
    });

    Emitter.on(getArrowUpDownEventKey('diagnosisInsurTable'), (payload) => {
      let type = payload?.type;
      const icdeDataSource = props?.form?.getFieldValue(
        'diagnosis-insur-table',
      );
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > icdeDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('diagnosisInsurTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off(EventConstant.DMR_ICDE_INSUR_TABLE_SYNC);
      Emitter.off(EventConstant.DMR_ICDE_INSUR_ADD);
      Emitter.off(EventConstant.DMR_ICDE_INSUR_DELETE);
      Emitter.off(EventConstant.DMR_ICDE_INSUR_COPY);
      Emitter.off(`DMR_ROW_SELECTION_SELECT_ALL_diagnosisInsurTable`);
      Emitter.off(`DMR_ROW_SELECTION_ITEM_CHANGE_diagnosisInsurTable`);
      Emitter.off(`DMR_BATCH_DELETE_diagnosisInsurTable`);

      Emitter.off(getDeletePressEventKey('diagnosisInsurTable'));
      Emitter.off(getArrowUpDownEventKey('diagnosisInsurTable'));
    };
  }, [insurSeparateTableLogic?.canEditInsurTable]);

  const onIcdeItemDelete = (index: number) => {
    if (index > -1) {
      // 检查权限
      const canEditInsurTable =
        insurSeparateTableLogic?.canEditInsurTable ?? true;
      if (!canEditInsurTable) {
        return;
      }

      let tableData = props?.form?.getFieldValue('diagnosis-insur-table');
      tableData.splice(index, 1);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainIcde(tableData);
      }

      // 更新form
      props?.form?.setFieldValue('diagnosis-insur-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('diagnosis-insur-table');

      // 删除的时候 给出当前那个选中的
      // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
      // 表格中不存在即写第0个的icdeName 建议写死
      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=diagnosisInsurTable] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setCurrentTime(Date.now());
    }
  };

  const clearValuesByKeys = (keys, index) => {
    const icdeDataSource = props?.form?.getFieldValue('diagnosis-insur-table');
    let formItemId = icdeDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('diagnosis-insur-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });

    props?.form?.setFieldValue('diagnosis-insur-table', cloneDeep(tableData));
    triggerFormValueChangeEvent('diagnosis-insur-table');
  };

  console.log(
    'diagnosisInsurTable tableData',
    props?.form?.getFieldValue('diagnosis-insur-table'),
  );

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      formItemContainerClassName={'form-content-item-container'}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'diagnosisInsurTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      dataSource={(() => {
        const canEditInsurTable =
          insurSeparateTableLogic?.canEditInsurTable ?? true;
        const baseDataSource = (
          props?.form?.getFieldValue('diagnosis-insur-table') ?? []
        )
          ?.filter((item) => item?.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = generateUniqueNumberId();
            }
            return item;
          });
        // 只有有编辑权限的用户才显示ADD行
        return canEditInsurTable
          ? baseDataSource?.concat({ id: 'ADD' })
          : baseDataSource;
      })()}
      rowKey={'id'}
      onDragEndPre={(active, over) => {
        // 检查权限
        const canEditInsurTable =
          insurSeparateTableLogic?.canEditInsurTable ?? true;
        if (!canEditInsurTable) {
          return;
        }

        return new Promise((resolve, reject) => {
          if (icdeMoveMainConfirm === false) {
            resolve(true);
          } else {
            if (active.newIndex === 0) {
              const tableData = props?.form?.getFieldValue(
                'diagnosis-insur-table',
              );
              let activeItem = tableData?.at(active.oldIndex);
              Modal.confirm({
                title: `确定移动${
                  isEmptyValues(activeItem?.IcdeCode)
                    ? '此条数据'
                    : `诊断编码: ${activeItem.IcdeCode}`
                }到主诊？`,
                content: '',
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                  resolve(true);
                },
                onCancel: () => {
                  resolve(false);
                },
                getContainer: () =>
                  document.getElementById('dmr-main-container'),
              });
            } else {
              resolve(true);
            }
          }
        });
      }}
      // onDragExtra={(tableData) => {
      //   if (icdeOperFirstMain) {
      //     setFirstItemIsMainIcde(tableData);
      //   }
      // }}
      onValuesChange={(recordList, changedValues) => {
        // 检查权限
        const canEditInsurTable =
          insurSeparateTableLogic?.canEditInsurTable ?? true;
        if (!canEditInsurTable) {
          return;
        }

        props?.form?.setFieldValue('diagnosis-insur-table', recordList);
        triggerFormValueChangeEvent('diagnosis-insur-table');
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        // 检查权限
        const canEditInsurTable =
          insurSeparateTableLogic?.canEditInsurTable ?? true;
        if (!canEditInsurTable) {
          return;
        }

        props?.form?.setFieldValue(
          'diagnosis-insur-table',
          cloneDeep(tableData),
        );
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('diagnosis-insur-table');
      }}
      columns={tableColumns}
      enableRowSelection={true}
      allowDragging={insurSeparateTableLogic?.canEditInsurTable ?? true}
      canRowSortable={() => insurSeparateTableLogic?.canEditInsurTable ?? true}
    />
  );
};

export default React.memo(IcdeInsurTable);
