import { tcmIcdeDictColumns } from '@/pages/configuration/base/columns';
import {
  Button,
  Form,
  Modal,
  TableProps,
  Card,
  Input,
  Space,
  message,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useModel, useRequest, useSelector } from 'umi';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import { icdeTypesProcessor } from '@/pages/configuration/base/icde/processor';
import TcmIcdeDictionaryAdd from '@/pages/configuration/base/icde/components/tcm-icde/components/add';
import { TcmIcdeDictionaryItem } from '@/pages/configuration/base/interfaces';
import UniEditableTable from '@uni/components/src/table/edittable';
import { v4 as uuidv4 } from 'uuid';
import { useDebounce, useMutationObserver } from 'ahooks';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { useUpdateEffect } from 'ahooks';
import ISD from '@uni/components/src/infinite-scroll';
import DebounceSelect from '@/components/debounceSelect';
import { UniTable } from '@uni/components/src';
interface TcmIcdeDictionaryProps {
  moduleGroup?: string;
}
const Event = 'TCM';

interface DmrManagementSummaryItemProps {
  className?: string;
  label?: string;
  count?: number;
  itemKey?: string;
  onClick?: () => any;
}
const infiniteScrollId = 'tcm-icde-isd';

const DmrManagementSummaryItem = (props: DmrManagementSummaryItemProps) => {
  return (
    <div
      className={`dmr-management-summary-item-container ${props?.className}`}
      onClick={() => {
        props?.onClick && props?.onClick();
      }}
    >
      <span className={'label'}>{props?.label}</span>
    </div>
  );
};

const TcmIcdeDictionary = (props?: TcmIcdeDictionaryProps) => {
  const dictData = useSelector((state) => state?.uniDict?.dictData);

  const [form] = Form.useForm();
  const ref = useRef<any>();

  // keywords
  const [searchKeywords, setSearchKeywords] = useState(undefined);
  const debouncedKeywords = useDebounce(searchKeywords, { wait: 1000 });

  const [tcmIcdeDictionaryAdd, setTcmIcdeDictionaryAdd] = useState(false);

  const [
    tcmIcdeDictionaryTableDataSource,
    setTcmIcdeDictionaryTableDataSource,
  ] = useState([]);

  const [tcmIcdeDictionaryColumns, setTcmIcdeDictionaryColumns] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  // 几个infinite新值
  const infiniteChildRef = useRef<any>();
  const [infiniteData, setInfiniteData] = useState([]);
  const [infiniteLoading, setInfiniteLoading] = useState(false);
  const [editIndex, setEditIndex] = useState(-1);

  useEffect(() => {
    console.log('dictData', dictData);
    tcmIcdeConfigurationColumnsReq();
  }, [dictData]);

  // useEffect(() => {
  //   tcmIcdeDictionaryReq(
  //     backPagination?.current || 1,
  //     backPagination?.pageSize || 10,
  //   );
  // }, [debouncedKeywords]);

  // handle dmrCardsSummarySelectedKey
  const dmrCardsSummarySelectedKeyHandler = (data) => {
    if (dmrCardsSummarySelectedKey && dmrCardsSummarySelectedKey !== '999') {
      data['TcmIcdeCategory'] = dmrCardsSummarySelectedKey;
    }
    return data;
  };

  const { loading: tcmIcdeDictionaryLoading, run: tcmIcdeDictionaryReq } =
    useRequest(
      (current, pageSize) => {
        let data = {
          ModuleGroup: props?.moduleGroup,
          DtParam: {
            Draw: 1,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
          Keyword: debouncedKeywords,
        };

        data = dmrCardsSummarySelectedKeyHandler(data);

        return uniCommonService(
          'Api/Sys/CodeSys/GetDmrTcmIcdeWithAllCompares',
          {
            method: 'POST',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (
          response: RespVO<TableResp<any, TcmIcdeDictionaryItem>>,
        ) => {
          if (response.code === 0 && response?.statusCode === 200) {
            let tableDataSource = response?.data?.data.slice();
            setTcmIcdeDictionaryTableDataSource(
              tableDataSource.map((item) => {
                item['id'] = uuidv4();

                return item;
              }),
            );

            setBackPagination({
              ...backPagination,
              total: response?.data?.recordsFiltered || 0,
            });
          } else {
            setTcmIcdeDictionaryTableDataSource([]);
          }
        },
      },
    );

  const { run: tcmIcdeConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetDmrTcmIcdeWithAllCompares', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setTcmIcdeDictionaryColumns(
            tableColumnBaseProcessor(
              tcmIcdeDictColumns(Event, dictData),
              response?.data?.Columns,
            ),
          );
        } else {
          setTcmIcdeDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: tcmIcdeUpsertLoading, run: tcmIcdeUpsertReq } = useRequest(
    (values) => {
      let data = {};

      data = {
        ...values,
        ModuleGroup: props?.moduleGroup,
      };

      return uniCommonService('Api/Sys/CodeSys/UpsertDmrTcmIcdeAllCompare', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.statusCode === 200) {
          setTcmIcdeDictionaryAdd(false);
          // tcmIcdeDictionaryReq(
          //   backPagination?.current,
          //   backPagination?.pageSize,
          // );
          return response?.data;
        }
      },
      onSuccess: (data, ret) => {
        // 现在这边hack 因为无限滚动 不能再重新获取数据 需要使用记录的index与编辑的结果
        // 最好的是upsert该接口返回后端结果，确保数据正确
        if (editIndex > -1 && data) {
          setInfiniteData(
            infiniteData?.map((d, i) => {
              return d?.TcmIcdeId === data ? { ...d, ...ret?.at(0) } : d;
            }),
          );
          setEditIndex(-1); // reset
        } else if (editIndex === -1 && data) {
          // 新增的情况 之后讨论下怎么进行数据处理 应该是后端返回这个新增的当前位置？TODO
          Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
        }
      },
    },
  );

  const onTcmIcdeDictionaryItemAdd = (values: any) => {
    tcmIcdeUpsertReq(values);
  };

  const { run: icdeDeleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        ModuleGroup: props?.moduleGroup,
        tcmIcdeId: values?.TcmIcdeId,
      };
      return uniCommonService('Api/Sys/CodeSys/DeleteDmrTcmIcde', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
          return response?.data;
        }
      },
      onSuccess: (data, params) => {
        // 删除不reload 不然位置会直接挂 应该说滚动加载的数据好像就不适合被edit & delete
        console.log(params);
        if (data) {
          setInfiniteData(
            infiniteData?.filter(
              (d, i) => d?.TcmIcdeId !== params?.at(0)?.TcmIcdeId,
            ),
          );
        }
        // tcmIcdeDictionaryReq(backPagination?.current, backPagination?.pageSize);
      },
    },
  );

  useEffect(() => {
    Emitter.onMultiple(
      [
        `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_ICDE_SELECT}#${Event}`,
      ],
      (data) => {
        let currentFormValue = form.getFieldsValue()?.[data?.id];
        if (currentFormValue) {
          // set form value
          Object.keys(data?.values)?.forEach((key) => {
            currentFormValue[key] = data?.values?.[key];
          });
          form.setFieldValue(data?.id, currentFormValue);
        }
      },
    );

    Emitter.on(ConfigurationEvents.DMR_ICDE_EDIT, (data) => {
      if (data?.index > -1) {
        if (data?.record?.TcmIcdeId) {
          // index 要记录 会使用到
          form.setFieldsValue(data.record);
          setEditIndex(data?.index);
          setTcmIcdeDictionaryAdd(true);
        }
      }
    });

    Emitter.on(ConfigurationEvents.DMR_ICDE_DELETE, (data) => {
      if (data?.index > -1) {
        icdeDeleteReq(data.record);
      }
    });

    return () => {
      Emitter.offMultiple([
        `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_ICDE_SELECT}#${Event}`,
      ]);

      Emitter.off(ConfigurationEvents.DMR_ICDE_EDIT);
      Emitter.off(ConfigurationEvents.DMR_ICDE_DELETE);
    };
  }, [tcmIcdeDictionaryTableDataSource]);

  const [dmrCardsSummarySelectedKey, setDmrCardsSummarySelectedKey] =
    useState('999');

  // useUpdateEffect(() => {
  //   let pagination = {
  //     ...backPagination,
  //     current: 1,
  //     // pageSize: 10,
  //     total: 0,
  //   };
  //   setBackPagination(pagination);
  //   tcmIcdeDictionaryReq(pagination?.current, pagination?.pageSize);
  // }, [dmrCardsSummarySelectedKey]);

  // infinite 额外处理
  // debounce select
  async function fetchIcdeSelect(keyword: string): Promise<any[]> {
    if (!keyword) return;
    return await uniCommonService('Api/Dmr/DmrSearch/Icde', {
      params: {
        IsTcm: true,
        KeyWord: keyword,
        SkipCount: 0,
        MaxResultCount: 100,
      },
    });
  }

  // search emitter
  useEffect(() => {
    Emitter.on(
      EventConstant.INFINITE_SCROLL_CB_FETCH,
      async ({ searchedValue, reqData, cb, isdId }) => {
        if (isdId === infiniteScrollId) {
          let response = await uniCommonService(
            'Api/Sys/CodeSys/SearchDmrTcmIcdeWithAllCompare',
            {
              method: 'POST',
              data: {
                code: searchedValue,
                TcmIcdeCategory: reqData?.TcmIcdeCategory,
              },
            },
          );
          if (response?.code === 0) {
            cb(response.data);
          }
        }
      },
    );

    return () => {
      Emitter.off(EventConstant.INFINITE_SCROLL_CB_FETCH);
    };
  }, []);

  return (
    <>
      <Card
        title="中医诊断列表"
        extra={
          <Space>
            <ConfigExcelTemplateHandler
              downloadTemplateApiObj={{
                apiUrl: 'Api/Sys/CodeSys/GetDmrTcmIcdeAllCompareExcelTemplate',
              }}
              downloadPostData={{
                moduleGroup: props?.moduleGroup,
                exportName: '中医诊断列表',
              }}
              uploadXlsxApiObj={{
                apiUrl: 'Api/Sys/CodeSys/UploadDmrTcmIcdeAllCompareExcelFile',
                onSuccess: () => {
                  Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
                  // tcmIcdeDictionaryReq(
                  //   backPagination?.current,
                  //   backPagination?.pageSize,
                  // );
                },
              }}
              uploadPostData={{
                moduleGroup: props?.moduleGroup,
              }}
            />
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                // ref.current?.addEditRecord({
                //   id: uuidv4()
                // });
                setTcmIcdeDictionaryAdd(true);
                form.setFieldValue('IsValid', true);
                form.setFieldValue('IsMain', true);
              }}
            >
              新增中医诊断
            </Button>
          </Space>
        }
      >
        <UniTable
          actionRef={ref}
          id={`tcm-icde-dictionary-table`}
          className={'tcm-icde-dictionary-table'}
          rowKey={'uuidv4'}
          scroll={{
            y: 480,
            x: 'max-content',
          }}
          headerTitle={
            <>
              {/* <Input.Search
                allowClear
                placeholder="请输入诊断编码或名称"
                value={searchKeywords}
                onChange={(e) => setSearchKeywords(e.target.value)}
              /> */}
              <DebounceSelect
                value={searchKeywords}
                placeholder="请输入诊断编码或名称"
                fetchOptions={fetchIcdeSelect}
                onChange={(newValue) => {
                  setSearchKeywords(newValue);
                }}
                fieldNames={{
                  label: 'Name',
                  value: 'Code',
                }}
                style={{ width: '350px', marginBottom: '10px' }}
              />
            </>
          }
          toolBarRender={() => [
            <div className="dmr-management-summary-container d-flex">
              {dictData?.TcmIcdeCategory &&
                [
                  { Name: '全部', Code: '999' },
                  ...dictData?.TcmIcdeCategory,
                ]?.map((item) => {
                  return (
                    <DmrManagementSummaryItem
                      className={
                        item?.Code === dmrCardsSummarySelectedKey
                          ? 'card-selected'
                          : ''
                      }
                      label={item?.Name}
                      itemKey={item?.Code}
                      onClick={() => setDmrCardsSummarySelectedKey(item?.Code)}
                    />
                  );
                })}
            </div>,
          ]}
          // backendPagination
          dictionaryData={dictData}
          bordered={true}
          loading={
            tcmIcdeDictionaryLoading || infiniteLoading || tcmIcdeUpsertLoading
          }
          columns={tcmIcdeDictionaryColumns}
          dataSource={infiniteData} // tcmIcdeDictionaryTableDataSource
          clickable={false}
          pagination={false} // backPagination
          // onTableChange={backTableOnChange}
          // recordCreatorProps={false}
          // editable={{
          //   form: form,
          //   type: 'multiple',
          //   editableKeys: editableColumnKeys,
          //   onSave: async (rowKey, data, row) => {
          //     tcmIcdeUpsertReq(data);
          //   },
          //   actionRender: (row, config, defaultDoms) => {
          //     return [defaultDoms.save, defaultDoms.cancel];
          //   },
          //   onChange: setEditableColumnKeys,
          // }}
        />
      </Card>

      <Modal
        title={editIndex > -1 ? '编辑中医诊断' : '新增中医诊断'}
        open={tcmIcdeDictionaryAdd}
        onOk={() => {
          onTcmIcdeDictionaryItemAdd(form.getFieldsValue());
        }}
        onCancel={() => {
          setTcmIcdeDictionaryAdd(false);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById(
          'tcm-icde-configuration-container',
        )}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <TcmIcdeDictionaryAdd form={form} event={Event} />
      </Modal>

      <ISD
        id={infiniteScrollId}
        scrollFetchObj={{
          url: 'Api/Sys/CodeSys/GetDmrTcmIcdeWithAllCompares',
        }}
        scrollDom={document?.querySelector(
          `div[id='tcm-icde-dictionary-table'] div[class=ant-table-body]`,
        )}
        reqData={{
          Code: searchKeywords,
          TcmIcdeCategory:
            dmrCardsSummarySelectedKey !== '999'
              ? dmrCardsSummarySelectedKey
              : undefined,
        }}
        dataSource={infiniteData}
        setDataSource={setInfiniteData}
        setLoading={setInfiniteLoading}
        searchedValue={searchKeywords}
        searchedKey="Code"
      />
    </>
  );
};

export default TcmIcdeDictionary;
