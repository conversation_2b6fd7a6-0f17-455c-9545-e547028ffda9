import { IGridItem } from '@/pages/dmr/interfaces';

export const bloodExtra: IGridItem[][] = [
  [
    {
      data: {
        prefix: '红细胞',
        key: 'RedBloodCell',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 0.01,
          precious: 2,
          formKey: 'RedBloodCell',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '血小板',
        key: 'BloodPlatelet',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 0.01,
          precious: 2,
          formKey: 'BloodPlatelet',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '血浆',
        key: 'Plasma',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 0.01,
          precious: 2,
          formKey: 'Plasma',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
  [
    {
      data: {
        prefix: '全血',
        key: 'WholeBlood',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 0.01,
          precious: 2,
          formKey: 'WholeBlood',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '冷沉淀',
        key: 'Cryoprecipitation',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 0.01,
          precious: 2,
          formKey: 'Cryoprecipitation',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '自体回收',
        key: 'OwnBloodTransfusion',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 0.01,
          precious: 2,
          formKey: 'OwnBloodTransfusion',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
    {
      data: {
        prefix: '其他',
        key: 'OtherBlood',
        desc: '',
        suffix: '',
        component: 'RestrictInputNumber',
        props: {
          min: 0,
          step: 0.01,
          precious: 2,
          formKey: 'OtherBlood',
          className: 'border-none',
        },
      },
      w: 4,
      xs: {
        w: 6,
      },
      xxs: {
        w: 5,
      },
    },
  ],
];
