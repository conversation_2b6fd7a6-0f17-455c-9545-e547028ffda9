import React, { useContext, useEffect, useRef, useState } from 'react';
import { Form, Modal } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import { IcdeOperationReadonlyItem } from '@uni/grid/src/components/icde-oper-input/input';
import { isEmptyValues, generateUniqueNumberId } from '@uni/utils/src/utils';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import GridItemContext from '@uni/commons/src/grid-context';
import { getOperMainColumns } from '../columns/operMainColumns';
import {
  getSessionJSON,
  INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY,
} from '@/pages/dmr/components/icde-insur-separate-table/utils/session';
import {
  filterDuplicatedOperCodesOnComboSelect,
  OperationComboRules,
} from '@/pages/dmr/operation-combo';
import mergeWith from 'lodash/mergeWith';
import omit from 'lodash/omit';

interface OperMainTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];
  underConfiguration?: boolean;
  onChange?: (value: any) => void;
}

export const operTypeToClassName = {
  '1': 'operation-tr-color-diagnosis',
  '2': 'operation-tr-color-treatment',
  '3': 'operation-tr-color-intervention',
  '4': 'operation-tr-color-basic',
};

const operationCopyKeys =
  (window as any).externalConfig?.['dmr']?.operationCopyKeys ?? [];

const icdeOperFirstMain =
  (window as any).externalConfig?.['dmr']?.icdeOperFirstMain ?? false;

const operCopyFocusKey =
  (window as any).externalConfig?.['dmr']?.operCopyFocusKey ?? undefined;
const operDeleteConfirm =
  (window as any).externalConfig?.['dmr']?.operDeleteConfirm ?? false;

const operationComboDefaultMap =
  (window as any).externalConfig?.['dmr']?.operationComboDefaultMap ?? {};

// 复制键集合
const copyKeys = !isEmptyValues(operationCopyKeys)
  ? operationCopyKeys
  : [
      'OperGroupNo',
      'OprnOprtBegntime',
      'OprnConTime',
      'Operator',
      'Firstasst',
      'Secondasst',
      'AnaType',
      'AnstLvCode',
      'AnaDoc',
      'OprnPatnType',
      'OprnOprtEndtime',
      'OperNote',
      'AnstBegntime',
      'AnstEndtime',
      'OperDept',
      'WoundHealingRateClass',
      'OperAccord',
    ];

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_OPER_MAIN_ADD, event.target.id);
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_OPER_MAIN_DELETE, index);
    }
  },
  COPY: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_OPER_MAIN_COPY, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationMainTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('operationMainTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    Emitter.emit(getArrowUpDownEventKey('operationMainTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};
// 清除键映射
const clearKeysMap = {
  // 仅用于联动删除使用
  OperCode: [
    'OperName',
    'OperCode',
    'InsurName',
    'InsurCode',
    'HqmsName',
    'HqmsCode',
    'OperExtra',
    'HqmsDegree',
    'DegreeRemark',
    'DrgsDegree',
    'RowClassName',
  ],
};

const OperMainTable = (props: OperMainTableProps) => {
  const itemRef = React.useRef<any>();
  const operMainTableRef = useRef(null);
  const [form] = Form.useForm();

  // 获取联动逻辑状态（优先从 sessionStorage 读取，兜底取 context）
  const gridContext = useContext(GridItemContext);
  const insurSeparateTableLogic =
    getSessionJSON(INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY) ??
    gridContext?.extra?.insurSeparateTableLogic;

  const operDataSource = Form.useWatch('operationMainTable', props?.form) ?? [];
  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);
  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    operDataSource?.length,
  );
  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // 联动逻辑：同步到Insur表格
  const syncToInsurTable = (
    operation: 'add' | 'delete' | 'copy' | 'update' | 'reorder' | 'combo',
    payload: any,
  ) => {
    console.log(
      'isTableLinkageDisabled',
      insurSeparateTableLogic?.isTableLinkageDisabled,
    );
    // 检查权限 只有在联动未被禁用时才执行
    if (!insurSeparateTableLogic?.isTableLinkageDisabled) {
      const currentInsurData =
        props?.form?.getFieldValue('operation-insur-table') || [];
      let newInsurData = [...currentInsurData];

      switch (operation) {
        case 'add':
          const newInsurItem = {
            id: payload.id,
            UniqueId: payload.UniqueId,
            IsReported: true,
            // 可以根据IcdeCode获取对应的医保编码和名称
          };
          newInsurData.push(newInsurItem);
          break;

        case 'delete':
          const deletedUniqueId = payload.UniqueId;
          if (deletedUniqueId) {
            const insurIndex = newInsurData.findIndex(
              (item) => item.UniqueId === deletedUniqueId,
            );
            if (insurIndex >= 0) {
              newInsurData.splice(insurIndex, 1);
            }
          }
          break;

        case 'copy':
          const { originalItem, copiedItem } = payload;
          const originalInsurItem = newInsurData.find(
            (item) => item.UniqueId === originalItem.UniqueId,
          );
          if (originalInsurItem) {
            // 复制好后要移植给医保的部分
            let copiedInsurItem = {
              IsReported: true,
              id: copiedItem.id,
              UniqueId: copiedItem.UniqueId,
            };
            copyKeys?.forEach((key) => {
              copiedInsurItem[key] = originalInsurItem[key];
            });
            // 找到复制时原本的那一条的index
            const insurIndex = newInsurData.findIndex(
              (item) => item.UniqueId === originalItem.UniqueId,
            );
            newInsurData.splice(insurIndex + 1, 0, copiedInsurItem);
          }
          break;

        case 'update':
          // onValuesChange 触发的更新操作
          const { changedValues, recordList } = payload;
          // 获取变化的id
          const changedIds = Object.keys(changedValues);

          changedIds.forEach((changedId) => {
            // 在recordList中找到对应的数据
            const changedRecord = recordList.find(
              (record) => record.id?.toString() === changedId?.toString(),
            );
            if (changedRecord && changedRecord.UniqueId) {
              // 在医保数据中找到对应的条目
              const insurIndex = newInsurData.findIndex(
                (item) => item.UniqueId === changedRecord.UniqueId,
              );
              if (insurIndex >= 0) {
                // 这边应该全部更新 然后再单独更新InsurCode & InsurName
                newInsurData[insurIndex] = changedRecord;
                // 更新 InsurCode 和 InsurName
                if (changedRecord.InsurCode !== undefined) {
                  newInsurData[insurIndex].InsurCode = changedRecord.InsurCode;
                }
                if (changedRecord.InsurName !== undefined) {
                  newInsurData[insurIndex].InsurName = changedRecord.InsurName;
                }
              }
            }
          });
          break;

        case 'reorder':
          // onTableDataSourceOrderChange 触发的重新排序操作
          const { tableData } = payload;
          // 根据新的tableData顺序重新排序医保数据
          const reorderedInsurData = [];
          tableData.forEach((mainItem) => {
            if (mainItem.UniqueId) {
              const matchingInsurItem = newInsurData.find(
                (insurItem) => insurItem.UniqueId === mainItem.UniqueId,
              );
              if (matchingInsurItem) {
                reorderedInsurData.push(matchingInsurItem);
              }
            }
          });
          newInsurData = reorderedInsurData;

          break;

        case 'combo':
          // onOperationComboItemSelect 触发的组合手术操作
          const { newInsurItems } = payload;
          if (newInsurItems && Array.isArray(newInsurItems)) {
            newInsurItems.forEach((newItem) => {
              const newInsurItem = {
                ...newItem,
                IsReported: true,
              };
              newInsurData.push(newInsurItem);
            });
          }
          break;
      }
      // 更新insur form
      props?.form?.setFieldValue(
        'operation-insur-table',
        cloneDeep(newInsurData),
      );
      console.log(
        'syncToInsurTable',
        operation,
        newInsurData,
        props?.form?.getFieldsValue(),
      );
      triggerFormValueChangeEvent('operation-insur-table');

      // 通知医保表格进行重新渲染
      Emitter.emit(EventConstant.DMR_OPER_INSUR_TABLE_SYNC, {
        operation,
        payload,
        timestamp: Date.now(),
      });
    }
  };

  // 更新行选择状态的函数RowSelection
  const updateRowSelectionState = () => {
    const tableData = props?.form?.getFieldValue('operation-main-table') || [];
    const validRows = tableData.filter((row) => row.id !== 'ADD');

    const selectedRows = validRows.filter((row) => {
      const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
      return rowSelectionValue === true;
    });

    const allSelected =
      selectedRows.length === validRows.length && validRows.length > 0;
    const indeterminate =
      selectedRows.length > 0 && selectedRows.length < validRows.length;
    const hasSelection = selectedRows.length > 0;

    Emitter.emit(`DMR_ROW_SELECTION_STATE_UPDATE_operationMainTable`, {
      allSelected: allSelected,
      indeterminate: indeterminate,
    });

    // 通知批量删除按钮状态
    Emitter.emit(`DMR_ROW_SELECTION_BATCH_UPDATE_operationMainTable`, {
      hasSelection: hasSelection,
    });
  };

  useEffect(() => {
    updateRowSelectionState();
    setTableDataSourceSize(operDataSource?.length);
  }, [operDataSource]);

  // 手术特殊部分：
  React.useImperativeHandle(operMainTableRef, () => {
    return {
      // 处理组合手术选择，包括添加多个手术项目和处理规则
      onOperationComboItemSelect: (
        comboItem: any,
        waitForAddToTableOperationData: any[],
        currentTriggeredRecordId: string,
      ) => {
        console.log(
          'onOperationComboItemSelect',
          comboItem,
          waitForAddToTableOperationData,
        );
        let tableData = props?.form?.getFieldValue('operation-main-table');

        // 换Index 也就是当前这条
        let currentTriggeredRecordIndex = tableData?.findIndex(
          (item) =>
            item.id?.toString() === currentTriggeredRecordId?.toString(),
        );

        let comboOperationCodes = waitForAddToTableOperationData
          ?.map((item) => {
            return item?.OperCode;
          })
          ?.filter((item) => !isEmptyValues(item));

        //  执行规则
        const operationComboRulesInstance = new OperationComboRules(
          comboOperationCodes,
          currentTriggeredRecordIndex,
          copyKeys,
          ['OperNote'],
        );

        let addTableData = [];
        filterDuplicatedOperCodesOnComboSelect(
          tableData,
          waitForAddToTableOperationData,
        )?.forEach((item: any, index: number) => {
          addTableData.push({
            id: tableData?.length + index + 1,
            IsReported: true,
            UniqueId: uuidv4(),

            // 处理完的数据
            ...item,
          });
        });

        if (!isEmptyValues(addTableData)) {
          let sliceCount = 0;
          if (
            !comboOperationCodes?.includes(
              tableData[currentTriggeredRecordIndex]?.OperCode,
            )
          ) {
            sliceCount = 1;
            tableData[currentTriggeredRecordIndex] = mergeWith(
              {},
              operationComboDefaultMap,
              tableData[currentTriggeredRecordIndex],
              omit(
                operationComboRulesInstance.comboSelectedItemsProcess(
                  tableData,
                  addTableData?.at(0),
                ),
                [
                  'UniqueId',
                  'IsReported',
                  'id',
                  'Id',
                  ...Object.keys(operationComboDefaultMap)?.filter(
                    (key) =>
                      !isEmptyValues(
                        tableData[currentTriggeredRecordIndex]?.[key],
                      ),
                  ),
                ],
              ),
              (objValue, srcValue) => {
                if (!isEmptyValues(srcValue)) {
                  return srcValue;
                }

                return objValue;
              },
            );
          }

          addTableData
            ?.slice(sliceCount)
            ?.forEach((waitForAddTableDataItem: any, index: number) => {
              tableData.splice(
                currentTriggeredRecordIndex + 1 + index,
                0,
                mergeWith(
                  {},
                  operationComboDefaultMap,
                  operationComboRulesInstance.comboSelectedItemsProcess(
                    tableData,
                    waitForAddTableDataItem,
                  ),
                  (objValue, srcValue) => {
                    if (!isEmptyValues(srcValue)) {
                      return srcValue;
                    }

                    return objValue;
                  },
                ),
              );
            });

          // 删除对 医保主手术的 设定 source: DMRDEV-816
          // 手术组套 当且仅当 表格内无主 给第一条主
          // let insurMainOperItem = tableData?.find(
          //   (item) => item?.IsMain === true,
          // );
          // if (isEmptyValues(insurMainOperItem) && tableData?.length > 0) {
          //   tableData[0]['IsMain'] = true;
          // }

          setWaitFocusId(
            `div[id=operationMainTable] tbody > tr:nth-child(${
              currentTriggeredRecordIndex + 1
            }) > td[dataindex="OperCode"] input`,
          );
          setTableDataSourceSize(0);
          setTimeout(() => {
            setTableDataSourceSize(tableData?.length);
          }, 0);
          props?.form?.setFieldValue(
            'operation-main-table',
            cloneDeep(tableData),
          );
          triggerFormValueChangeEvent('operation-main-table');

          // 联动逻辑：同步组合手术新增到Insur表格
          const newInsurItems = [];
          // 收集所有新增的手术项目（包括更新的当前项和新插入的项目）
          if (sliceCount === 1) {
            // 当前项被更新了，需要同步
            newInsurItems.push(tableData[currentTriggeredRecordIndex]);
          }
          // 新插入的项目
          addTableData?.slice(sliceCount)?.forEach((item, index) => {
            newInsurItems.push(
              tableData[currentTriggeredRecordIndex + 1 + index],
            );
          });

          // 调用同步函数
          syncToInsurTable('combo', { newInsurItems });
        }
      },
    };
  });

  // 行上下移动事件
  const lineUpDownEvents = {
    LINE_UP: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('operationMainTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue(
        'operation-main-table',
      ).length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('operationMainTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  const setFirstItemIsMainOper = (tableData: any[], formAdd = false) => {
    // 当且仅当 表格内有主 才给第一条主（并且不是通过添加产生的数据）
    let hasIsMainTrue =
      tableData?.filter((item) => item?.IsMain === true)?.length > 0;
    if (hasIsMainTrue === false && !formAdd) {
      return;
    }

    let firstItem = tableData?.[0];
    tableData?.forEach((item) => {
      item['IsMain'] = false;
      form.setFieldValue([item?.id, 'IsMain'], false);
    });
    if (firstItem) {
      firstItem['IsMain'] = true;
      form.setFieldValue([firstItem?.id, 'IsMain'], true);
      form.setFieldValue([firstItem?.id, 'IsReported'], true);
    }
  };

  // 根据键数组清除值
  const clearValuesByKeys = (keys, index) => {
    const operationDataSource = props?.form?.getFieldValue(
      'operation-main-table',
    );
    let formItemId = operationDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('operation-main-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue('operation-main-table', cloneDeep(tableData));
    triggerFormValueChangeEvent('operation-main-table');
  };

  useEffect(() => {
    const base = getOperMainColumns(insurSeparateTableLogic);
    const columns = mergeColumnsInDmrTable(
      props?.columns,
      base,
      'OperMainTable',
    );
    setTableColumns(columns);
  }, [props?.columns, insurSeparateTableLogic?.canEditMainTable]);

  useEffect(() => {
    if (!isEmptyValues(waitFocusId)) {
      setTimeout(() => {
        waitFocusElementRefocusBySelector(waitFocusId);
      }, 100);
    }
  }, [waitFocusId]);

  useEffect(() => {
    // delete事件
    Emitter.on(getDeletePressEventKey('operationMainTable'), (itemId) => {
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);

      let clearKeys = [key];
      if (clearKeysMap[key]) {
        clearKeys = clearKeysMap[key];
      }
      clearValuesByKeys(clearKeys, index);

      setTimeout(() => {
        document.getElementById(itemId)?.focus();
      }, 100);
    });

    Emitter.on(EventConstant.DMR_OPER_MAIN_ADD, () => {
      const canEditMainTable =
        insurSeparateTableLogic?.canEditMainTable ?? true;
      if (!canEditMainTable) return;

      let rowData = {
        id: generateUniqueNumberId(),
        IsReported: true,
        UniqueId: uuidv4(),
      };
      let tableData = props?.form?.getFieldValue('operation-main-table') || [];
      tableData.splice(tableData.length, 0, rowData);
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }
      props?.form?.setFieldValue('operation-main-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-main-table');
      // 联动逻辑：同步新增到Insur表格
      syncToInsurTable('add', rowData);
      setWaitFocusId(
        `div[id=operationMainTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setTableDataSourceSize(tableData?.length);
    });

    // 处理全选/反选事件
    Emitter.on(`DMR_ROW_SELECTION_SELECT_ALL_operationMainTable`, (data) => {
      const tableData =
        props?.form?.getFieldValue('operation-main-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 批量更新所有行的选中状态
      validRows.forEach((row) => {
        form.setFieldValue([row.id, 'RowSelection'], data.checked);
      });

      // 触发表单值变化事件
      triggerFormValueChangeEvent('operation-main-table');

      // 通知选中状态更新
      updateRowSelectionState();
    });

    // 处理单个行选择变化事件
    Emitter.on(`DMR_ROW_SELECTION_ITEM_CHANGE_operationMainTable`, (data) => {
      // 延迟更新状态，确保表单值已经更新
      setTimeout(() => {
        updateRowSelectionState();
      }, 150);
    });

    // 处理批量删除事件
    Emitter.on(`DMR_BATCH_DELETE_operationMainTable`, () => {
      const tableData =
        props?.form?.getFieldValue('operation-main-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 获取选中的行索引
      const selectedIndexes = [];
      validRows.forEach((row, index) => {
        const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
        if (rowSelectionValue === true) {
          selectedIndexes.push(index);
        }
      });

      if (selectedIndexes.length > 0) {
        // 从后往前删除，避免索引变化问题
        const sortedIndexes = selectedIndexes.sort((a, b) => b - a);
        let newTableData = [...tableData];

        sortedIndexes.forEach((index) => {
          newTableData.splice(index, 1);
        });

        // TODO 设定主诊为第一个
        if (icdeOperFirstMain && newTableData.length > 0) {
          setFirstItemIsMainOper(newTableData);
        }

        // 更新form
        props?.form?.setFieldValue(
          'operation-main-table',
          cloneDeep(newTableData),
        );
        triggerFormValueChangeEvent('operation-main-table');

        setTableDataSourceSize(newTableData?.length);

        // todo 同步删除到Insur表格

        // 延迟更新选择状态
        setTimeout(() => {
          updateRowSelectionState();
        }, 100);
      }
    });

    Emitter.on(EventConstant.DMR_OPER_MAIN_DELETE, (index) => {
      const canEditMainTable =
        insurSeparateTableLogic?.canEditMainTable ?? true;
      if (!canEditMainTable) return;

      const onDelete = () => onOperationItemDelete(index);
      if (operDeleteConfirm) {
        Modal.confirm({
          title: `确定删除第${index + 1} 条手术数据？`,
          onOk: onDelete,
          getContainer: () => document.getElementById('dmr-main-container'),
        });
      } else {
        onDelete();
      }
    });
    // 复制一条
    Emitter.on(EventConstant.DMR_OPER_MAIN_COPY, (payload) => {
      let currentCopyItem = form.getFieldValue(payload?.['id']);
      let index = payload?.index;
      let tableData = props?.form?.getFieldValue('operation-main-table');

      let copiedItem = {
        id: generateUniqueNumberId(),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      Object.keys(currentCopyItem)?.forEach((key) => {
        if (copyKeys?.includes(key)) {
          copiedItem[key] = currentCopyItem[key];
        }
      });

      tableData.splice(index + 1, 0, copiedItem);

      // 删除对 医保主手术的 设定 source: DMRDEV-816
      // 吃话： 有主的时候 拖能第一个为主 2025/2/18 13:36:10  eaten by 王总
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }

      if (!isEmptyValues(operCopyFocusKey)) {
        setWaitFocusId(
          `div[id=operationMainTable] tbody > tr:nth-child(${
            index + 2
          }) > td input[id*=${operCopyFocusKey}]`,
        );
      } else {
        // 跳到最新一行的第一个上
        setWaitFocusId(
          `div[id=operationMainTable] tbody > tr:nth-child(${
            index + 2
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);

      // props?.onChange && props?.onChange(tableData);
      props?.form?.setFieldValue('operation-main-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-main-table');
      // 联动逻辑：同步复制到Insur表格
      syncToInsurTable('copy', {
        originalItem: currentCopyItem,
        copiedItem: copiedItem,
      });
    });

    Emitter.on(getArrowUpDownEventKey('operationMainTable'), (payload) => {
      let type = payload?.type;
      const operationDataSource = props?.form?.getFieldValue(
        'operation-main-table',
      );
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > operationDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('operationMainTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();
      Emitter.off(getDeletePressEventKey('operationMainTable'));
      Emitter.off(getArrowUpDownEventKey('operationMainTable'));
      Emitter.off(EventConstant.DMR_OPER_MAIN_ADD);
      Emitter.off(EventConstant.DMR_OPER_MAIN_DELETE);
      Emitter.off(EventConstant.DMR_OPER_MAIN_COPY);
      Emitter.off(`DMR_ROW_SELECTION_SELECT_ALL_operationMainTable`);
      Emitter.off(`DMR_ROW_SELECTION_ITEM_CHANGE_operationMainTable`);
      Emitter.off(`DMR_BATCH_DELETE_operationMainTable`);
    };
  }, [insurSeparateTableLogic]);

  const onOperationItemDelete = (index: number) => {
    if (index > -1) {
      const canEditMainTable =
        insurSeparateTableLogic?.canEditMainTable ?? true;
      if (!canEditMainTable) return;

      let tableData = props?.form?.getFieldValue('operation-main-table');
      const deletedItem = tableData[index]; // 保存被删除的项用于联动
      tableData.splice(index, 1);
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }
      props?.form?.setFieldValue('operation-main-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-main-table');

      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=operationMainTable] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setTableDataSourceSize(tableData?.length);
      // 联动逻辑：同步删除到Insur表格
      if (deletedItem) {
        syncToInsurTable('delete', deletedItem);
      }
    }
  };

  const columnsProcessor = (columns) => {
    return columns.map((columnItem) => {
      if (!columnItem.onCell) {
        columnItem.onCell = (record, index) => {
          if (record?.id === 'ADD') {
            return {
              colSpan: 0,
            };
          }

          return {};
        };

        if (columnItem.children) {
          columnItem.children = columnsProcessor(columnItem.children);
        }
      }

      return columnItem;
    });
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      dmrTableContainerRef={operMainTableRef}
      formItemContainerClassName={'form-content-item-container'}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'operationMainTable'}
      tableLayout={'auto'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{ x: 'max-content' }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      columns={tableColumns || []}
      dataSource={(() => {
        const canEditMainTable =
          insurSeparateTableLogic?.canEditMainTable ?? true;
        const baseDataSource = (
          props?.form?.getFieldValue('operation-main-table') ?? []
        )
          ?.filter((item) => item?.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = generateUniqueNumberId();
            }
            return item;
          });

        // 只有有编辑权限的用户才显示ADD行
        return canEditMainTable
          ? baseDataSource?.concat({ id: 'ADD' })
          : baseDataSource;
      })()}
      rowKey={'id'}
      onValuesChange={(recordList, changedValues) => {
        const canEditMainTable =
          insurSeparateTableLogic?.canEditMainTable ?? true;
        if (!canEditMainTable) return;
        props?.form?.setFieldValue('operation-main-table', recordList);
        triggerFormValueChangeEvent('operation-main-table');
        // 触发联动更新
        if (changedValues && Object.keys(changedValues).length > 0) {
          syncToInsurTable('update', {
            changedValues,
            recordList,
          });
        }
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        const canEditMainTable =
          insurSeparateTableLogic?.canEditMainTable ?? true;
        if (!canEditMainTable) return;
        props?.form?.setFieldValue(
          'operation-main-table',
          cloneDeep(tableData),
        );
        // 触发联动重新排序
        syncToInsurTable('reorder', {
          tableData,
          dataOrder,
        });
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('operation-main-table');
      }}
      enableRowSelection={true}
      allowDragging={insurSeparateTableLogic?.canEditMainTable ?? true}
      canRowSortable={() => insurSeparateTableLogic?.canEditMainTable ?? true}
      onDragExtra={(tableData) => {
        if (icdeOperFirstMain) {
          setFirstItemIsMainOper(tableData);
        }
      }}
      rowClassName={(record, index) => {
        if (record?.OperType) {
          let operTypeClassName = operTypeToClassName?.[record?.OperType];
          if (operTypeClassName) {
            return operTypeClassName;
          }
        }

        return '';
      }}
    />
  );
};

export default React.memo(OperMainTable);
