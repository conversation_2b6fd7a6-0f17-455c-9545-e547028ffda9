import cloneDeep from 'lodash/cloneDeep';
import isNil from 'lodash/isNil';
import { v4 as uuidv4 } from 'uuid';

// request Submit/Check
export const icuTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  data['CardIcus'] = (formFieldValues?.['icu-table'] || [])
    .filter((item) => item.id !== 'ADD')
    ?.map((item) => {
      if (isNil(item['UniqueId'])) {
        item['UniqueId'] = uuidv4();
      }

      if (typeof item['id'] === 'string' || item['id'] instanceof String) {
        delete item['id'];
      }

      return item;
    });
};

// response Get
export const icuTableResponseParamProcessor = (
  formFieldValue: any,
  icuItems: any[],
) => {
  formFieldValue['icu-table'] = icuItems.map((item) => {
    item['id'] = item['Id'];
    return item;
  });

  // 为了form item的刷新
  formFieldValue['icuTable'] = cloneDeep(formFieldValue['icu-table']?.slice());

  return formFieldValue;
};
