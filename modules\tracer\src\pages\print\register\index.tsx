import { Reducer, useEffect, useMemo, useReducer, useRef } from 'react';
import { Dispatch, useDispatch, useModel, useSelector } from 'umi';
import { UniTable } from '@uni/components/src';
import {
  <PERSON>ert,
  Button,
  Card,
  Col,
  Divider,
  InputNumber,
  InputRef,
  Modal,
  Popconfirm,
  Row,
  Space,
  Tooltip,
  message,
  Typography,
  Spin,
  notification,
  Badge,
  Image,
} from 'antd';
import { useSafeState, useKeyPress, useDebounce } from 'ahooks';
import {
  IModalState,
  IReducer,
  ITableState,
} from '@uni/reducers/src/Interface';
import { SwagTraceRecordItem } from '../../interface';
import { columnsHandler, handleMrActionApi, isRespErr } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType, SigninType } from '@/Constants';
import {
  ModalAction,
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
  InitModalState,
} from '@uni/reducers/src';
import dayjs from 'dayjs';
import {
  ProForm,
  ProFormDependency,
  ProFormGroup,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@uni/components/src/pro-form';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useTimelineReq } from '@/hooks';
import PatTimeline from '@/components/PatTimeline';
import {
  CloseCircleOutlined,
  CloseOutlined,
  DeleteOutlined,
  FileExcelOutlined,
  LeftCircleOutlined,
  LeftOutlined,
  RightCircleOutlined,
  RightOutlined,
  RotateRightOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import { exportExcel } from '@uni/utils/src/excel-export';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import { modalSelectedColumns } from '@/pages/archive/columns';
import clsx from 'clsx';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import './index.less';
import ProFormContainer from '@uni/components/src/pro-form-container/index';
import { PrintFormItems } from './formItems';
import { Emitter } from '@uni/utils/src/emitter';
import { PRINT_EVENTS } from '../eventConstants';
import ImageGallery from 'react-image-gallery';
import GalleryItem from './components/GalleryItem';
const PrintRegister = () => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  // barcode form
  const proFormRef = useRef<ProFormInstance>();

  // 复印信息 form
  const [editForm] = ProForm.useForm<ProFormInstance>();

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();
  const [revertRecord, setRevertRecord] = useSafeState(null);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagTraceRecordItem & { uuid: string }>, IReducer>
  >(tableReducer, InitTableState);

  // modal state
  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<SwagTraceRecordItem[]>, IReducer>
  >(modalReducer, {
    ...InitModalState,
    specialData: undefined,
  });
  // modal selected table key
  const [selectedRecordKey, setSelectedRecordKey] = useSafeState([]);
  // modal columns key
  const [modalColumns, setModalColumns] = useSafeState([]);
  // modal alert
  const [modalAlert, setModalAlert] = useSafeState(false);

  // 节流隐式标识
  const hiddenLoading = useRef(false);
  // 还是使用ref来替换document.getId
  const barCodeRef = useRef<InputRef>(null);

  // 实际签收数
  const [actualCnt, setActualCnt] = useSafeState(0);
  // 实际签收数编辑状态
  const actualCntInputRef = useRef();

  // 设计思路是先判断是不是相同的 不是就没事 是就进debounce判断
  const newestBarCode = useRef(undefined);

  // enter 监听
  useKeyPress(
    'enter',
    () => {
      console.log('press enter only');
      if (hiddenLoading.current) return;
      if (proFormRef.current.getFieldValue('BarCode')) {
        hiddenLoading.current = true;
        proFormRef.current
          .validateFields()
          .then((values) => {
            if (values?.SignType?.value === 'BarCode') {
              searchByBarCodeReq(values.BarCode);
            } else {
              // 对于非BarCode类型，使用SignType作为key，BarCode作为value
              searchOneReq({
                [values.SignType.value]: values.BarCode,
              });
            }
          })
          .catch((err) => {
            hiddenLoading.current = false;
          });
      }
    },
    {
      exactMatch: true,
      target: document.getElementById('signInForm'),
    },
  );

  // 查询结果统一处理（处理方式一致）
  const searchResultHandler = (barCode: string, res: any) => {
    // 重置节流标识
    hiddenLoading.current = false;
    if (!isRespErr(res)) {
      let resData;
      if (res?.data?.Items) {
        resData = res?.data?.Items?.slice();
      } else {
        resData = res?.data?.slice();
      }

      if (resData?.length === 1) {
        // 单个，直接插入table
        SearchTableDispatch({
          type: TableAction.dataUnshiftUniq,
          payload: {
            data: {
              ...(Array.isArray(resData) ? resData?.at(0) : resData?.data),
              InsertTime: dayjs(),
            },
            key: 'BarCode',
            overWriteBy: {
              key: 'isCorrect',
              value: true,
            },
          },
        });
        // 重置输入框
        proFormRef.current.resetFields(['BarCode']);
      } else if (resData?.length > 1) {
        // 多条，modal提示处理
        ModalStateDispatch({
          type: ModalAction.change,
          payload: {
            visible: true,
            record: resData,
            specialData: barCode,
            actionType: undefined,
          },
        });
        proFormRef.current.resetFields(['BarCode']);
      } else {
        // 没查到数据
        Modal.confirm({
          title: `查无数据`,
          content: '请确认病案标识填写正确',
          onOk: () => {
            proFormRef.current.resetFields(['BarCode']);
            focusBarCode();
          },
          onCancel: () => {
            proFormRef.current.resetFields(['BarCode']);
            focusBarCode();
          },
          cancelButtonProps: { style: { display: 'none' } },
        });
      }
    }
  };
  // 批量查询，先查，再签收
  // 统一查询 除了barcode类型的其他都走这里
  const searchOneReq = async (searchParam) => {
    if (!searchParam) return;

    // 构建请求数据
    const requestData = {
      ...searchParam,
      SkipCount: 0,
      MaxResultCount: 999999,
    };

    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetPrintTraceRecordList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetPrintTraceRecordList`,
          method: 'POST',
          data: requestData,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    // 获取searchParam中的值并转为字符串用于显示
    const displayValue = String(Object.values(searchParam)[0]);
    searchResultHandler(displayValue, res);
  };

  // 特殊debounce 扫码枪才需要
  const handleBarCodeDebounce = (barCode) => {
    if (SearchTable?.data?.findIndex((d) => d?.BarCode === barCode) > -1) {
      // 判断时间 存在与表内 长于 特定时间，比如 2s
      if (
        dayjs().diff(
          SearchTable?.data?.find((d) => d?.BarCode === barCode)?.InsertTime,
        ) < 2000
      ) {
        // 拦截
        hiddenLoading.current = false;
        proFormRef?.current.setFieldValue('BarCode', '');
        return true;
      }
    }

    newestBarCode.current = barCode;
    return false;
  };

  // 扫码枪条码，走这里
  const searchByBarCodeReq = async (barCode: string) => {
    // 做成 hooks
    // 扫之前，先做判断 判断条码号与上一次扫的 & 表格内的 是否相符 如果能匹配出来则特殊报错
    if (handleBarCodeDebounce(barCode)) {
      // 拦截
      return;
    }

    if (!barCode) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `TraceRecord/GetPrintTraceRecordList`,
        requestParams: {
          url: `Api/Mr/TraceRecord/GetPrintTraceRecordListByBarCode`,
          method: 'POST',
          data: {
            BarCode: barCode,
            SkipCount: 0,
            MaxResultCount: 999999,
          },
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    searchResultHandler(barCode, res);
  };

  // 最后确认登记时才调 由用户触发
  const reqActionReq = async (data: any) => {
    if (!data) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `PrintRecord/Register`,
        requestParams: {
          url: `Api/Mr/PrintRecord/Register`,
          method: 'POST',
          data,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });
    if (!isRespErr(res)) {
      let { data } = res;
      console.log(res);
      // special
      if (data?.StatusCode) {
        if (data?.StatusCode !== '200') {
          notification.error({
            message: data?.ErrMsg,
            description: '请查看表格中被标红的数据',
            placement: 'top',
            duration: 3,
          });
          // message.error(data?.ErrMsg);
          // 这边判断Data
          SearchTableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: SearchTable?.data?.map((d) => ({
                ...d,
                IsRespErr:
                  data?.Data?.findIndex((v) => v?.BarCode === d?.BarCode) > -1,
              })),
            },
          });
        } else {
          message.success('登记成功');
          // 清空页面
          SearchTableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: [],
            },
          });
          editForm.resetFields();
        }
      }
    } else {
      message.error('登记失败');
    }
  };

  // columns 处理，主要用于处理options
  const columnsSolver = useMemo(() => {
    return SearchTable.columns
      ? columnsHandler(SearchTable.columns, {
          dataIndex: 'option',
          title: '操作',
          visible: true,
          width: 60,
          align: 'center',
          fixed: 'right',
          render: (
            text,
            record: SwagTraceRecordItem & { isCorrect: boolean },
          ) => {
            return (
              <Popconfirm
                key="delete"
                title="确定要删除？"
                onConfirm={(e) => {
                  e.stopPropagation();
                  SearchTableDispatch({
                    type: TableAction.dataFilt,
                    payload: {
                      key: 'BarCode',
                      value: record?.BarCode,
                    },
                  });
                }}
                onCancel={(e) => e.stopPropagation()}
              >
                <Tooltip title="删除">
                  <DeleteOutlined
                    className="icon_blue-color"
                    onClick={(e) => e.stopPropagation()}
                  />
                </Tooltip>
              </Popconfirm>
            );
          },
        })
      : [];
  }, [SearchTable.columns]);

  // columns处理
  useEffect(() => {
    if (columnsList?.['PrintTraceRecord'] && SearchTable.columns.length < 1) {
      SearchTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            [],
            columnsList['PrintTraceRecord'],
          ),
        },
      });
      setModalColumns(
        tableColumnBaseProcessor(
          modalSelectedColumns,
          columnsList['PrintTraceRecord'],
        ),
      );
    }
  }, [columnsList?.['PrintTraceRecord']]);

  const focusBarCode = () => {
    // 定位
    setTimeout(() => {
      barCodeRef.current?.focus({ cursor: 'end' });
    }, 20);
  };

  // onfinish
  const handleConfirmRegister = () => {
    editForm?.validateFields().then((values: any) => {
      console.log(values);
      if (SearchTable?.data?.length < 1) {
        message.error('病案为空！');
        return;
      }
      reqActionReq({
        ..._.omit(values, ['CopyCnt', 'PrintDocType']),
        // 图片
        ApplicationBlobIds: cameraPics.map((pic) => pic?.blobId),
        Records: SearchTable.data?.map((d) => ({
          BarCode: d?.BarCode,
          CopyCnt: values?.CopyCnt,
          Details: values?.PrintDocType?.map((type) => ({
            PrintDocType: type,
            PrintDocName: dictData?.['Mr']?.PrintDocType?.find(
              (dict) => dict?.Code === type,
            )?.Name,
            UnitPrice: 0,
            PaperCnt: 0,
          })),
        })),
      });
    });
  };

  // Emitter + 与相机拍照相关
  const [cameraPics, setCameraPics] = useSafeState([]);
  const [imagePreviewVisible, setImagePreviewVisible] = useSafeState(false);

  useEffect(() => {
    Emitter.on(PRINT_EVENTS.CAMERA_OUT, (pics) => {
      setCameraPics([...cameraPics, ...pics]);
    });

    Emitter.on(PRINT_EVENTS.CAMERA_PICS_VIEW, () => {
      setImagePreviewVisible(true);
    });

    Emitter.on(PRINT_EVENTS.APPLICANT_ID_CARD_TYPE_CHANGE, (value) => {
      editForm.setFieldValue('ApplicantIdCardType', value);
    });

    return () => {
      Emitter.off(PRINT_EVENTS.CAMERA_OUT);
      Emitter.off(PRINT_EVENTS.CAMERA_PICS_VIEW);
      Emitter.off(PRINT_EVENTS.APPLICANT_ID_CARD_TYPE_CHANGE);
    };
  }, [cameraPics]);

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <div className="print_code_form_wrapper">
              <ProForm
                layout="horizontal"
                className="print_code_form"
                grid
                id="signInForm"
                labelCol={{ flex: '120px' }}
                wrapperCol={{ flex: '200px' }}
                formRef={proFormRef}
                submitter={{
                  render: (props, doms) => {
                    return [
                      <Button
                        style={{
                          width: '120px',
                          position: 'absolute',
                          top: 0,
                          left: '340px',
                        }}
                        key="submit"
                        onClick={() => {
                          if (hiddenLoading.current) return;
                          hiddenLoading.current = true;
                          props.form
                            .validateFields()
                            .then((values) => {
                              if (values.SignType.value === 'BarCode') {
                                searchByBarCodeReq(values.BarCode);
                              } else {
                                // 对于非BarCode类型，使用SignType作为key，BarCode作为value
                                searchOneReq({
                                  [values.SignType.value]: values.BarCode,
                                });
                              }
                            })
                            .catch((err) => {
                              hiddenLoading.current = false;
                            });
                        }}
                      >
                        查询(Enter)
                      </Button>,
                    ];
                  },
                }}
              >
                <ProFormGroup>
                  <ProFormSelect
                    name="SignType"
                    colProps={{ flex: '120px' }}
                    allowClear={false}
                    initialValue={{
                      label: SigninType[0].title,
                      value: SigninType[0].value,
                    }}
                    fieldProps={{
                      labelInValue: true,
                      fieldNames: {
                        label: 'title',
                        value: 'value',
                      },
                    }}
                    rules={[{ required: true }]}
                    options={SigninType as any[]}
                  />
                  <ProFormDependency name={['SignType']}>
                    {({ SignType }) => {
                      return (
                        <ProFormText
                          id="barCodeInput"
                          colProps={{ flex: 'auto' }}
                          name="BarCode"
                          placeholder={
                            SignType?.value === 'BarCode'
                              ? '条码号(扫码)'
                              : `请输入${SignType?.label}`
                          }
                          fieldProps={{
                            ref: barCodeRef,
                          }}
                          rules={[{ required: true }]}
                        />
                      );
                    }}
                  </ProFormDependency>
                </ProFormGroup>
              </ProForm>
              <Space>
                {SearchTable.data?.length > 0 && (
                  <span style={{ display: 'flex', alignItems: 'baseline' }}>
                    病例数：
                    <Badge
                      count={SearchTable.data?.length}
                      // showZero={true}
                      color="#f4a741"
                    />
                  </span>
                )}
                <Divider type="vertical" />
                <Popconfirm
                  title="导出时会将错误的记录过滤掉"
                  onConfirm={(e) => {
                    let exportColumns = columnsSolver?.filter(
                      (columnItem) =>
                        columnItem.className?.indexOf('exportable') > -1 &&
                        columnItem.valueType !== 'option' &&
                        columnItem.dataIndex !== 'operation',
                    );
                    exportExcel(
                      exportColumns,
                      exportExcelDictionaryModuleProcessor(
                        exportColumns,
                        _.cloneDeep(SearchTable.data),
                      ),
                      `病案单份签收列表__${dayjs().format('YYYY-MM-DD')}`,
                      [],
                    );
                  }}
                  disabled={SearchTable.data?.length < 1}
                >
                  <Tooltip title="导出Excel">
                    <Button
                      type="text"
                      shape="circle"
                      disabled={SearchTable.data?.length < 1}
                      icon={<FileExcelOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
                <TableColumnEditButton
                  {...{
                    columnInterfaceUrl:
                      'Api/Mr/TraceRecord/GetPrintTraceRecordList',
                    onTableRowSaveSuccess: (columns) => {
                      // 这个columns 存到dva
                      dispatch({
                        type: 'global/saveColumns',
                        payload: {
                          name: 'PrintTraceRecord',
                          value: columns,
                        },
                      });
                      SearchTableDispatch({
                        type: TableAction.columnsChange,
                        payload: {
                          columns: tableColumnBaseProcessor([], columns),
                        },
                      });
                    },
                  }}
                />
              </Space>
            </div>
            <div>
              <UniTable
                id="trace_record"
                rowKey="BarCode"
                showSorterTooltip={false}
                loading={
                  loadings['TraceRecord/GetPrintTraceRecordList'] || false
                }
                dictionaryData={dictData}
                columns={columnsSolver} // columnsHandler
                dataSource={SearchTable.data}
                scroll={{ x: 'max-content' }}
                widthCalculate
                widthDetectAfterDictionary
                rowClassName={(record) => {
                  if (record?.IsRespErr) return 'err_by_api';
                }}
              />
            </div>
            <Typography.Title level={4} style={{ marginTop: '0.3em' }}>
              复印信息
            </Typography.Title>
            <Spin spinning={!dictData?.['Mr']?.PrintDocType}>
              <ProFormContainer
                preserve={false}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 16 }}
                rowProps={{ gutter: 8 }}
                searchOpts={
                  dictData?.['Mr']?.PrintDocType
                    ? PrintFormItems(
                        dictData?.['Mr']?.ApplicantRealtion,
                        dictData?.['Mr']?.ApplicantIdCardType,
                        dictData?.['Mr']?.PrintReason,
                        dictData?.['Mr']?.PrintDocType,
                        cameraPics,
                      )
                    : []
                }
                form={editForm}
                submitter={{
                  render: (props, doms) => {
                    return [
                      <Button
                        type="primary"
                        style={{
                          width: '20%',
                          display: 'block',
                          margin: '0 auto',
                        }}
                        key="submit"
                        onClick={() => {
                          handleConfirmRegister();
                        }}
                      >
                        登记
                      </Button>,
                    ];
                  },
                }}
              />
            </Spin>
          </Card>
        </Col>
      </Row>

      <Modal
        title="确认病案"
        open={ModalState.visible}
        width={900}
        onOk={(e) => {
          if (
            ModalState.record.findIndex(
              (d) => d.BarCode === selectedRecordKey?.at(0),
            ) > -1
          ) {
            // 直接插入table
            SearchTableDispatch({
              type: TableAction.dataUnshiftUniq,
              payload: {
                data: {
                  ...ModalState.record?.[
                    ModalState.record.findIndex(
                      (d) => d.BarCode === selectedRecordKey?.at(0),
                    )
                  ],
                  InsertTime: dayjs(),
                },
                key: 'BarCode',
                overWriteBy: {
                  key: 'isCorrect',
                  value: true,
                },
              },
            });
            // 把modal关闭
            ModalStateDispatch({
              type: ModalAction.init,
            });
          } else {
            // 没选
            setModalAlert(true);
          }
        }}
        okButtonProps={{
          loading: loadings[`Tracing/${ReqActionType.mrRoomSignIn}`],
        }}
        onCancel={(e) => {
          // 重置节流标识
          hiddenLoading.current = false;
          focusBarCode();
          ModalStateDispatch({
            type: ModalAction.init,
          });
          setSelectedRecordKey([]);
          setModalAlert(false);
        }}
      >
        <UniTable
          id="multi_record_check"
          rowKey="BarCode"
          showSorterTooltip={false}
          loading={
            loadings['TraceRecord/GetPrintTraceRecordList'] ||
            loadings[`Tracing/${ReqActionType.mrRoomSignIn}`] ||
            false
          }
          columns={modalColumns} // columnsHandler
          dataSource={ModalState.record}
          scroll={{ x: 'max-content' }}
          tableAlertRender={() => {
            return modalAlert ? (
              <Alert
                message="请选择一个病案"
                description="请选择一个病案再点击确认，如果没有查询到目标病案请确认信息输入正确"
                type="error"
                closable
                onClose={() => {
                  setModalAlert(false);
                }}
              />
            ) : (
              false
            );
          }}
          tableAlertOptionRender={false}
          rowSelection={{
            alwaysShowAlert: true,
            type: 'radio',
            selectedRowKeys: selectedRecordKey,
            onChange: (
              selectedRowKeys: React.Key[],
              selectedRows: SwagTraceRecordItem[],
            ) => {
              setSelectedRecordKey(selectedRowKeys);
              setModalAlert(false);
            },
          }}
          onRow={(record) => {
            return {
              onClick: (event) => {
                setSelectedRecordKey([record?.BarCode]);
              },
            };
          }}
        />
      </Modal>

      {/* 图片预览 */}
      <Modal
        width={1000}
        open={imagePreviewVisible}
        onCancel={() => setImagePreviewVisible(false)}
        okButtonProps={{ style: { display: 'none' } }}
        closable={false}
        cancelText="关闭"
      >
        <ImageGallery
          items={cameraPics.map((pic) => ({
            original: pic.imgSrc,
            thumbnail: pic.imgSrc,
          }))}
          showFullscreenButton={false}
          showPlayButton={false}
          renderItem={(item, index) => (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'end',
              }}
              key={index}
            >
              <Button
                style={{ marginBottom: '10px' }}
                onClick={() => {
                  setCameraPics(
                    cameraPics.filter((pic) => pic.imgSrc !== item.original),
                  );
                }}
                icon={<DeleteOutlined />}
              >
                删除该图片
              </Button>
              <GalleryItem {...item} />
            </div>
          )}
        />
      </Modal>
      {/* <div style={{ display: 'none' }}>
        <Image.PreviewGroup
          preview={{
            visible: imagePreviewVisible,
            onVisibleChange: (vis) => setImagePreviewVisible(vis),
          }}
          icons={{
            zoomIn: (
              <DeleteOutlined
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            ),
            close: <CloseOutlined />,
            left: <LeftCircleOutlined />,
            right: <RightCircleOutlined />,
          }}
        >
          {cameraPics.map((pic) => (
            <Image key={pic.blodId} src={pic.imgSrc} />
          ))}
        </Image.PreviewGroup>
      </div> */}
    </>
  );
};

export default PrintRegister;
