import pick from 'lodash/pick';
import assign from 'lodash/assign';
import keys from 'lodash/keys';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { CardBundleInfo, IcdeRespItem } from '@/pages/dmr/network/interfaces';
import { SubmitResult } from '@/pages/dmr/interfaces';
import uniq from 'lodash/uniq';

const dmrSaveBaseInfoReqV2 = async (
  originCardInfo: CardBundleInfo,
  formFieldsValue,
  hisId,
  modelData,
  dmrProcessorInstance,
  // 用于 历史数据
  interfaceUrl,
) => {
  let data: CardBundleInfo = Object.assign({}, originCardInfo);

  let savedParams = await dmrProcessorInstance.cardSaveCheckParamProcessor(
    data,
    originCardInfo,
    formFieldsValue,
    modelData,
  );
  console.log('dmrSaveBaseInfoReqV2', formFieldsValue, savedParams);
  data = Object.assign({}, savedParams);

  let reduced = new CardBundleInfo();
  let saveParams = assign(reduced, pick(data, keys(reduced)));
  saveParams['HisId'] = hisId;

  return uniCommonService(interfaceUrl ?? 'Api/Dmr/DmrCardBundle/Submit', {
    method: 'POST',
    data: saveParams,
  });
};

export const dmrIcdeReport = (icdeId: number, report: boolean) => {
  if (icdeId) {
    uniCommonService('Api/Insur/InsurCard/UpdateCardIcdeReported', {
      method: 'POST',
      data: {
        IcdeId: icdeId,
        isReported: report,
      },
    });
  }
};

export const dmrOperationReport = (operationId: number, report: boolean) => {
  if (operationId) {
    uniCommonService('Api/Insur/InsurCard/UpdateCardOperReported', {
      method: 'POST',
      data: {
        OperId: operationId,
        isReported: report,
      },
    });
  }
};

export const saveCardInfoV2 = async (
  originCardInfo: any,
  formFieldValues: any,
  hisId: string,
  modelData: any,
  dmrProcessorInstance: any,
  // 用于 历史数据
  interfaceUrl?: string,
) => {
  let saveResponse: RespVO<SubmitResult> = await dmrSaveBaseInfoReqV2(
    originCardInfo,
    formFieldValues,
    hisId,
    modelData,
    dmrProcessorInstance,

    // 用于 历史数据
    interfaceUrl,
  );

  return saveResponse;
};

export const saveGlobalDmrIndexLayouts = (
  layouts: any,
  configModule: string,
) => {
  let identityParam = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
  };

  let data = {
    ...identityParam,
    ConfigModule: configModule,
    FullReplace: true,
    values: layouts,
  };

  return uniCommonService('Api/Sys/ClientKitSys/SetValue', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

export const dmrExportBaseInfoReqV2 = async (
  originCardInfo,
  formFieldsValue,
  hisId,
  modalData,
  dmrProcessorInstance,
) => {
  let data: CardBundleInfo = Object.assign({}, originCardInfo);

  let saveParams = await dmrProcessorInstance.cardSaveCheckParamProcessor(
    data,
    originCardInfo,
    formFieldsValue,
    modalData,
  );

  data = Object.assign({}, saveParams);

  let reduced = new CardBundleInfo();
  let exportParams = assign(reduced, pick(data, keys(reduced)));
  exportParams['HisId'] = hisId;

  return uniCommonService('Api/Dmr/DmrCardBundle/ExportCardPdf', {
    method: 'POST',
    data: exportParams,
  });
};

export const saveDmrLayout = (layout: any, configModule: string) => {
  uniCommonService('Api/Sys/ClientKitSys/SetValue', {
    method: 'POST',
    requestType: 'json',
    data: {
      IdentityCode: 'Global',
      IdentityType: 'Global',
      configModule: configModule,
      values: layout,
      fullReplace: true,
    },
  });
};

export const saveGlobalDmrModuleInfo = (
  contentLayout: any,
  headerLayout: any,
) => {
  let dmrModuleInfo: { [key: string]: string[] } = {};

  // headerLayout
  headerLayout?.['lg']?.forEach((item) => {
    dmrModuleKeyGroupProcessor(item, dmrModuleInfo);
  });

  // contentLayout
  contentLayout?.['lg']?.forEach((item) => {
    dmrModuleKeyGroupProcessor(item, dmrModuleInfo);
  });

  uniCommonService('Api/Sys/ClientKitSys/SetValue', {
    method: 'POST',
    requestType: 'json',
    data: {
      IdentityCode: 'Global',
      IdentityType: 'Global',
      configModule: 'DmrModules',
      values: dmrModuleInfo,
      fullReplace: true,
    },
  });
};

const dmrModuleKeyGroupProcessor = (
  item: any,
  dmrModuleInfo: { [key: string]: string[] },
) => {
  let groupKey = 'Default';
  if (item?.data?.props?.modelDataKey) {
    if (item?.data?.props?.modelDataGroup) {
      groupKey = item?.data?.props?.modelDataGroup;
    }

    dmrModuleInfo[groupKey] = uniq([
      ...(dmrModuleInfo[groupKey] ?? []),
      item?.data?.props?.modelDataKey,
    ]);
  }

  if (item?.data?.props?.suffixProps?.modelDataKey) {
    if (item?.data?.props?.modelDataGroup) {
      groupKey = item?.data?.props?.modelDataGroup;
    }

    dmrModuleInfo[groupKey] = uniq([
      ...(dmrModuleInfo[groupKey] ?? []),
      item?.data?.props?.modelDataGroup,
    ]);
  }
};

export const saveGlobalDmrMenuConfig = (dmrMenus: any) => {
  uniCommonService('Api/Sys/ClientKitSys/SetValue', {
    method: 'POST',
    requestType: 'json',
    data: {
      IdentityCode: 'Global',
      IdentityType: 'Global',
      configModule: 'DmrLeftMenus',
      values: {
        DmrLeftMenus: dmrMenus,
      },
      fullReplace: true,
    },
  });
};

export const saveGlobalDmrThemeConfig = (dmrTheme: any) => {
  uniCommonService('Api/Sys/ClientKitSys/SetValue', {
    method: 'POST',
    requestType: 'json',
    data: {
      IdentityCode: 'Global',
      IdentityType: 'Global',
      configModule: 'DmrTheme',
      values: dmrTheme,
      fullReplace: true,
    },
  });
};
