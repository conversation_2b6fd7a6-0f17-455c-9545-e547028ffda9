import React, { useEffect, useState } from 'react';
import './index.less';
import {
  Col,
  Form,
  Modal,
  Row,
  Input,
  Tag,
  Timeline,
  Spin,
  message,
} from 'antd';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import dayjs from 'dayjs';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { TimeScape } from '@uni/components/src/date-mask/timescape';
import { handleMrActionApi } from '@/utils/utils';

interface SinginForTraceModalProps {
  barCode?: string;
  dmrSignInDate?: string;
  onReqDone?: (signInDate: string) => void;
}
const format = 'YYYY-MM-DD';
const SinginForTraceModal = (props: SinginForTraceModalProps) => {
  const [open, setOpen] = useState(false);
  const [selectiveSignInDate, setSelectiveSignInDate] = useState(
    dayjs().format('YYYY-MM-DD'),
  );
  const [revertLoadingMessage, setRevertLoadingMessage] = useState<
    (() => void) | null
  >(null);

  useEffect(() => {
    Emitter.on(EventConstant.DMR_SIGNIN_FOR_TRACE_MODAL, (status) => {
      // 这边判断props.dmrSignInDate是否有值
      if (props?.dmrSignInDate) {
        // 如果有值，说明是撤销签收
        dmrRevertTraceReq();
      } else {
        setOpen(status);
      }
    });

    return () => {
      Emitter.off(EventConstant.DMR_SIGNIN_FOR_TRACE_MODAL);
    };
  }, [props?.dmrSignInDate]);

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        // 让时间选择器获取焦点
        document
          .getElementById('formItem#selectiveSignInDate#Years#TimeScape')
          ?.focus();
      }, 100);
    }
  }, [open]);

  // sign in req
  const { loading: dmrTraceLoading, run: dmrTraceReq } = useRequest(
    () => {
      return uniCommonService('Api/Mr/Tracing/SelectiveMrRoomSignIn', {
        method: 'POST',
        data: {
          BarCode: props?.barCode,
          SelectiveSignInDate: selectiveSignInDate,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let result = handleMrActionApi(response.data);
          console.log('dmrTraceReq result', result);
          result?.isCorrect
            ? message.success('签收成功')
            : message.error(result?.errMsg?.join('。/n'));

          if (result?.isCorrect) {
            setOpen(false);
            props?.onReqDone &&
              props?.onReqDone(result?.data?.[0]?.DmrSignInDate);
          }
        } else {
          message.error(response?.message || '签收失败，请稍后再试');
        }
      },
    },
  );

  // revert sign in req
  const { loading: dmrRevertTraceLoading, run: dmrRevertTraceReq } = useRequest(
    () => {
      return uniCommonService('Api/Mr/Tracing/MrRoomRevertSignIn', {
        method: 'POST',
        data: {
          BarCodes: [props?.barCode],
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let result = handleMrActionApi(response.data);
          result?.isCorrect
            ? message.success('撤销签收成功')
            : message.error(result?.errMsg?.join('。/n'));

          if (result?.isCorrect) {
            props?.onReqDone && props?.onReqDone(undefined);
          }
        } else {
          message.error(response?.data?.ErrMsg || '撤销签收失败，请稍后再试');
        }
      },
    },
  );

  // 监听撤销签收请求的loading状态
  useEffect(() => {
    if (dmrRevertTraceLoading) {
      // 开始loading时显示消息
      const hide = message.loading('撤销签收中');
      setRevertLoadingMessage(() => hide);
    } else {
      // loading结束时关闭消息
      if (revertLoadingMessage) {
        revertLoadingMessage();
        setRevertLoadingMessage(null);
      }
    }
  }, [dmrRevertTraceLoading]);

  return (
    <Modal
      title="病案签收"
      open={open}
      onCancel={() => setOpen(false)}
      onOk={() => {
        dmrTraceReq();
      }}
      confirmLoading={dmrTraceLoading}
      mask={false}
      destroyOnClose={true}
      bodyStyle={{
        display: 'flex',
        gap: '10px',
        alignItems: 'baseline',
      }}
      zIndex={10000}
    >
      <label
        style={{
          color: 'rgb(235, 87, 87)',
          fontWeight: 'bold',
          whiteSpace: 'nowrap',
          fontSize: '16px',
        }}
      >
        签收时间
      </label>
      <div className="signin-trace-input-container">
        <TimeScape
          value={selectiveSignInDate}
          dateSeparator={'.'}
          formKey="selectiveSignInDate"
          format={format}
          onValueChange={(dateString) => {
            console.log('renderCompactInput', dateString);
            // setInputDate(event);
            let currentDate = dayjs(dateString, format, true);
            if (currentDate.isValid()) {
              let formatDate = currentDate.format(format);
              if (formatDate !== selectiveSignInDate) {
                // setInputDate(formatDate);
                setSelectiveSignInDate(formatDate);
              }
            }
          }}
        />
      </div>
    </Modal>
  );
};

export default SinginForTraceModal;
