import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { UniDmrDragEditOnlyTable, UniDragEditTable } from '@uni/components/src';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { Form, Modal } from 'antd';
import { icuColumns } from '@/pages/dmr/columns';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { icuTable } from '@/pages/dmr/fields/base';
import cloneDeep from 'lodash/cloneDeep';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import { generateUniqueNumberId } from '@uni/utils/src/utils';

interface DragTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];

  underConfiguration?: boolean;

  onChange?: (value: any) => void;
}

// TODO 数据 data 改动

interface IcuItem {
  id: number | string;
  InsurCardId?: string;
  HisId?: string;
  IcuCode?: string;
  InpoolIcuTime?: string;
  OutIcuTime?: string;
  IcuDuration?: string;
}

const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_ICU_ADD, event.target.id);
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(1);
    let index = parseInt(indexString);
    if (index > -1) {
      Emitter.emit(EventConstant.DMR_ICU_DELETE, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#icuTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#icuTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#icuTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#icuTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('icuTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    Emitter.emit(getArrowUpDownEventKey('icuTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};

const IcuDragTable = (props: DragTableProps) => {
  const itemRef = React.useRef<any>();

  const [form] = Form.useForm();

  const icuDataSource = Form.useWatch('icuTable', props?.form) ?? [];

  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);

  const [tableDataSourceSize, setTableDataSourceSize] = useState<number>(
    icuDataSource?.length,
  );

  const [tableColumns, setTableColumns] = useState<any[]>([]);

  useEffect(() => {
    setTableDataSourceSize(icuDataSource?.length);
  }, [icuDataSource]);

  const lineUpDownEvents = {
    LINE_UP: (event) => {
      console.log('LINE_UP', event);
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('icuTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue('icu-table').length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('icuTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  useEffect(() => {
    setTimeout(() => {
      waitFocusElementRefocusBySelector(waitFocusId);
    }, 100);
  }, [waitFocusId]);

  useEffect(() => {
    let columns = mergeColumnsInDmrTable(
      props?.columns,
      icuColumns,
      'IcuDragTable',
    );

    setTableColumns(columns);
  }, [props?.columns]);

  useEffect(() => {
    // delete事件
    Emitter.on(getDeletePressEventKey('icuTable'), (itemId) => {
      // key 包含 index 和 其他的东西
      console.log('icuTable', itemId);
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);
      let clearKeys = [key];
      clearValuesByKeys(clearKeys, index);
    });

    Emitter.on(EventConstant.DMR_ICU_ADD, () => {
      let rowData = {
        id: generateUniqueNumberId(),
      };

      let tableData = props?.form?.getFieldValue('icu-table');

      tableData.splice(tableData.length, 0, rowData);

      // setIcuDataSource(tableData);
      // props?.onChange && props?.onChange(tableData);
      props?.form?.setFieldValue('icu-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('icu-table');

      // TODO 这个要看一下
      setWaitFocusId(
        `div[id=icuTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setTableDataSourceSize(tableData?.length);
    });

    Emitter.on(EventConstant.DMR_ICU_DELETE, (index) => {
      if (index > -1) {
        let tableData = props?.form?.getFieldValue('icu-table');
        tableData.splice(index, 1);

        props?.form?.setFieldValue('icu-table', cloneDeep(tableData));
        triggerFormValueChangeEvent('icu-table');

        // 删除的时候 给出当前那个选中的
        // 当前选中的意思是表格中的上一个存在即是上表格中上一个的最后一个
        let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
        if (dataItems?.length > 0) {
          setWaitFocusId(
            `div[id=icuTable] tbody > tr:nth-child(${
              index >= dataItems.length - 1 ? dataItems.length : index + 1
            }) > td input`,
          );
        }
        setTableDataSourceSize(tableData?.length);
      }
    });

    Emitter.on(getArrowUpDownEventKey('icuTable'), (payload) => {
      const icuDataSource = props?.form?.getFieldValue('icu-table');
      let type = payload?.type;
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > icuDataSource?.length - 2) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('icuTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      resizeObserver?.disconnect();

      Emitter.off(EventConstant.DMR_ICU_ADD);
      Emitter.off(EventConstant.DMR_ICU_DELETE);

      Emitter.off(getDeletePressEventKey('icuTable'));
      Emitter.off(getArrowUpDownEventKey('icuTable'));
    };
  }, []);

  const clearValuesByKeys = (keys, index) => {
    const icuDataSource = props?.form?.getFieldValue('icu-table');
    let formItemId = icuDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });
    // 更新form
    let tableData = icuDataSource.slice();
    keys?.forEach((key) => {
      tableData[index][key] = undefined;
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue('icu-table', cloneDeep(tableData));
    triggerFormValueChangeEvent('icu-table');
  };

  const columnsProcessor = (columns) => {
    return columns.map((columnItem) => {
      if (!columnItem.onCell) {
        columnItem.onCell = (record, index) => {
          if (record?.id === 'ADD') {
            return {
              colSpan: 0,
            };
          }

          return {};
        };

        if (columnItem.children) {
          columnItem.children = columnsProcessor(columnItem.children);
        }
      }

      return columnItem;
    });
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      formItemContainerClassName={'form-content-item-container'}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'icuTable'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{
        x: 'max-content',
      }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      columns={columnsProcessor(tableColumns)}
      dataSource={(props?.form?.getFieldValue('icu-table') ?? [])
        ?.filter((item) => item.id !== 'ADD')
        ?.map((item) => {
          if (!item['id']) {
            item['id'] = generateUniqueNumberId();
          }

          return item;
        })
        ?.concat({
          id: 'ADD',
        })}
      rowKey={'id'}
      onValuesChange={(tableData) => {
        // setIcuDataSource(tableData);

        props?.form?.setFieldValue('icu-table', tableData);
        triggerFormValueChangeEvent('icu-table');
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        props?.form?.setFieldValue('icu-table', cloneDeep(tableData));
        // props?.form?.setFieldValue('icuTable', cloneDeep(newTableData));
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('icu-table');
      }}
    />
  );
};

export default IcuDragTable;
