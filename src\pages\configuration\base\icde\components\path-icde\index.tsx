import { pathologyIcdeDictColumns } from '@/pages/configuration/base/columns';
import {
  Button,
  Form,
  Modal,
  TableProps,
  Card,
  Input,
  Space,
  message,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useRequest } from 'umi';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import PathologyIcdeDictionaryAdd from '@/pages/configuration/base/icde/components/path-icde/components/add';
import { PathologyIcdeDictionaryItem } from '@/pages/configuration/base/interfaces';
import { v4 as uuidv4 } from 'uuid';
import UniEditableTable from '@uni/components/src/table/edittable';
import { useDebounce } from 'ahooks';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import ISD from '@uni/components/src/infinite-scroll';
import DebounceSelect from '@/components/debounceSelect';
import { UniTable } from '@uni/components/src';
interface PathologyIcdeDictionaryProps {
  moduleGroup?: string;
}
const Event = 'PathIcde';

const InfiniteScrollId = 'path-icde-isd';

const PathologyIcdeDictionary = (props?: PathologyIcdeDictionaryProps) => {
  const [form] = Form.useForm();

  const ref = useRef<any>();

  // keywords
  const [searchKeywords, setSearchKeywords] = useState(undefined);
  const debouncedKeywords = useDebounce(searchKeywords, { wait: 1000 });

  const [pathologyIcdeDictionaryAdd, setPathologyIcdeDictionaryAdd] =
    useState(false);

  const [
    pathologyIcdeDictionaryTableDataSource,
    setPathologyIcdeDictionaryTableDataSource,
  ] = useState([]);

  const [pathologyIcdeDictionaryColumns, setPathologyIcdeDictionaryColumns] =
    useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  // 几个infinite新值
  const infiniteChildRef = useRef<any>();
  const [infiniteData, setInfiniteData] = useState([]);
  const [infiniteLoading, setInfiniteLoading] = useState(false);
  const [editIndex, setEditIndex] = useState(-1);

  useEffect(() => {
    pathologyIcdeConfigurationColumnsReq();
    // pathologyIcdeDictionaryReq(
    //   backPagination?.current || 1,
    //   backPagination?.pageSize || 10,
    // );
  }, []);

  // useEffect(() => {
  //   pathologyIcdeDictionaryReq(
  //     backPagination?.current || 1,
  //     backPagination?.pageSize || 10,
  //   );
  // }, [debouncedKeywords]);

  // const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
  //   setBackPagination({
  //     ...backPagination,
  //     current: pagi.current,
  //     pageSize: pagi.pageSize,
  //   });

  //   pathologyIcdeDictionaryReq(pagi.current, pagi.pageSize);
  // };

  const {
    loading: pathologyIcdeDictionaryLoading,
    run: pathologyIcdeDictionaryReq,
  } = useRequest(
    (current, pageSize) => {
      let data = {
        ModuleGroup: props?.moduleGroup,
        DtParam: {
          Draw: 1,
          Start: (current - 1) * pageSize,
          Length: pageSize,
        },
        Keyword: debouncedKeywords,
      };
      return uniCommonService(
        'Api/Sys/CodeSys/GetDmrPathologyWithAllCompares',
        {
          method: 'POST',
          data: data,
        },
      );
    },
    {
      manual: true,
      formatResult: (
        response: RespVO<TableResp<any, PathologyIcdeDictionaryItem>>,
      ) => {
        if (response.code === 0 && response?.statusCode === 200) {
          let tableDataSource = response?.data?.data.slice();
          setPathologyIcdeDictionaryTableDataSource(
            tableDataSource.map((item) => {
              item['id'] = uuidv4();

              return item;
            }),
          );

          setBackPagination({
            ...backPagination,
            total: response?.data?.recordsFiltered || 0,
          });
        } else {
          setPathologyIcdeDictionaryTableDataSource([]);
        }
      },
    },
  );

  const { run: pathologyIcdeConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/CodeSys/GetDmrPathologyWithAllCompares',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setPathologyIcdeDictionaryColumns(
            tableColumnBaseProcessor(
              pathologyIcdeDictColumns(Event),
              response?.data?.Columns,
            ),
          );
        } else {
          setPathologyIcdeDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: pathologyIcdeUpsertLoading, run: pathologyIcdeUpsertReq } =
    useRequest(
      (values) => {
        let data = {};

        data = {
          ...values,
          ModuleGroup: props?.moduleGroup,
        };

        return uniCommonService(
          'Api/Sys/CodeSys/UpsertDmrPathologyAllCompare',
          {
            method: 'POST',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.statusCode === 200) {
            setPathologyIcdeDictionaryAdd(false);
            // pathologyIcdeDictionaryReq(
            //   backPagination?.current,
            //   backPagination?.pageSize,
            // );
            return response?.data;
          }
        },
        onSuccess: (data, ret) => {
          // 现在这边hack 因为无限滚动 不能再重新获取数据 需要使用记录的index与编辑的结果
          // 最好的是upsert该接口返回后端结果，确保数据正确
          if (editIndex > -1 && data) {
            setInfiniteData(
              infiniteData?.map((d, i) => {
                return d?.PathologyId === data ? { ...d, ...ret?.at(0) } : d;
              }),
            );
            setEditIndex(-1); // reset
          } else if (editIndex === -1 && data) {
            // 新增的情况 之后讨论下怎么进行数据处理 应该是后端返回这个新增的当前位置？TODO
            Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
          }
        },
      },
    );

  const onPathologyIcdeDictionaryItemAdd = (values: any) => {
    pathologyIcdeUpsertReq(values);
  };

  const { run: icdeDeleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        ModuleGroup: props?.moduleGroup,
        pathologyId: values.PathologyId,
      };
      return uniCommonService('Api/Sys/CodeSys/DeleteDmrPathology', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
          return response?.data;
        }
      },
      onSuccess: (data, params) => {
        // pathologyIcdeDictionaryReq(
        //   backPagination?.current,
        //   backPagination?.pageSize,
        // );
        if (data) {
          setInfiniteData(
            infiniteData?.filter(
              (d, i) => d?.PathologyId !== params?.at(0)?.PathologyId,
            ),
          );
        }
      },
    },
  );

  useEffect(() => {
    Emitter.onMultiple(
      [
        `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_ICDE_SELECT}#${Event}`,
      ],
      (data) => {
        let currentFormValue = form.getFieldsValue()?.[data?.id];
        if (currentFormValue) {
          // set form value
          Object.keys(data?.values)?.forEach((key) => {
            currentFormValue[key] = data?.values?.[key];
          });
          form.setFieldValue(data?.id, currentFormValue);
        }
      },
    );

    Emitter.on(ConfigurationEvents.DMR_ICDE_EDIT, (data) => {
      if (data?.index > -1) {
        if (data?.record?.PathologyId) {
          // index 要记录 会使用到
          form.setFieldsValue(data.record);
          setEditIndex(data?.index);
          setPathologyIcdeDictionaryAdd(true);
        }
      }
    });

    Emitter.on(ConfigurationEvents.DMR_ICDE_DELETE, (data) => {
      if (data?.index > -1) {
        icdeDeleteReq(data.record);
      }
    });

    return () => {
      Emitter.offMultiple([
        `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_ICDE_SELECT}#${Event}`,
      ]);
      Emitter.off(ConfigurationEvents.DMR_ICDE_EDIT);
      Emitter.off(ConfigurationEvents.DMR_ICDE_DELETE);
    };
  }, [pathologyIcdeDictionaryTableDataSource]);

  // infinite 额外处理
  // debounce select
  async function fetchIcdeSelect(keyword: string): Promise<any[]> {
    if (!keyword) return;
    return await uniCommonService('Api/Dmr/DmrSearch/Icde', {
      params: {
        IsPatho: true,
        KeyWord: keyword,
        SkipCount: 0,
        MaxResultCount: 100,
      },
    });
  }

  // search emitter
  useEffect(() => {
    Emitter.on(
      EventConstant.INFINITE_SCROLL_CB_FETCH,
      async ({ searchedValue, cb, reqData, isdId }) => {
        if (isdId === InfiniteScrollId) {
          let response = await uniCommonService(
            'Api/Sys/CodeSys/SearchDmrPathologyWithAllCompare',
            {
              method: 'POST',
              data: {
                code: searchedValue,
              },
            },
          );
          if (response?.code === 0) {
            cb(response.data);
          }
        }
      },
    );

    return () => {
      Emitter.off(EventConstant.INFINITE_SCROLL_CB_FETCH);
    };
  }, []);

  return (
    <>
      <Card
        title="病理诊断列表"
        extra={
          <Space>
            <ConfigExcelTemplateHandler
              downloadTemplateApiObj={{
                apiUrl:
                  'Api/Sys/CodeSys/GetDmrPathologyAllCompareExcelTemplate',
              }}
              downloadPostData={{
                moduleGroup: props?.moduleGroup,
                exportName: '病理诊断列表',
              }}
              uploadXlsxApiObj={{
                apiUrl: 'Api/Sys/CodeSys/UploadDmrPathologyAllCompareExcelFile',
                onSuccess: () => {
                  Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
                  // pathologyIcdeDictionaryReq(
                  //   backPagination?.current,
                  //   backPagination?.pageSize,
                  // );
                },
              }}
              uploadPostData={{
                moduleGroup: props?.moduleGroup,
              }}
            />
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                // ref.current?.addEditRecord({
                //   id: uuidv4()
                // });
                setPathologyIcdeDictionaryAdd(true);
                form.setFieldValue('IsValid', true);
                form.setFieldValue('IsMain', true);
              }}
            >
              新增病理诊断
            </Button>
          </Space>
        }
      >
        <UniTable
          actionRef={ref}
          id={`pathology-icde-dictionary-table`}
          className={'pathology-icde-dictionary-table'}
          rowKey={'uuidv4'}
          scroll={{
            y: 480,
            x: 'max-content',
          }}
          headerTitle={
            // <Input.Search
            //   allowClear
            //   placeholder="请输入诊断编码或名称"
            //   value={searchKeywords}
            //   onChange={(e) => setSearchKeywords(e.target.value)}
            // />
            <DebounceSelect
              value={searchKeywords}
              placeholder="请输入诊断编码或名称"
              fetchOptions={fetchIcdeSelect}
              onChange={(newValue) => {
                setSearchKeywords(newValue);
              }}
              fieldNames={{
                label: 'Name',
                value: 'Code',
              }}
              style={{ width: '350px', marginBottom: '10px' }}
            />
          }
          toolBarRender={() => []}
          // backendPagination
          bordered={true}
          loading={
            pathologyIcdeDictionaryLoading ||
            infiniteLoading ||
            pathologyIcdeUpsertLoading
          }
          columns={pathologyIcdeDictionaryColumns}
          dataSource={infiniteData} // pathologyIcdeDictionaryTableDataSource
          clickable={false}
          pagination={false} // backPagination
          // onTableChange={backTableOnChange}
          // recordCreatorProps={false}
          // toolBarRender={null}
          // editable={{
          //   form: form,
          //   type: 'multiple',
          //   editableKeys: editableColumnKeys,
          //   onSave: async (rowKey, data, row) => {
          //     console.log(rowKey, data, row);
          //     pathologyIcdeUpsertReq(data);
          //   },
          //   actionRender: (row, config, defaultDoms) => {
          //     return [defaultDoms.save, defaultDoms.cancel];
          //   },
          //   onChange: setEditableColumnKeys,
          // }}
        />
      </Card>

      <Modal
        title={editIndex > -1 ? '编辑病理诊断' : '新增病理诊断'}
        open={pathologyIcdeDictionaryAdd}
        onOk={() => {
          onPathologyIcdeDictionaryItemAdd(form.getFieldsValue());
        }}
        onCancel={() => {
          setPathologyIcdeDictionaryAdd(false);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById(
          'pathology-icde-dictionary-table',
        )}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <PathologyIcdeDictionaryAdd form={form} event={Event} />
      </Modal>

      <ISD
        id={InfiniteScrollId}
        scrollFetchObj={{
          url: 'Api/Sys/CodeSys/GetDmrPathologyWithAllCompares',
        }}
        scrollDom={document?.querySelector(
          `div[id='pathology-icde-dictionary-table'] div[class=ant-table-body]`,
        )}
        reqData={{
          Code: searchKeywords,
        }}
        dataSource={infiniteData}
        setDataSource={setInfiniteData}
        setLoading={setInfiniteLoading}
        searchedValue={searchKeywords}
        searchedKey="Code"
      />
    </>
  );
};

export default PathologyIcdeDictionary;
