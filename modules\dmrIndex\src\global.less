//@import '~@uni/commons/src/scroll.less';
@import '~@uni/commons/src/global.less';
@import '~@uni/commons/src/style/variables.less';

#microAppContent {
  height: 100%;
}

.border-error {
  border: 1px solid @red-color !important;
  border-radius: 2px;
}

.border-active {
  border: 1px solid var(--focus-item-border-color) !important;
  border-radius: 2px;
  background-color: var(--focus-item-background-color) !important;
}

.tr-border-active {
  background-color: var(--table-focus-item-background-color) !important;

  td {
    background-color: var(--table-focus-item-background-color) !important;
  }

  > td:first-child {
    border-left: 4px solid var(--table-focus-item-left-border-color) !important;
  }
}

.dropdown-container {
  max-height: 180px;
  overflow-y: auto;

  border: 1px solid @border-color;
  border-radius: 4px;
}

.form-content-item-container {
  border: 1px solid transparent;
}

.form-content-item-container:focus-within {
  border: 1px solid var(--focus-item-border-color) !important;
  border-radius: 2px;
  background-color: var(--focus-item-background-color) !important;

  .select:focus-within {
    background-color: var(--focus-item-background-color) !important;
  }
}

.grid-stack-item-content {
  .ant-table-row:focus-within {
    background-color: var(--table-focus-item-background-color) !important;

    td {
      background-color: var(--table-focus-item-background-color) !important;
    }

    > td:first-child {
      border-left: 4px solid var(--table-focus-item-left-border-color) !important;
    }
  }

  .select:focus-within {
    background-color: var(--table-focus-item-background-color) !important;
  }
}

.ant-select-single .ant-select-selector .ant-select-selection-search {
  position: absolute;
  top: 0;
  right: 1px !important;
  bottom: 0;
  left: 2px !important;
}
