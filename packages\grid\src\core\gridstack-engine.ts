/**
 * gridstack-engine.ts 10.0.1
 * Copyright (c) 2021-2022 <PERSON> - see GridStack root license
 */

import { Utils } from './utils';
import {
  GridStackNode,
  ColumnOptions,
  GridStackPosition,
  GridStackMoveOpts,
  SaveFcn,
  CompactOptions,
} from './types';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';

/** callback to update the DOM attributes since this class is generic (no HTML or other info) for items that changed - see _notify() */
type OnChangeCB = (nodes: GridStackNode[]) => void;

/** options used during creation - similar to GridStackOptions */
export interface GridStackEngineOptions {
  column?: number;
  maxRow?: number;
  float?: boolean;
  nodes?: GridStackNode[];
  onChange?: OnChangeCB;
}

/**
 * Defines the GridStack engine that does most no DOM grid manipulation.
 * See GridStack methods and vars for descriptions.
 *
 * NOTE: values should not be modified directly - call the main GridStack API instead
 */
export class GridStackEngine {
  public column: number;
  public maxRow: number;
  public nodes: GridStackNode[];
  public addedNodes: GridStackNode[] = [];
  public removedNodes: GridStackNode[] = [];
  public batchMode: boolean;
  /** @internal callback to update the DOM attributes */
  protected onChange: OnChangeCB;
  /** @internal */
  protected _float: boolean;
  /** @internal */
  protected _prevFloat: boolean;
  /** @internal cached layouts of difference column count so we can restore back (eg 12 -> 1 -> 12) */
  protected _layouts?: GridStackNode[][]; // maps column # to array of values nodes
  /** @internal true while we are resizing widgets during column resize to skip certain parts */
  protected _inColumnResize?: boolean;
  /** @internal true if we have some items locked */
  protected _hasLocked: boolean;
  /** @internal unique global internal _id counter */
  public static _idSeq = 0;

  // custom
  public rowItemsKeys: string[][] = [];
  // 特殊表格组配置：需要保持同行且Y坐标同步的表格组
  public specialTableGroups: string[][] = [];

  public constructor(opts: GridStackEngineOptions = {}) {
    this.column = opts.column || 12;
    this.maxRow = opts.maxRow;
    this._float = opts.float;
    this.nodes = opts.nodes || [];
    this.onChange = opts.onChange;
  }

  public batchUpdate(flag = true, doPack = true): GridStackEngine {
    if (!!this.batchMode === flag) return this;
    this.batchMode = flag;
    if (flag) {
      this._prevFloat = this._float;
      this._float = true; // let things go anywhere for now... will restore and possibly reposition later
      this.cleanNodes();
      this.saveInitial(); // since begin update (which is called multiple times) won't do this
    } else {
      this._float = this._prevFloat;
      delete this._prevFloat;
      if (doPack) this._packNodes();
      this._notify();
    }
    return this;
  }

  // use entire row for hitting area (will use bottom reverse sorted first) if we not actively moving DOWN and didn't already skip
  protected _useEntireRowArea(
    node: GridStackNode,
    nn: GridStackPosition,
  ): boolean {
    return (
      (!this.float || (this.batchMode && !this._prevFloat)) &&
      !this._hasLocked &&
      (!node._moving || node._skipDown || nn.y <= node.y)
    );
  }

  /** @internal fix collision on given 'node', going to given new location 'nn', with optional 'collide' node already found.
   * return true if we moved. */
  protected _fixCollisions(
    node: GridStackNode,
    nn = node,
    collide?: GridStackNode,
    opt: GridStackMoveOpts = {},
  ): boolean {
    this.sortNodes(-1); // from last to first, so recursive collision move items in the right order

    collide = collide || this.collide(node, nn); // REAL area collide for swap and skip if none...
    if (!collide) return false;

    // swap check: if we're actively moving in gravity mode, see if we collide with an object the same size
    if (node._moving && !opt.nested && !this.float) {
      if (this.swap(node, collide)) return true;
    }

    // during while() collisions MAKE SURE to check entire row so larger items don't leap frog small ones (push them all down starting last in grid)
    let area = nn;
    if (this._useEntireRowArea(node, nn)) {
      area = { x: 0, w: this.column, y: nn.y, h: nn.h };
      collide = this.collide(node, area, opt.skip); // force new hit
    }

    let didMove = false;
    let newOpt: GridStackMoveOpts = { nested: true, pack: false };
    while ((collide = collide || this.collide(node, area, opt.skip))) {
      // could collide with more than 1 item... so repeat for each
      let moved: boolean;
      // if colliding with a locked item OR moving down with top gravity (and collide could move up) -> skip past the collide,
      // but remember that skip down so we only do this once (and push others otherwise).
      if (
        collide.locked ||
        (node._moving &&
          !node._skipDown &&
          nn.y > node.y &&
          !this.float &&
          // can take space we had, or before where we're going
          (!this.collide(collide, { ...collide, y: node.y }, node) ||
            !this.collide(collide, { ...collide, y: nn.y - collide.h }, node)))
      ) {
        node._skipDown = node._skipDown || nn.y > node.y;
        moved = this.moveNode(node, {
          ...nn,
          y: collide.y + collide.h,
          ...newOpt,
        });
        if (collide.locked && moved) {
          Utils.copyPos(nn, node); // moving after lock become our new desired location
        } else if (!collide.locked && moved && opt.pack) {
          // we moved after and will pack: do it now and keep the original drop location, but past the old collide to see what else we might push way
          this._packNodes();
          nn.y = collide.y + collide.h;
          Utils.copyPos(node, nn);
        }
        didMove = didMove || moved;
      } else {
        // move collide down *after* where we will be, ignoring where we are now (don't collide with us)
        moved = this.moveNode(collide, {
          ...collide,
          y: nn.y + nn.h,
          skip: node,
          ...newOpt,
        });
      }
      if (!moved) {
        return didMove;
      } // break inf loop if we couldn't move after all (ex: maxRow, fixed)
      collide = undefined;
    }
    return didMove;
  }

  /** return the nodes that intercept the given node. Optionally a different area can be used, as well as a second node to skip */
  public collide(
    skip: GridStackNode,
    area = skip,
    skip2?: GridStackNode,
  ): GridStackNode | undefined {
    const skipId = skip._id;
    const skip2Id = skip2?._id;
    return this.nodes.find(
      (n) =>
        n._id !== skipId && n._id !== skip2Id && Utils.isIntercepted(n, area),
    );
  }
  public collideAll(
    skip: GridStackNode,
    area = skip,
    skip2?: GridStackNode,
  ): GridStackNode[] {
    const skipId = skip._id;
    const skip2Id = skip2?._id;
    return this.nodes.filter(
      (n) =>
        n._id !== skipId && n._id !== skip2Id && Utils.isIntercepted(n, area),
    );
  }

  // row empty
  public rowEmpty(newY: number): boolean {
    return this.nodes.find((n) => n.y === newY) === undefined;
  }

  /** does a pixel coverage collision based on where we started, returning the node that has the most coverage that is >50% mid line */
  protected directionCollideCoverage(
    node: GridStackNode,
    o: GridStackMoveOpts,
    collides: GridStackNode[],
  ): GridStackNode | undefined {
    if (!o.rect || !node._rect) return;
    let r0 = node._rect; // where started
    let r = { ...o.rect }; // where we are

    // update dragged rect to show where it's coming from (above or below, etc...)
    if (r.y > r0.y) {
      r.h += r.y - r0.y;
      r.y = r0.y;
    } else {
      r.h += r0.y - r.y;
    }
    if (r.x > r0.x) {
      r.w += r.x - r0.x;
      r.x = r0.x;
    } else {
      r.w += r0.x - r.x;
    }

    let collide: GridStackNode;
    let overMax = 0.5; // need >50%
    collides.forEach((n) => {
      if (n.locked || !n._rect) return;
      let r2 = n._rect; // overlapping target
      let yOver = Number.MAX_VALUE,
        xOver = Number.MAX_VALUE;
      // depending on which side we started from, compute the overlap % of coverage
      // (ex: from above/below we only compute the max horizontal line coverage)
      if (r0.y < r2.y) {
        // from above
        yOver = (r.y + r.h - r2.y) / r2.h;
      } else if (r0.y + r0.h > r2.y + r2.h) {
        // from below
        yOver = (r2.y + r2.h - r.y) / r2.h;
      }
      if (r0.x < r2.x) {
        // from the left
        xOver = (r.x + r.w - r2.x) / r2.w;
      } else if (r0.x + r0.w > r2.x + r2.w) {
        // from the right
        xOver = (r2.x + r2.w - r.x) / r2.w;
      }
      let over = Math.min(xOver, yOver);
      if (over > overMax) {
        overMax = over;
        collide = n;
      }
    });
    o.collide = collide; // save it so we don't have to find it again
    return collide;
  }

  /** does a pixel coverage returning the node that has the most coverage by area */
  /*
  protected collideCoverage(r: GridStackPosition, collides: GridStackNode[]): {collide: GridStackNode, over: number} {
    let collide: GridStackNode;
    let overMax = 0;
    collides.forEach(n => {
      if (n.locked || !n._rect) return;
      let over = Utils.areaIntercept(r, n._rect);
      if (over > overMax) {
        overMax = over;
        collide = n;
      }
    });
    return {collide, over: overMax};
  }
  */

  /** called to cache the nodes pixel rectangles used for collision detection during drag */
  public cacheRects(
    w: number,
    h: number,
    top: number,
    right: number,
    bottom: number,
    left: number,
  ): GridStackEngine {
    this.nodes.forEach(
      (n) =>
        (n._rect = {
          y: n.y * h + top,
          x: n.x * w + left,
          w: n.w * w - left - right,
          h: n.h * h - top - bottom,
        }),
    );
    return this;
  }

  /** called to possibly swap between 2 nodes (same size or column, not locked, touching), returning true if successful */
  public swap(a: GridStackNode, b: GridStackNode): boolean | undefined {
    if (!b || b.locked || !a || a.locked) return false;

    function _doSwap(): true {
      // assumes a is before b IFF they have different height (put after rather than exact swap)
      let x = b.x,
        y = b.y;
      b.x = a.x;
      b.y = a.y; // b -> a position
      if (a.h != b.h) {
        a.x = x;
        a.y = b.y + b.h; // a -> goes after b
      } else if (a.w != b.w) {
        a.x = b.x + b.w;
        a.y = y; // a -> goes after b
      } else {
        a.x = x;
        a.y = y; // a -> old b position
      }
      a._dirty = b._dirty = true;
      return true;
    }
    let touching: boolean; // remember if we called it (vs undefined)

    // same size and same row or column, and touching
    if (
      a.w === b.w &&
      a.h === b.h &&
      (a.x === b.x || a.y === b.y) &&
      (touching = Utils.isTouching(a, b))
    )
      return _doSwap();
    if (touching === false) return; // IFF ran test and fail, bail out

    // check for taking same columns (but different height) and touching
    if (
      a.w === b.w &&
      a.x === b.x &&
      (touching || (touching = Utils.isTouching(a, b)))
    ) {
      if (b.y < a.y) {
        let t = a;
        a = b;
        b = t;
      } // swap a <-> b vars so a is first
      return _doSwap();
    }
    if (touching === false) return;

    // check if taking same row (but different width) and touching
    if (
      a.h === b.h &&
      a.y === b.y &&
      (touching || (touching = Utils.isTouching(a, b)))
    ) {
      if (b.x < a.x) {
        let t = a;
        a = b;
        b = t;
      } // swap a <-> b vars so a is first
      return _doSwap();
    }
    return false;
  }

  public isAreaEmpty(x: number, y: number, w: number, h: number): boolean {
    let nn: GridStackNode = { x: x || 0, y: y || 0, w: w || 1, h: h || 1 };
    return !this.collide(nn);
  }

  /** re-layout grid items to reclaim any empty space - optionally keeping the sort order exactly the same ('list' mode) vs truly finding an empty spaces */
  public compact(
    layout: CompactOptions = 'compact',
    doSort = true,
  ): GridStackEngine {
    if (this.nodes.length === 0) return this;
    if (doSort) this.sortNodes();
    const wasBatch = this.batchMode;
    if (!wasBatch) this.batchUpdate();
    const wasColumnResize = this._inColumnResize;
    if (!wasColumnResize) this._inColumnResize = true; // faster addNode()
    let copyNodes = this.nodes;
    this.nodes = []; // pretend we have no nodes to conflict layout to start with...
    copyNodes.forEach((n, index, list) => {
      let after: GridStackNode;
      if (!n.locked) {
        n.autoPosition = true;
        if (layout === 'list' && index) after = list[index - 1];
      }
      this.addNode(n, false, after); // 'false' for add event trigger
    });
    if (!wasColumnResize) delete this._inColumnResize;
    if (!wasBatch) this.batchUpdate(false);
    return this;
  }

  /** enable/disable floating widgets (default: `false`) See [example](http://gridstackjs.com/demo/float.html) */
  public set float(val: boolean) {
    if (this._float === val) return;
    this._float = val || false;
    if (!val) {
      this._packNodes()._notify();
    }
  }

  /** float getter method */
  public get float(): boolean {
    return this._float || false;
  }

  /** sort the nodes array from first to last, or reverse. Called during collision/placement to force an order */
  public sortNodes(dir: 1 | -1 = 1, column = this.column): GridStackEngine {
    this.nodes = Utils.sort(this.nodes, dir, column);
    return this;
  }

  /** @internal called to top gravity pack the items back OR revert back to original Y positions when floating */
  protected _packNodes(): GridStackEngine {
    if (this.batchMode) {
      return this;
    }
    this.sortNodes(); // first to last

    if (this.float) {
      // restore original Y pos
      this.nodes.forEach((n) => {
        if (n._updating || n._orig === undefined || n.y === n._orig.y) return;
        let newY = n.y;
        while (newY > n._orig.y) {
          --newY;
          let collide = this.collide(n, { x: n.x, y: newY, w: n.w, h: n.h });
          if (!collide) {
            n._dirty = true;
            n.y = newY;
          }
        }
      });
    } else {
      // top gravity pack
      this.nodes.forEach((n, i) => {
        if (n.locked) return;
        while (n.y > 0) {
          // custom moving
          // 当这个是正在移动的 只要没有collide 那就移动到现在这个点位上
          if (n._moving) {
            console.log('moving element', n);
            let collide = this.collide(n, { x: n.x, y: n.y, w: n.w, h: n.h });
            let canBeMoved = i === 0 || !collide;
            if (!canBeMoved && collide) {
              let collideNode = collide.el.gridstackNode; // find the source node the clone collided with at 50%
              // 先还原 再交换
              Utils.copyPos(n, n._orig);
              this.swap(n, collideNode);
            }
            n._dirty = true;
            break;
          }

          let newY = i === 0 ? 0 : n.y - 1;
          // origin
          let canBeMoved =
            i === 0 || !this.collide(n, { x: n.x, y: newY, w: n.w, h: n.h });

          // 特殊处理：检查是否为特殊表格组成员
          const specialGroupNodes = this.getSpecialTableGroup(n?.id);
          const isSpecialTable = specialGroupNodes !== null;
          if (!canBeMoved && !isSpecialTable) break;

          // custom start 特殊表格组Y坐标和高度同步处理
          // 首先判断自身的display 是否是 none
          // 如果是特殊表格组成员，则进行Y坐标和高度同步
          if (specialGroupNodes !== null && specialGroupNodes.length > 0) {
            // 1. Y坐标同步：计算目标Y坐标（取最小值） Y至始至终 同步
            const allYValues = [
              n.y,
              ...specialGroupNodes.map((node) => node.y),
            ];
            const targetY = Math.min(...allYValues);

            // 2. 智能高度同步：考虑每个表格的真实期望高度
            // 检查同组节点的显隐状态，如果同组节点隐藏了，则只使用当前显示节点的自身高度
            const visiblePeers = specialGroupNodes.filter((peer) =>
              this.isTableVisible(peer.id),
            );

            let targetH: number;

            if (visiblePeers.length === 0) {
              // 只有当前节点可见，使用当前节点自身的真实高度
              targetH = (n as any)._desiredH ?? n.h;
            } else {
              // 多个节点可见，进行智能高度同步，自身若不可见则自身高度不进allDesiredHeights
              const currentDesiredH = (n as any)._desiredH ?? n.h;
              const allDesiredHeights =
                n.el?.style?.display !== 'none' ? [currentDesiredH] : [];

              visiblePeers.forEach((peer) => {
                const peerDesiredH = (peer as any)._desiredH ?? peer.h;
                let effectiveH = peerDesiredH;
                // 应用节点的min/max约束
                if (peer.minH) effectiveH = Math.max(effectiveH, peer.minH);
                if (peer.maxH) effectiveH = Math.min(effectiveH, peer.maxH);
                allDesiredHeights.push(effectiveH);
              });

              // 目标高度为所有可见节点期望高度的最大值
              targetH = Math.max(...allDesiredHeights);
            }

            console.log(
              '特殊表格组Y坐标和智能高度同步 (_packNodes)',
              'node:',
              n,
              'display:',
              n.el?.style?.display,
              'visiblePeers:',
              visiblePeers.map((p) => ({
                id: p.id,
                y: p.y,
                h: p.h,
                desiredH: (p as any)._desiredH,
              })),
              'targetY:',
              targetY,
              'targetH:',
              targetH,
            );

            // 同步当前节点的Y坐标
            if (n.y !== targetY) {
              n.y = targetY;
              n._dirty = true;
            }

            // 同步当前节点的高度（应用约束）
            let constrainedH = targetH;
            if (n.minH) constrainedH = Math.max(constrainedH, n.minH);
            if (n.maxH) constrainedH = Math.min(constrainedH, n.maxH);
            if (n.h !== constrainedH) {
              n.h = constrainedH;
              n._dirty = true;
            }

            // y 至始至终 同步
            specialGroupNodes.forEach((peer) => {
              if (peer.y !== targetY) {
                peer.y = targetY;
                peer._dirty = true;
              }
            });

            // h 可见的同组节点，同步；并且前提是，自己处于可见状态
            if (n.el?.style?.display !== 'none') {
              visiblePeers.forEach((peer) => {
                let peerConstrainedH = targetH;
                if (peer.minH)
                  peerConstrainedH = Math.max(peerConstrainedH, peer.minH);
                if (peer.maxH)
                  peerConstrainedH = Math.min(peerConstrainedH, peer.maxH);

                if (peer.h !== peerConstrainedH) {
                  peer.h = peerConstrainedH;
                  peer._dirty = true;
                }
              });
            }
          }
          // custom end 特殊表格组Y坐标和高度同步处理

          // canBeMoved 表示当前格子的上方存在一个能放得下的空地方 但是按照布局来看不能放上面 所以做一个判定 newY所在的行有widget的时候不能往上顶
          let sameLineSameY = true;
          let newYLineIsEmpty = this.rowEmpty(newY);
          // 寻找和他同行的 item 看一下原来到底是哪一行
          let sameLineKeys = Utils.getSameLineItemKeys(
            this.rowItemsKeys,
            n?.id,
          );

          // 表示当前行有除他自己以外的多个
          if (sameLineKeys?.length !== 0) {
            // 同时这个item不能是第一个，如果是第一个，不知道第一个头顶上是不是一个空行，必须让第一个往上试顶一下
            if (n.x !== 0) {
              let sameLineItemsY = this.nodes
                .filter((item) => sameLineKeys?.includes(item?.id))
                ?.map((item) => item?.y ?? 65535);
              let minSameLineItemY = Math.min(...sameLineItemsY);

              // console.log('packNodes', minSameLineItemY, sameLineKeys, n);

              if (minSameLineItemY !== newY) {
                newY = minSameLineItemY;
                n._dirty = true;
                n.y = newY;
                break;
              } else {
                // 表示 newY 和 同行的其他元素 一样
                // 但是 newY 和 现在 的不一样
                if (newY !== n?.y) {
                  // 当前行还是没有same的Y 需要修改Y
                  sameLineSameY = false;
                }
              }
            }
          }
          if (!newYLineIsEmpty && sameLineSameY) break;
          // custom end

          // Note: must be dirty (from last position) for GridStack::OnChange CB to update positions
          // and move items back. The user 'change' CB should detect changes from the original
          // starting position instead.
          n._dirty = n.y !== newY;
          n.y = newY;
        }
      });
    }
    return this;
  }

  /**
   * given a random node, makes sure it's coordinates/values are valid in the current grid
   * @param node to adjust
   * @param resizing if out of bound, resize down or move into the grid to fit ?
   */
  public prepareNode(node: GridStackNode, resizing?: boolean): GridStackNode {
    node._id = node._id ?? GridStackEngine._idSeq++;

    // if we're missing position, have the grid position us automatically (before we set them to 0,0)
    if (
      node.x === undefined ||
      node.y === undefined ||
      node.x === null ||
      node.y === null
    ) {
      node.autoPosition = true;
    }

    // assign defaults for missing required fields
    let defaults: GridStackNode = { x: 0, y: 0, w: 1, h: 1 };
    Utils.defaults(node, defaults);

    if (!node.autoPosition) {
      delete node.autoPosition;
    }
    if (!node.noResize) {
      delete node.noResize;
    }
    if (!node.noMove) {
      delete node.noMove;
    }
    Utils.sanitizeMinMax(node);

    // check for NaN (in case messed up strings were passed. can't do parseInt() || defaults.x above as 0 is valid #)
    if (typeof node.x == 'string') {
      node.x = Number(node.x);
    }
    if (typeof node.y == 'string') {
      node.y = Number(node.y);
    }
    if (typeof node.w == 'string') {
      node.w = Number(node.w);
    }
    if (typeof node.h == 'string') {
      node.h = Number(node.h);
    }
    if (isNaN(node.x)) {
      node.x = defaults.x;
      node.autoPosition = true;
    }
    if (isNaN(node.y)) {
      node.y = defaults.y;
      node.autoPosition = true;
    }
    if (isNaN(node.w)) {
      node.w = defaults.w;
    }
    if (isNaN(node.h)) {
      node.h = defaults.h;
    }

    this.nodeBoundFix(node, resizing);
    return node;
  }

  /** part2 of preparing a node to fit inside our grid - checks for x,y,w from grid dimensions */
  public nodeBoundFix(
    node: GridStackNode,
    resizing?: boolean,
  ): GridStackEngine {
    let before = node._orig || Utils.copyPos({}, node);

    if (node.maxW) {
      node.w = Math.min(node.w, node.maxW);
    }
    if (node.maxH) {
      node.h = Math.min(node.h, node.maxH);
    }
    if (node.minW && node.minW <= this.column) {
      node.w = Math.max(node.w, node.minW);
    }
    if (node.minH) {
      node.h = Math.max(node.h, node.minH);
    }

    // if user loaded a larger than allowed widget for current # of columns,
    // remember it's position & width so we can restore back (1 -> 12 column) #1655 #1985
    // IFF we're not in the middle of column resizing!
    const saveOrig = (node.x || 0) + (node.w || 1) > this.column;
    if (
      saveOrig &&
      this.column < 12 &&
      !this._inColumnResize &&
      node._id &&
      this.findCacheLayout(node, 12) === -1
    ) {
      let copy = { ...node }; // need _id + positions
      if (copy.autoPosition || copy.x === undefined) {
        delete copy.x;
        delete copy.y;
      } else copy.x = Math.min(11, copy.x);
      copy.w = Math.min(12, copy.w || 1);
      this.cacheOneLayout(copy, 12);
    }

    if (node.w > this.column) {
      node.w = this.column;
    } else if (node.w < 1) {
      node.w = 1;
    }

    if (this.maxRow && node.h > this.maxRow) {
      node.h = this.maxRow;
    } else if (node.h < 1) {
      node.h = 1;
    }

    if (node.x < 0) {
      node.x = 0;
    }
    if (node.y < 0) {
      node.y = 0;
    }

    if (node.x + node.w > this.column) {
      if (resizing) {
        node.w = this.column - node.x;
      } else {
        node.x = this.column - node.w;
      }
    }
    if (this.maxRow && node.y + node.h > this.maxRow) {
      if (resizing) {
        node.h = this.maxRow - node.y;
      } else {
        node.y = this.maxRow - node.h;
      }
    }

    if (!Utils.samePos(node, before)) {
      node._dirty = true;
    }

    return this;
  }

  /** returns a list of modified nodes from their original values */
  public getDirtyNodes(verify?: boolean): GridStackNode[] {
    // compare original x,y,w,h instead as _dirty can be a temporary state
    if (verify) {
      return this.nodes.filter((n) => n._dirty && !Utils.samePos(n, n._orig));
    }
    return this.nodes.filter((n) => n._dirty);
  }

  /** @internal call this to call onChange callback with dirty nodes so DOM can be updated */
  protected _notify(removedNodes?: GridStackNode[]): GridStackEngine {
    if (this.batchMode || !this.onChange) return this;
    let dirtyNodes = (removedNodes || []).concat(this.getDirtyNodes());
    this.onChange(dirtyNodes);
    // custom
    this.rowItemWithKeyCalculate();
    return this;
  }

  /** @internal remove dirty and last tried info */
  public cleanNodes(): GridStackEngine {
    if (this.batchMode) return this;
    this.nodes.forEach((n) => {
      delete n._dirty;
      delete n._lastTried;
    });
    return this;
  }

  /** @internal called to save initial position/size to track real dirty state.
   * Note: should be called right after we call change event (so next API is can detect changes)
   * as well as right before we start move/resize/enter (so we can restore items to prev values) */
  public saveInitial(): GridStackEngine {
    this.nodes.forEach((n) => {
      n._orig = Utils.copyPos({}, n);
      delete n._dirty;
    });
    this._hasLocked = this.nodes.some((n) => n.locked);
    return this;
  }

  /** @internal restore all the nodes back to initial values (called when we leave) */
  public restoreInitial(): GridStackEngine {
    this.nodes.forEach((n) => {
      if (Utils.samePos(n, n._orig)) return;
      Utils.copyPos(n, n._orig);
      n._dirty = true;
    });
    this._notify();
    return this;
  }

  /** find the first available empty spot for the given node width/height, updating the x,y attributes. return true if found.
   * optionally you can pass your own existing node list and column count, otherwise defaults to that engine data.
   * Optionally pass a widget to start search AFTER, meaning the order will remain the same but possibly have empty slots we skipped
   */
  public findEmptyPosition(
    node: GridStackNode,
    nodeList = this.nodes,
    column = this.column,
    after?: GridStackNode,
  ): boolean {
    let start = after ? after.y * column + (after.x + after.w) : 0;
    let found = false;
    for (let i = start; !found; ++i) {
      let x = i % column;
      let y = Math.floor(i / column);
      if (x + node.w > column) {
        continue;
      }
      let box = { x, y, w: node.w, h: node.h };
      if (!nodeList.find((n) => Utils.isIntercepted(box, n))) {
        if (node.x !== x || node.y !== y) node._dirty = true;
        node.x = x;
        node.y = y;
        delete node.autoPosition;
        found = true;
      }
    }
    return found;
  }

  /** call to add the given node to our list, fixing collision and re-packing */
  public addNode(
    node: GridStackNode,
    triggerAddEvent = false,
    after?: GridStackNode,
  ): GridStackNode {
    let dup = this.nodes.find((n) => n._id === node._id);
    if (dup) return dup; // prevent inserting twice! return it instead.

    // skip prepareNode if we're in middle of column resize (not new) but do check for bounds!
    this._inColumnResize ? this.nodeBoundFix(node) : this.prepareNode(node);
    delete node._temporaryRemoved;
    delete node._removeDOM;

    let skipCollision: boolean;
    if (
      node.autoPosition &&
      this.findEmptyPosition(node, this.nodes, this.column, after)
    ) {
      delete node.autoPosition; // found our slot
      skipCollision = true;
    }

    this.nodes.push(node);
    if (triggerAddEvent) {
      this.addedNodes.push(node);
    }

    if (!skipCollision) this._fixCollisions(node);
    if (!this.batchMode) {
      this._packNodes()._notify();
    }
    return node;
  }

  public removeNode(
    node: GridStackNode,
    removeDOM = true,
    triggerEvent = false,
  ): GridStackEngine {
    if (!this.nodes.find((n) => n._id === node._id)) {
      // TEST console.log(`Error: GridStackEngine.removeNode() node._id=${node._id} not found!`)
      return this;
    }
    if (triggerEvent) {
      // we wait until final drop to manually track removed items (rather than during drag)
      this.removedNodes.push(node);
    }
    if (removeDOM) node._removeDOM = true; // let CB remove actual HTML (used to set _id to null, but then we loose layout info)
    // don't use 'faster' .splice(findIndex(),1) in case node isn't in our list, or in multiple times.
    this.nodes = this.nodes.filter((n) => n._id !== node._id);
    if (!node._isAboutToRemove) this._packNodes(); // if dragged out, no need to relayout as already done...
    this._notify([node]);
    return this;
  }

  public removeAll(removeDOM = true): GridStackEngine {
    delete this._layouts;
    if (!this.nodes.length) return this;
    removeDOM && this.nodes.forEach((n) => (n._removeDOM = true)); // let CB remove actual HTML (used to set _id to null, but then we loose layout info)
    this.removedNodes = this.nodes;
    this.nodes = [];
    return this._notify(this.removedNodes);
  }

  /** checks if item can be moved (layout constrain) vs moveNode(), returning true if was able to move.
   * In more complicated cases (maxRow) it will attempt at moving the item and fixing
   * others in a clone first, then apply those changes if still within specs. */
  public moveNodeCheck(node: GridStackNode, o: GridStackMoveOpts): boolean {
    // if (node.locked) return false;
    if (!this.changedPosConstrain(node, o)) return false;
    o.pack = true;

    // simpler case: move item directly...
    if (!this.maxRow) {
      return this.moveNode(node, o);
    }

    // complex case: create a clone with NO maxRow (will check for out of bounds at the end)
    let clonedNode: GridStackNode;
    let clone = new GridStackEngine({
      column: this.column,
      float: this.float,
      nodes: this.nodes.map((n) => {
        if (n._id === node._id) {
          clonedNode = { ...n };
          return clonedNode;
        }
        return { ...n };
      }),
    });
    if (!clonedNode) return false;

    // check if we're covering 50% collision and could move, while still being under maxRow or at least not making it worse
    // (case where widget was somehow added past our max #2449)
    let canMove =
      clone.moveNode(clonedNode, o) &&
      clone.getRow() <= Math.max(this.getRow(), this.maxRow);
    // else check if we can force a swap (float=true, or different shapes) on non-resize
    if (!canMove && !o.resizing && o.collide) {
      let collide = o.collide.el.gridstackNode; // find the source node the clone collided with at 50%
      if (this.swap(node, collide)) {
        // swaps and mark dirty
        this._notify();
        return true;
      }
    }
    if (!canMove) return false;

    // if clone was able to move, copy those mods over to us now instead of caller trying to do this all over!
    // Note: we can't use the list directly as elements and other parts point to actual node, so copy content
    clone.nodes
      .filter((n) => n._dirty)
      .forEach((c) => {
        let n = this.nodes.find((a) => a._id === c._id);
        if (!n) return;
        Utils.copyPos(n, c);
        n._dirty = true;
      });
    this._notify();
    return true;
  }

  /** return true if can fit in grid height constrain only (always true if no maxRow) */
  public willItFit(node: GridStackNode): boolean {
    delete node._willFitPos;
    if (!this.maxRow) return true;
    // create a clone with NO maxRow and check if still within size
    let clone = new GridStackEngine({
      column: this.column,
      float: this.float,
      nodes: this.nodes.map((n) => {
        return { ...n };
      }),
    });
    let n = { ...node }; // clone node so we don't mod any settings on it but have full autoPosition and min/max as well! #1687
    this.cleanupNode(n);
    delete n.el;
    delete n._id;
    delete n.content;
    delete n.grid;
    clone.addNode(n);
    if (clone.getRow() <= this.maxRow) {
      node._willFitPos = Utils.copyPos({}, n);
      return true;
    }
    return false;
  }

  /** true if x,y or w,h are different after clamping to min/max */
  public changedPosConstrain(
    node: GridStackNode,
    p: GridStackPosition,
  ): boolean {
    // first make sure w,h are set for caller
    p.w = p.w || node.w;
    p.h = p.h || node.h;
    if (node.x !== p.x || node.y !== p.y) return true;
    // check constrained w,h
    if (node.maxW) {
      p.w = Math.min(p.w, node.maxW);
    }
    if (node.maxH) {
      p.h = Math.min(p.h, node.maxH);
    }
    if (node.minW) {
      p.w = Math.max(p.w, node.minW);
    }
    if (node.minH) {
      p.h = Math.max(p.h, node.minH);
    }
    return node.w !== p.w || node.h !== p.h;
  }

  /** return true if the passed in node was actually moved (checks for no-op and locked) */
  public moveNode(node: GridStackNode, o: GridStackMoveOpts): boolean {
    if (!node || /*node.locked ||*/ !o) return false;
    let wasUndefinedPack: boolean;
    if (o.pack === undefined && !this.batchMode) {
      wasUndefinedPack = o.pack = true;
    }

    // constrain the passed in values and check if we're still changing our node
    if (typeof o.x !== 'number') {
      o.x = node.x;
    }
    if (typeof o.y !== 'number') {
      o.y = node.y;
    }
    if (typeof o.w !== 'number') {
      o.w = node.w;
    }
    if (typeof o.h !== 'number') {
      o.h = node.h;
    }
    let resizing = node.w !== o.w || node.h !== o.h;
    let nn: GridStackNode = Utils.copyPos({}, node, true); // get min/max out first, then opt positions next
    Utils.copyPos(nn, o);
    this.nodeBoundFix(nn, resizing);
    Utils.copyPos(o, nn);

    // custom start 记录期望高度（用于特殊表格组智能高度同步）
    if (resizing && this.getSpecialTableGroup(node.id)) {
      // 记录当前节点在约束后的期望高度，供_packNodes中的智能同步使用
      (node as any)._desiredH = nn.h;
    }
    // custom end

    if (!o.forceCollide && Utils.samePos(node, o)) return false;
    let prevPos: GridStackPosition = Utils.copyPos({}, node);

    // check if we will need to fix collision at our new location
    let collides = this.collideAll(node, nn, o.skip);
    let needToMove = true;
    if (collides.length) {
      let activeDrag = node._moving && !o.nested;
      // check to make sure we actually collided over 50% surface area while dragging
      let collide = activeDrag
        ? this.directionCollideCoverage(node, o, collides)
        : collides[0];
      // if we're enabling creation of sub-grids on the fly, see if we're covering 80% of either one, if we didn't already do that
      if (
        activeDrag &&
        collide &&
        node.grid?.opts?.subGridDynamic &&
        !node.grid._isTemp
      ) {
        let over = Utils.areaIntercept(o.rect, collide._rect);
        let a1 = Utils.area(o.rect);
        let a2 = Utils.area(collide._rect);
        let perc = over / (a1 < a2 ? a1 : a2);
        if (perc > 0.8) {
          collide.grid.makeSubGrid(collide.el, undefined, node);
          collide = undefined;
        }
      }

      if (collide) {
        needToMove = !this._fixCollisions(node, nn, collide, o); // check if already moved...
      } else {
        needToMove = false; // we didn't cover >50% for a move, skip...
        if (wasUndefinedPack) delete o.pack;
      }
    }

    // now move (to the original ask vs the collision version which might differ) and repack things
    if (needToMove) {
      node._dirty = true;
      Utils.copyPos(node, nn);
    }
    if (o.pack) {
      if (node._moving && !o.nested) {
        this.rowItemWithKeyCalculate();
      }
      this._packNodes()._notify();
    }
    return !Utils.samePos(node, prevPos); // pack might have moved things back
  }

  public getRow(): number {
    return this.nodes.reduce((row, n) => Math.max(row, n.y + n.h), 0);
  }

  public beginUpdate(node: GridStackNode): GridStackEngine {
    if (!node._updating) {
      node._updating = true;
      delete node._skipDown;
      if (!this.batchMode) this.saveInitial();
    }
    return this;
  }

  public endUpdate(): GridStackEngine {
    let n = this.nodes.find((n) => n._updating);
    if (n) {
      delete n._updating;
      delete n._skipDown;
    }
    return this;
  }

  /** saves a copy of the largest column layout (eg 12 even when rendering oneColumnMode) so we don't loose orig layout,
   * returning a list of widgets for serialization */
  public save(saveElement = true, saveCB?: SaveFcn): GridStackNode[] {
    // use the highest layout for any saved info so we can have full detail on reload #1849
    let len = this._layouts?.length;
    let layout = len && this.column !== len - 1 ? this._layouts[len - 1] : null;
    let list: GridStackNode[] = [];
    this.sortNodes();
    this.nodes.forEach((n) => {
      let wl = layout?.find((l) => l._id === n._id);
      // use layout info fields instead if set
      let w: GridStackNode = { ...n, ...(wl || {}) };
      Utils.removeInternalForSave(w, !saveElement);
      if (saveCB) saveCB(n, w);
      list.push(w);
    });
    return list;
  }

  /** @internal called whenever a node is added or moved - updates the cached layouts */
  public layoutsNodesChange(nodes: GridStackNode[]): GridStackEngine {
    if (!this._layouts || this._inColumnResize) return this;
    // remove smaller layouts - we will re-generate those on the fly... larger ones need to update
    this._layouts.forEach((layout, column) => {
      if (!layout || column === this.column) return this;
      if (column < this.column) {
        this._layouts[column] = undefined;
      } else {
        // we save the original x,y,w (h isn't cached) to see what actually changed to propagate better.
        // NOTE: we don't need to check against out of bound scaling/moving as that will be done when using those cache values. #1785
        let ratio = column / this.column;
        nodes.forEach((node) => {
          if (!node._orig) return; // didn't change (newly added ?)
          let n = layout.find((l) => l._id === node._id);
          if (!n) return; // no cache for new nodes. Will use those values.
          // Y changed, push down same amount
          // TODO: detect doing item 'swaps' will help instead of move (especially in 1 column mode)
          if (n.y >= 0 && node.y !== node._orig.y) {
            n.y += node.y - node._orig.y;
          }
          // X changed, scale from new position
          if (node.x !== node._orig.x) {
            n.x = Math.round(node.x * ratio);
          }
          // width changed, scale from new width
          if (node.w !== node._orig.w) {
            n.w = Math.round(node.w * ratio);
          }
          // ...height always carries over from cache
        });
      }
    });
    return this;
  }

  /**
   * @internal Called to scale the widget width & position up/down based on the column change.
   * Note we store previous layouts (especially original ones) to make it possible to go
   * from say 12 -> 1 -> 12 and get back to where we were.
   *
   * @param prevColumn previous number of columns
   * @param column  new column number
   * @param nodes different sorted list (ex: DOM order) instead of current list
   * @param layout specify the type of re-layout that will happen (position, size, etc...).
   * Note: items will never be outside of the current column boundaries. default (moveScale). Ignored for 1 column
   */
  public columnChanged(
    prevColumn: number,
    column: number,
    nodes: GridStackNode[],
    layout: ColumnOptions = 'moveScale',
  ): GridStackEngine {
    if (!this.nodes.length || !column || prevColumn === column) return this;

    // simpler shortcuts layouts
    const doCompact = layout === 'compact' || layout === 'list';
    if (doCompact) {
      this.sortNodes(1, prevColumn); // sort with original layout once and only once (new column will affect order otherwise)
    }

    // cache the current layout in case they want to go back (like 12 -> 1 -> 12) as it requires original data IFF we're sizing down (see below)
    if (column < prevColumn) this.cacheLayout(this.nodes, prevColumn);
    this.batchUpdate(); // do this EARLY as it will call saveInitial() so we can detect where we started for _dirty and collision
    let newNodes: GridStackNode[] = [];

    // if we're going to 1 column and using DOM order (item passed in) rather than default sorting, then generate that layout
    let domOrder = false;
    if (column === 1 && nodes?.length) {
      domOrder = true;
      let top = 0;
      nodes.forEach((n) => {
        n.x = 0;
        n.w = 1;
        n.y = Math.max(n.y, top);
        top = n.y + n.h;
      });
      newNodes = nodes;
      nodes = [];
    } else {
      nodes = doCompact ? this.nodes : Utils.sort(this.nodes, -1, prevColumn); // current column reverse sorting so we can insert last to front (limit collision)
    }

    // see if we have cached previous layout IFF we are going up in size (restore) otherwise always
    // generate next size down from where we are (looks more natural as you gradually size down).
    if (column > prevColumn && this._layouts) {
      const cacheNodes = this._layouts[column] || [];
      // ...if not, start with the largest layout (if not already there) as down-scaling is more accurate
      // by pretending we came from that larger column by assigning those values as starting point
      let lastIndex = this._layouts.length - 1;
      if (
        !cacheNodes.length &&
        prevColumn !== lastIndex &&
        this._layouts[lastIndex]?.length
      ) {
        prevColumn = lastIndex;
        this._layouts[lastIndex].forEach((cacheNode) => {
          let n = nodes.find((n) => n._id === cacheNode._id);
          if (n) {
            // still current, use cache info positions
            if (!doCompact && !cacheNode.autoPosition) {
              n.x = cacheNode.x ?? n.x;
              n.y = cacheNode.y ?? n.y;
            }
            n.w = cacheNode.w ?? n.w;
            if (cacheNode.x == undefined || cacheNode.y === undefined)
              n.autoPosition = true;
          }
        });
      }

      // if we found cache re-use those nodes that are still current
      cacheNodes.forEach((cacheNode) => {
        let j = nodes.findIndex((n) => n._id === cacheNode._id);
        if (j !== -1) {
          const n = nodes[j];
          // still current, use cache info positions
          if (doCompact) {
            n.w = cacheNode.w; // only w is used, and don't trim the list
            return;
          }
          if (
            cacheNode.autoPosition ||
            isNaN(cacheNode.x) ||
            isNaN(cacheNode.y)
          ) {
            this.findEmptyPosition(cacheNode, newNodes);
          }
          if (!cacheNode.autoPosition) {
            n.x = cacheNode.x ?? n.x;
            n.y = cacheNode.y ?? n.y;
            n.w = cacheNode.w ?? n.w;
            newNodes.push(n);
          }
          nodes.splice(j, 1);
        }
      });
    }

    // much simpler layout that just compacts
    if (doCompact) {
      this.compact(layout, false);
    } else {
      // ...and add any extra non-cached ones
      if (nodes.length) {
        if (typeof layout === 'function') {
          layout(column, prevColumn, newNodes, nodes);
        } else if (!domOrder) {
          // origin
          // let ratio = (doCompact || layout === 'none') ? 1 : column / prevColumn;
          //custom
          let ratio =
            doCompact || layout === 'none' || layout === 'move'
              ? 1
              : column / prevColumn;
          let move = layout === 'move' || layout === 'moveScale';
          let scale = layout === 'scale' || layout === 'moveScale';
          nodes.forEach((node) => {
            // NOTE: x + w could be outside of the grid, but addNode() below will handle that
            node.x =
              column === 1
                ? 0
                : move
                ? Math.round(node.x * ratio)
                : Math.min(node.x, column - 1);
            node.w =
              column === 1 || prevColumn === 1
                ? 1
                : scale
                ? Math.round(node.w * ratio) || 1
                : Math.min(node.w, column);
            newNodes.push(node);
          });
          nodes = [];
        }
      } else {
        // custom 重置 layout用于 回到原来的版本
        setTimeout(() => {
          Emitter.emit(EventConstant.TABLE_LAYOUT_CHANGE_RESET_SEPARATOR);
          Emitter.emit(
            EventConstant.DMR_TABLE_LAYOUT_CHANGE_SECTION_RESET_SEPARATOR,
          );
        }, 200);
      }

      // finally re-layout them in reverse order (to get correct placement)
      if (!domOrder) newNodes = Utils.sort(newNodes, -1, column);
      this._inColumnResize = true; // prevent cache update
      this.nodes = []; // pretend we have no nodes to start with (add() will use same structures) to simplify layout
      newNodes.forEach((node) => {
        this.addNode(node, false); // 'false' for add event trigger
        delete node._orig; // make sure the commit doesn't try to restore things back to original
      });

      // custom
      this.rowItemWithKeyCalculate();
    }

    this.nodes.forEach((n) => delete n._orig); // clear _orig before batch=false so it doesn't handle float=true restore
    this.batchUpdate(false, !doCompact);
    delete this._inColumnResize;
    return this;
  }

  /**
   * call to cache the given layout internally to the given location so we can restore back when column changes size
   * @param nodes list of nodes
   * @param column corresponding column index to save it under
   * @param clear if true, will force other caches to be removed (default false)
   */
  public cacheLayout(
    nodes: GridStackNode[],
    column: number,
    clear = false,
  ): GridStackEngine {
    let copy: GridStackNode[] = [];
    nodes.forEach((n, i) => {
      // make sure we have an id in case this is new layout, else re-use id already set
      if (n._id === undefined) {
        const existing = n.id
          ? this.nodes.find((n2) => n2.id === n.id)
          : undefined; // find existing node using users id
        n._id = existing?._id ?? GridStackEngine._idSeq++;
      }
      copy[i] = { x: n.x, y: n.y, w: n.w, _id: n._id }; // only thing we change is x,y,w and id to find it back
    });
    this._layouts = clear ? [] : this._layouts || []; // use array to find larger quick
    this._layouts[column] = copy;
    return this;
  }

  /**
   * call to cache the given node layout internally to the given location so we can restore back when column changes size
   * @param node single node to cache
   * @param column corresponding column index to save it under
   */
  public cacheOneLayout(n: GridStackNode, column: number): GridStackEngine {
    n._id = n._id ?? GridStackEngine._idSeq++;
    let l: GridStackNode = { x: n.x, y: n.y, w: n.w, _id: n._id };
    if (n.autoPosition || n.x === undefined) {
      delete l.x;
      delete l.y;
      if (n.autoPosition) l.autoPosition = true;
    }
    this._layouts = this._layouts || [];
    this._layouts[column] = this._layouts[column] || [];
    let index = this.findCacheLayout(n, column);
    if (index === -1) this._layouts[column].push(l);
    else this._layouts[column][index] = l;
    return this;
  }

  protected findCacheLayout(
    n: GridStackNode,
    column: number,
  ): number | undefined {
    return this._layouts?.[column]?.findIndex((l) => l._id === n._id) ?? -1;
  }

  public removeNodeFromLayoutCache(n: GridStackNode) {
    if (!this._layouts) {
      return;
    }
    for (let i = 0; i < this._layouts.length; i++) {
      let index = this.findCacheLayout(n, i);
      if (index !== -1) {
        this._layouts[i].splice(index, 1);
      }
    }
  }

  /** called to remove all internal values but the _id */
  public cleanupNode(node: GridStackNode): GridStackEngine {
    for (let prop in node) {
      if (prop[0] === '_' && prop !== '_id') delete node[prop];
    }
    return this;
  }

  // custom start
  public rowItemWithKeyCalculate = () => {
    // 测算当前布局下的每行内有哪些组件
    const rowItemWithKey = {};
    this.nodes?.forEach((item) => {
      if (rowItemWithKey[item?.y]) {
        rowItemWithKey[item?.y] = [...rowItemWithKey[item?.y], item.id];
      } else {
        rowItemWithKey[item?.y] = [item.id];
      }
    });
    this.rowItemsKeys = Object.values(rowItemWithKey);
  };

  /**
   * 设置特殊表格组，这些表格需要保持同行且Y坐标同步
   * @param groups 表格组数组，每个组包含需要同步的表格ID数组
   * @example setSpecialTableGroups([['diagnosisMainTable', 'diagnosisInsurTable'], ['table1', 'table2', 'table3']])
   */
  public setSpecialTableGroups(groups: string[][]): void {
    this.specialTableGroups = groups;
  }

  /**
   * 检查节点是否属于特殊表格组，并返回同组的其他节点
   */
  private getSpecialTableGroup(nodeId: string): GridStackNode[] | null {
    for (const group of this.specialTableGroups) {
      if (group.includes(nodeId)) {
        // 使用现有的同行检测逻辑
        const sameLineKeys = Utils.getSameLineItemKeys(
          this.rowItemsKeys,
          nodeId,
        );

        // 找到同组且同行的其他节点
        const sameRowGroupNodes = this.nodes.filter(
          (n) =>
            group.includes(n.id) &&
            n.id !== nodeId &&
            sameLineKeys.includes(n.id),
        );

        return sameRowGroupNodes.length > 0 ? sameRowGroupNodes : null;
      }
    }
    return null;
  }

  /**
   * 调整特殊表格组的宽度，当同组表格被隐藏时重新分配宽度
   */
  private adjustSpecialTableGroupWidths(
    node: GridStackNode,
    groupNodes: GridStackNode[],
  ): void {
    // 检查DOM中的表格显隐状态
    const isNodeVisible = this.isTableVisible(node.id);
    const visibleGroupNodes = groupNodes.filter((peer) =>
      this.isTableVisible(peer.id),
    );

    // 如果当前节点可见，但同组其他节点被隐藏，则扩展当前节点宽度
    if (isNodeVisible && visibleGroupNodes.length === 0) {
      // 只有当前节点可见，扩展到18
      if (node.w !== 18) {
        node.w = 18;
        node._dirty = true;
        console.log('扩展表格宽度到18:', node.id);
      }
    } else if (isNodeVisible && visibleGroupNodes.length > 0) {
      // 多个节点可见，恢复正常宽度
      if (node.w !== 9) {
        node.w = 9;
        node._dirty = true;
        console.log('恢复表格宽度到9:', node.id);
      }

      // 同步其他可见节点的宽度
      visibleGroupNodes.forEach((peer) => {
        if (peer.w !== 9) {
          peer.w = 9;
          peer._dirty = true;
          console.log('同步表格宽度到9:', peer.id);
        }
      });
    }
  }

  /**
   * 检查表格在DOM中是否可见
   */
  private isTableVisible(nodeId: string): boolean {
    const element = document?.getElementById(nodeId);
    if (!element) return false;

    const style = window.getComputedStyle(element);
    return style.display !== 'none';
  }
  // custom end
}
