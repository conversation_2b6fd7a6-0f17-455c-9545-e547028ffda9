import './index.less';
import 'react-grid-layout/css/styles.css';
import '@/pages/dmr/index.less';
import React, {
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { dynamicComponentsMap } from '@/utils/dynamicComponents';
import {
  Button,
  Dropdown,
  Form,
  MenuProps,
  message,
  Modal,
  notification,
  Popover,
  Spin,
  Upload,
  Radio,
  Tooltip,
} from 'antd';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import InputSuffix from '@uni/grid/src/components/input-suffix';
import WidthProvideRGL from '@/utils/widthProvider';
import ReactResizeDetector, { withResizeDetector } from 'react-resize-detector';
import { Responsive, WidthProvider } from 'react-grid-layout';
import '../../components/grid-layout/utils';
import Dmr, {
  BREAKPOINTS_LABEL,
  defaultFieldValue,
  DmrHeader,
  ROW_HEIGHT,
  Separators,
  topMenuKeys,
} from '@/pages/dmr';
import flattenDeep from 'lodash/flattenDeep';
import { contentData } from '@/pages/dmr/fields/base';
import { v4 as uuidv4 } from 'uuid';
import { useRequest } from 'umi';
import {
  getCardInfoV2,
  getDmrIndexLayoutConfig,
  getDmrSavedMenu,
} from '@/pages/dmr/network/get';
import { RespVO } from '@uni/commons/src/interfaces';
import { generateLayout } from '@/utils/layouts';
import { GridItem } from '@uni/grid/src/common/item';
import RightMenu, { TopMenuItem } from '@/pages/dmr/components/right-menu';
import PropertyContainer, {
  tableLabelMapping,
} from '@/pages/configuration/components/property-container';
import { DmrConfigurationConstants } from '@/pages/configuration/constants';
import cloneDeep from 'lodash/cloneDeep';
import merge from 'lodash/merge';
import {
  saveGlobalDmrIndexLayouts,
  saveGlobalDmrModuleInfo,
} from '@/pages/dmr/network/save';
import MenuEdit from '@/pages/configuration/components/menu-edit';
import {
  contentItemAddProcessor,
  layoutAddOtherProperties,
  layoutItemAddProcessor,
  layoutSaveProcessor,
} from '@/pages/configuration/processor';
import PreCheckRuleEdit from '@/pages/configuration/components/rule-edit';
import {
  exportLayoutData,
  mergeLayoutsAndGridStackItemPosition,
  resizeGridStackItemPosition,
} from '@/pages/configuration/utils/utils';
import { UploadOutlined } from '@ant-design/icons';
import LayoutImport from '@/pages/configuration/components/layout-import';
import OperatorsEdit from '@/pages/configuration/components/operator-edit';
import PreCheckModuleEdit from '@/pages/configuration/components/precheck-module-edit';
import '@/components/grid-layout/gridUtil';
import { processNoBlankLine } from '@/components/grid-layout/gridUtil';
import DmrConfigurationLoading from '@/pages/configuration/components/configuration-loading';
import { headerData } from '@/pages/dmr/fields/header';
import DmrDataPreview from '@/pages/configuration/components/data-preview';
import mergeWith from 'lodash/mergeWith';
import { DmrProcessor } from '@/pages/dmr/processors/processors';
import DmrThemeEdit from '@/pages/configuration/components/theme-edit';
import { isEmptyValues } from '@uni/utils/src/utils';
import { compact, moveElement } from '@/components/grid-layout/utils';
import isEqual from 'lodash/isEqual';
import omit from 'lodash/omit';
import { getLayoutItem } from 'react-grid-layout/build/utils';
import LineNumber from '@/pages/configuration/components/line-number';
import {
  Breakpoint,
  GridItemHTMLElement,
  GridStack,
  GridStackNode,
  GridStackWidget,
  Utils,
} from '@uni/grid/src/core/gridstack';
import {
  onGridColumnChange,
  onGridItemDragStop,
  onGridItemResizeStop,
} from '@uni/grid/src/core/custom-utils';
import { LAYOUT_COLUMNS } from '@uni/grid/src/common';
import { useModel } from '@@/plugin-model/useModel';
import GridItemContext from '@uni/commons/src/grid-context';
import { dmrThemeProcessor } from '@/pages/dmr/processors/others';
import { uniCommonService } from '@uni/services/src';

const externalDmrConfig = (window as any).externalConfig?.['dmr'];

export const dmrConfigProperties = {
  isDraggable: true,
  resizeHandles: ['se'],
  static: false,
  isResizable: true,
};

// form key 需要再放一份  和 name一样

let headerGridInstance: GridStack = undefined;
let contentGridInstance: GridStack = undefined;

const DmrConfiguration = () => {
  const [layouts, setLayouts] = useState({});

  const [headerLayouts, setHeaderLayouts] = useState({});

  const [form] = Form.useForm();

  const [layoutForm] = Form.useForm();

  const [resolutionType, setResolutionType] = useState('lg');

  const [publicMode, setPublicMode] = useState(global['PublicMode'] ?? false);

  const otherOperations = [
    {
      label: '预审核规则',
      key: 'PRE_RULE',
    },
    {
      label: '菜单修改',
      key: 'MENU_EDIT',
    },
    {
      label: '病案操作按钮',
      key: 'DMR_OPERATOR_EDIT',
    },
    {
      label: '预审核模块',
      key: 'DMR_PRE_CHECK_MODULE',
    },
    {
      label: '病案数据预览',
      key: 'DMR_PREVIEW',
    },
    {
      label: '查看配置文档',
      key: 'DOCS',
    },
    {
      label: '',
      key: 'DMR_PUBLIC',
    },
    {
      label: '颜色自定义',
      key: 'THEME_EDIT',
    },
  ];

  const layoutOperations = [
    {
      label: '切换分辨率',
      key: 'RESOLUTION',
      content: (
        <div>
          <Radio.Group
            defaultValue={resolutionType}
            buttonStyle="solid"
            onChange={(e) => {
              setResolutionType(e.target.value);
            }}
          >
            {Object.keys(BREAKPOINTS_LABEL).map((key) => {
              return (
                <Radio.Button value={key}>
                  {BREAKPOINTS_LABEL[key]?.label}
                </Radio.Button>
              );
            })}
          </Radio.Group>
        </div>
      ),
    },
    // {
    //   label: '新增',
    //   key: 'ADD',
    // },
    {
      label: '保存',
      key: 'SAVE',
    },
    {
      label: '重置',
      key: 'RESET',
    },
    {
      label: '初始化',
      key: 'INITIALIZE',
    },
    {
      label: '导入布局',
      key: 'IMPORT',
    },
    {
      label: '导入表头',
      key: 'IMPORT_HEADER',
    },
    {
      label: '导出布局',
      key: 'EXPORT',
    },
  ];

  useEffect(() => {
    getDmrLayout();

    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_IMPORT_COMPLETE,
      (payload) => {
        let importedLayout = payload?.data;
        let type = payload?.type;
        if (type === 'IMPORT') {
          let contentLayouts = generateLayout(
            importedLayout || {},
            false,
            contentData,
            'DmrLayout',
          );
          setLayouts(generateDraggableResizableLayout(contentLayouts));
        }

        if (type === 'IMPORT_HEADER') {
          let headerLayouts = generateLayout(
            importedLayout || {},
            false,
            headerData,
            'DmrHeaderLayout',
          );
          setHeaderLayouts(generateDraggableResizableLayout(headerLayouts));
        }
      },
    );

    return () => {
      Emitter.off(DmrConfigurationConstants.DMR_CONFIGURATION_IMPORT_COMPLETE);

      // make grid instance undefined
      headerGridInstance = undefined;
      contentGridInstance = undefined;
    };
  }, []);

  const getDmrLayout = () => {
    let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
    dmrLayoutsGetReq(userInfo?.CliDepts?.at(0), userInfo?.HospCodes?.at(0));
  };

  const generateDraggableResizableLayout = (layouts: any) => {
    Object.keys(layouts)?.forEach((key) => {
      layouts[key] = layouts[key]?.map((item) => {
        if (item?.maxH) {
          delete item['maxH'];
        }

        return {
          ...item,
          ...dmrConfigProperties,
        };
      });
    });
    console.log('layouts', layouts, JSON.stringify(layouts));

    return layouts;
  };

  //接口start
  const { loading: dmrLayoutsGetLoading, run: dmrLayoutsGetReq } = useRequest(
    (cliDeptsCode, hospCode) => {
      return getDmrIndexLayoutConfig(cliDeptsCode, hospCode, ['DmrTheme']);
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let layouts = generateLayout(
            response?.data?.DmrLayout || {},
            false,
            contentData,
            'DmrLayout',
          );
          let headerLayout = generateLayout(
            response?.data?.DmrHeaderLayout || {},
            false,
            headerData,
            'DmrHeaderLayout',
          );

          setLayouts(generateDraggableResizableLayout(layouts));
          setHeaderLayouts(generateDraggableResizableLayout(headerLayout));

          dmrThemeProcessor(response?.data?.DmrTheme);
        } else {
          setLayouts({});
          setHeaderLayouts({});
        }
      },
    },
  );
  //接口end

  const onOperationClick = (key) => {
    switch (key) {
      case 'ADD':
        Emitter.emit(
          DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_STATUS,
          {
            status: true,
            type: 'ADD',
          },
        );
        break;
      case 'SAVE':
        saveLayout();
        break;
      case 'RESET': {
        Modal.confirm({
          title: `确定重置布局 为数据库中存储的？`,
          content: '',
          onOk: () => {
            getDmrLayout();
          },
        });
        break;
      }
      case 'INITIALIZE':
        Modal.confirm({
          title: `确定初始化布局？`,
          content: '',
          onOk: () => {
            let layouts = generateLayout({}, false, contentData, 'DmrLayout');
            let headerLayouts = generateLayout(
              {},
              false,
              headerData,
              'DmrHeaderLayout',
            );
            setLayouts(generateDraggableResizableLayout(layouts));
            setHeaderLayouts(generateDraggableResizableLayout(headerLayouts));
            form.resetFields();
          },
        });
        break;
      case 'EXPORT':
        let currentLayouts = layoutSaveProcessor(
          layoutForm?.getFieldsValue(true),
          'CONTENT',
        );
        exportLayoutData(currentLayouts, '内容');

        let currentHeaderLayouts = layoutSaveProcessor(
          layoutForm?.getFieldsValue(true),
          'HEADER',
        );
        exportLayoutData(currentHeaderLayouts, '表头');
        break;
      case 'MENU_EDIT':
        Emitter.emit(
          DmrConfigurationConstants.DMR_CONFIGURATION_MENU_EDIT,
          true,
        );
        break;
      case 'DMR_OPERATOR_EDIT':
        Emitter.emit(
          DmrConfigurationConstants.DMR_CONFIGURATION_OPERATORS_EDIT,
          true,
        );
        break;
      case 'PRE_RULE':
        Emitter.emit(
          DmrConfigurationConstants.DMR_CONFIGURATION_PRE_CHECK_RULES_EDIT,
          true,
        );
        break;
      case 'DMR_PRE_CHECK_MODULE':
        Emitter.emit(
          DmrConfigurationConstants.DMR_CONFIGURATION_PRE_CHECK_MODULE_EDIT,
          true,
        );
        break;
      case 'DMR_PREVIEW':
        Emitter.emit(
          DmrConfigurationConstants.DMR_CONFIGURATION_DATA_PREVIEW,
          true,
        );
        break;
      case 'THEME_EDIT':
        Emitter.emit(
          DmrConfigurationConstants.DMR_CONFIGURATION_THEME_EDIT,
          true,
        );
        break;
      case 'DOCS':
        window.open('/docs');
        break;
      case 'DMR_PUBLIC':
        setDmrIndexPublicMode();
        break;
      default:
        break;
    }
  };

  const setDmrIndexPublicMode = async () => {
    let data = {
      IdentityCode: 'Global',
      IdentityType: 'Global',
      ConfigModule: 'PublicMode',
      FullReplace: true,
      values: {
        PublicMode: !publicMode,
      },
    };

    let publicModeResponse: RespVO<any> = await uniCommonService(
      'Api/Sys/ClientKitSys/SetValue',
      {
        method: 'POST',
        requestType: 'json',
        data: data,
      },
    );

    if (publicModeResponse.statusCode === 200) {
      message.success('更新成功');
      global['PublicMode'] = !publicMode;
      setPublicMode(!publicMode);
    }
  };

  const saveLayout = async () => {
    let layoutEditValues = layoutForm?.getFieldsValue(true);
    Emitter.emit(DmrConfigurationConstants.DMR_CONFIGURATION_LOADING, true);
    console.log('saveLayout layoutEditValues', layoutEditValues);
    let contentLayout = layoutSaveProcessor(layoutEditValues, 'CONTENT');
    let headerLayout = layoutSaveProcessor(layoutEditValues, 'HEADER');
    // // header layout
    await saveGlobalDmrIndexLayouts(headerLayout, 'DmrHeaderLayout');
    // content layout
    await saveGlobalDmrIndexLayouts(contentLayout, 'DmrLayout');
    // 保存 当前module 信息
    saveGlobalDmrModuleInfo(contentLayout, headerLayout);

    message.success('保存成功');
    Emitter.emit(DmrConfigurationConstants.DMR_CONFIGURATION_LOADING, false);
  };

  return (
    <div className={'dmr-configuration-container'}>
      <div className={'center-container'}>
        <div
          id={'dmr-configuration-operations'}
          className={'dmr-configuration-operation-container'}
        >
          <div className={'flex-row-center'}>
            {otherOperations?.map((item) => {
              return (
                <Button
                  onClick={() => {
                    onOperationClick(item.key);
                  }}
                  type="primary"
                  className={'item'}
                >
                  {item?.key === 'DMR_PUBLIC'
                    ? `首页模式（${publicMode ? '公开' : '不公开'}）`
                    : item.label}
                </Button>
              );
            })}
          </div>
          <div className={'flex-row-center'}>
            {layoutOperations?.map((item) => {
              return (
                <>
                  {item?.key === 'IMPORT' || item?.key === 'IMPORT_HEADER' ? (
                    <LayoutImport
                      label={item?.label}
                      onImportClick={() => {}}
                      onImportSuccess={() => {}}
                      type={item?.key}
                    />
                  ) : item?.key === 'RESOLUTION' ? (
                    <Popover
                      content={item?.content}
                      placement={'bottom'}
                      trigger="hover"
                    >
                      <Button type="primary" className={'item'}>
                        {item.label}
                      </Button>
                    </Popover>
                  ) : (
                    <Button
                      onClick={() => {
                        onOperationClick(item.key);
                      }}
                      type="primary"
                      className={'item'}
                    >
                      {item.label}
                    </Button>
                  )}
                </>
              );
            })}
          </div>
        </div>

        <div
          id={'dmr-configuration-wrapper'}
          style={{
            height:
              document.getElementById('site-layout-content')?.offsetHeight -
              (document.getElementById('dmr-configuration-operations')
                ?.offsetHeight ?? 0) -
              10 -
              50,
          }}
        >
          <DmrConfigurationFormContainer
            layoutForm={layoutForm}
            layouts={layouts}
            headerLayouts={headerLayouts}
            resolutionType={resolutionType}
            valueForm={form}
          />

          <PreCheckRuleEdit />

          <OperatorsEdit />

          <PreCheckModuleEdit />

          <DmrConfigurationLoading />

          <DmrDataPreview />

          <DmrThemeEdit />

          <Form form={layoutForm}>
            {[
              'layouts',
              'contentData',
              'headerContentData',
              'headerLayouts',
            ]?.map((key) => {
              return <Form.Item name={key} hidden={true} />;
            })}
          </Form>
        </div>
      </div>

      <PropertyContainer
        existTableComponentNames={layoutForm
          .getFieldValue('contentData')
          ?.filter((item) => item?.component?.indexOf('Table') > -1)
          ?.map((item) => item?.component)}
      />
    </div>
  );
};

const ResponsiveReactGridLayout = WidthProvideRGL(
  withResizeDetector(Responsive as any, {
    handleHeight: false,
    refreshMode: 'debounce',
    refreshRate: 500,
    refreshOptions: {
      leading: true,
      trailing: true,
    },
  }),
);

interface DmrConfigurationFormContainerProps {
  layoutForm: any;
  valueForm: any;
  layouts: any;
  headerLayouts: any;
  resolutionType: string;
}

const DmrConfigurationFormContainer = (
  props: DmrConfigurationFormContainerProps,
) => {
  // const responsiveGridLayoutRef = useRef();

  const viewMode = true;

  const [dmrLayoutLoading, setDmrLayoutLoading] = useState(true);

  // const [layoutContentData, setLayoutContentData] = useState<any>([]);
  // const [layouts, setLayouts] = useState<any>({});

  // const [headerContentData, setHeaderContentData] = useState<any>([]);
  // const [headerLayouts, setHeaderLayouts] = useState<any>({});

  const [formCardInfo, setFormCardInfo] = useState(undefined);

  const [breakpoint, setBreakpoint] = useState('lg');

  const [dmrProcessorInstance] = useState(() => new DmrProcessor());

  let layoutContentData = Form.useWatch('contentData', props?.layoutForm);
  let layouts = Form.useWatch('layouts', props?.layoutForm);

  let headerContentData = Form.useWatch('headerContentData', props?.layoutForm);
  let headerLayouts = Form.useWatch('headerLayouts', props?.layoutForm);

  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateFromMaster',
  );

  const breakpoints = (type: 'CONTENT' | 'HEADER'): Breakpoint[] => [
    {
      // 1920 * 1080
      c: LAYOUT_COLUMNS['lg'],
      w: 1508,
    },
    {
      // 1920 * 1080 右侧推出
      c: LAYOUT_COLUMNS['lg'],
      w: 1238,
    },
    {
      // 1600 * 900
      c: LAYOUT_COLUMNS['lg'],
      w: 1171,
    },
    {
      // 1600 * 900 右侧推出
      // layout: 'list' as any,
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER'
            ? props?.layoutForm?.getFieldValue('headerLayouts')
            : props?.layoutForm?.getFieldValue('layouts'),
        );
      },
      c: LAYOUT_COLUMNS['md'],
      w: 918,
    },
    {
      // 1440 * 800
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER'
            ? props?.layoutForm?.getFieldValue('headerLayouts')
            : props?.layoutForm?.getFieldValue('layouts'),
        );
      },
      c: LAYOUT_COLUMNS['md'],
      w: 1028,
    },
    {
      // 1440 * 800 推出
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['sm'],
      w: 790,
    },
    {
      // 1366 * 768
      layout: (
        column: number,
        oldColumn: number,
        nodes: GridStackNode[],
        oldNodes: GridStackNode[],
      ) => {
        onGridColumnChange(
          type,
          column,
          oldColumn,
          nodes,
          oldNodes,
          type === 'HEADER' ? headerLayouts : layouts,
        );
      },
      c: LAYOUT_COLUMNS['sm'],
      w: 954,
    },
  ];

  console.log(
    'layoutForm',
    layouts,
    layoutContentData,
    headerLayouts,
    headerContentData,
  );

  // instance update layout
  const loadContentLayout = (
    instance: GridStack,
    layout: any[],
    addRemove: boolean = true,
  ) => {
    let stackLayouts = [];
    layout?.forEach((item) => {
      instance.makeWidget(`#${item?.i}`);
      stackLayouts.push({
        id: item?.i,
        x: item?.x,
        y: item?.y,
        // sizeToContent: true,
        w: item?.w,
      });
    });

    instance.load(stackLayouts, addRemove);
    instance.batchUpdate(false);
  };

  useEffect(() => {
    setBreakpoint(props?.resolutionType);
  }, [props?.resolutionType]);

  useEffect(() => {
    let gridOpts = {
      column: LAYOUT_COLUMNS['lg'],
      cellHeight: ROW_HEIGHT + 3,
      sizeToContent: true,
      marginTop: 0,
      marginLeft: 0,
      marginRight: 0,
      marginBottom: 0,
      alwaysShowResizeHandle: true,
      resizable: {
        handles: 'w, e',
      },
    };

    // content & header grid instance
    if (contentGridInstance === undefined) {
      contentGridInstance = GridStack.init(
        {
          columnOpts: {
            columnMax: LAYOUT_COLUMNS['lg'],
            layout: 'move' as any,
            breakpoints: breakpoints('CONTENT')?.sort((a, b) => a.w - b.w),
          },
          ...gridOpts,
        },
        document.getElementById('dmr-content-grid-layout'),
      );

      // 设置特殊表格组配置
      contentGridInstance?.engine?.setSpecialTableGroups([
        ['diagnosisMainTable', 'diagnosisInsurTable'], // 诊断主表和医保表保持同行
        ['operationMainTable', 'operationInsurTable'], // 手术主表和医保表保持同行
        // 可以在这里添加更多的特殊表格组
      ]);

      contentGridInstance?.on('change', (name, callback) => {
        let items = contentGridInstance?.getGridItems();
        let layouts = props?.layoutForm?.getFieldValue('layouts');
        layouts[breakpoint] = mergeLayoutsAndGridStackItemPosition(
          items,
          layouts[breakpoint],
        );
        props?.layoutForm?.setFieldValue('layouts', layouts);

        setTimeout(() => {
          Emitter.emit(EventConstant.DMR_MENU_POSITION_RECALCULATE);
          Emitter.emit(EventConstant.DMR_LINE_NUMBER_RECALCULATE);
        }, 300);
      });
      contentGridInstance?.on(
        'dragstop',
        (event: Event, el: GridItemHTMLElement) => {
          let layouts = onGridItemDragStop(
            event,
            el,
            props?.layoutForm?.getFieldValue('layouts'),
            breakpoint,
          );
          props?.layoutForm?.setFieldValue('layouts', layouts);
        },
      );
      contentGridInstance?.on(
        'resizestop',
        (event: Event, el: GridItemHTMLElement) => {
          let layouts = onGridItemResizeStop(
            event,
            el,
            props?.layoutForm?.getFieldValue('layouts'),
            breakpoint,
          );
          props?.layoutForm?.setFieldValue('layouts', layouts);
        },
      );

      global['contentGridInstance'] = contentGridInstance;
    }

    if (headerGridInstance === undefined) {
      headerGridInstance = GridStack.init(
        {
          columnOpts: {
            columnMax: LAYOUT_COLUMNS['lg'],
            layout: 'move' as any,
            breakpoints: breakpoints('HEADER')?.sort((a, b) => a.w - b.w),
          },
          ...gridOpts,
        },
        document.getElementById('dmr-header-grid-layout'),
      );

      headerGridInstance?.on('change', (name, callback) => {
        let items = headerGridInstance?.getGridItems();
        let headerLayouts = props?.layoutForm?.getFieldValue('headerLayouts');
        headerLayouts[breakpoint] = mergeLayoutsAndGridStackItemPosition(
          items,
          headerLayouts[breakpoint],
        );
        props?.layoutForm?.setFieldValue('headerLayouts', headerLayouts);
      });

      headerGridInstance?.on(
        'dragstop',
        (event: Event, el: GridItemHTMLElement) => {
          let headerLayouts = onGridItemDragStop(
            event,
            el,
            props?.layoutForm?.getFieldValue('headerLayouts'),
            breakpoint,
          );
          props?.layoutForm?.setFieldValue('headerLayouts', headerLayouts);
        },
      );

      headerGridInstance?.on(
        'resizestop',
        (event: Event, el: GridItemHTMLElement) => {
          let headerLayouts = onGridItemResizeStop(
            event,
            el,
            props?.layoutForm?.getFieldValue('headerLayouts'),
            breakpoint,
          );
          props?.layoutForm?.setFieldValue('headerLayouts', headerLayouts);
        },
      );

      global['headerGridInstance'] = headerGridInstance;
    }

    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_RECORD_SELECT_PREVIEW,
      (record) => {
        getDmrCardInfo(record?.HisId);
      },
    );

    return () => {
      Emitter.off(
        DmrConfigurationConstants.DMR_CONFIGURATION_RECORD_SELECT_PREVIEW,
      );
    };
  }, []);

  useEffect(() => {
    let layouts = cloneDeep(props?.layouts);
    let headerLayouts = cloneDeep(props?.headerLayouts);

    Object.keys(layouts)?.forEach((key) => {
      layouts?.[key]?.forEach((item: any) => {
        item['gridId'] = uuidv4();
      });
    });
    Object.keys(headerLayouts)?.forEach((key) => {
      headerLayouts?.[key]?.forEach((item: any) => {
        item['gridId'] = uuidv4();
      });
    });
    // content layout
    // setLayoutContentData(layouts?.['lg']);
    // setLayouts(layouts);
    // header layout
    // setHeaderContentData(headerLayouts?.['lg']);
    // setHeaderLayouts(headerLayouts);

    setDmrLayoutLoading(true);

    // 保存一份到form
    props?.layoutForm?.setFieldsValue({
      contentData: layouts?.['lg'],
      layouts: layouts,

      headerContentData: headerLayouts?.['lg'],
      headerLayouts: headerLayouts,
    });

    console.log('layoutForm', props?.layoutForm?.getFieldsValue());
    setTimeout(() => {
      setDmrLayoutLoading(false);
    }, 500);
  }, [props?.layouts, props?.headerLayouts, props?.layoutForm]);

  useEffect(() => {
    if (contentGridInstance) {
      if (!isEmptyValues(layouts?.[breakpoint])) {
        contentGridInstance.batchUpdate();
        contentGridInstance.removeAll(false);
        loadContentLayout(contentGridInstance, layouts[breakpoint]);
      }
    }
  }, [layouts, contentGridInstance, layoutContentData]);

  useEffect(() => {
    if (headerGridInstance && !isEmptyValues(headerLayouts)) {
      if (!isEmptyValues(headerLayouts?.[breakpoint])) {
        headerGridInstance.batchUpdate();
        headerGridInstance.removeAll(false);
        loadContentLayout(headerGridInstance, headerLayouts[breakpoint]);
      }
    }
  }, [headerLayouts, headerGridInstance, headerContentData]);

  useEffect(() => {
    Emitter.on(
      DmrConfigurationConstants.DMR_CONFIGURATION_ITEM_LAYOUT_ADD,
      (data) => {
        console.log('ADD DATA', data);
        let previousItemId = data?.previousItemId;
        let itemData = {
          ...omit(data, ['previousItemId']),
          ...layoutAddOtherProperties,
        };

        let layoutItem = layoutItemAddProcessor(itemData);
        let contentItem = contentItemAddProcessor(itemData);
        if (data?.contentAddZone === 'CONTENT') {
          // layouts
          let currentLayouts = {};
          let layouts = props?.layoutForm.getFieldValue('layouts');
          let layoutContentData =
            props?.layoutForm?.getFieldValue('contentData');

          Object.keys(layouts)?.forEach((key) => {
            let newLayouts = layouts[key];
            if (previousItemId) {
              let previousItemIndex = newLayouts?.findIndex(
                (item) => item?.i === previousItemId,
              );
              if (previousItemIndex > -1) {
                newLayouts.splice(previousItemIndex + 1, 0, layoutItem);
              } else {
                newLayouts.push(layoutItem);
              }
            } else {
              newLayouts.push(layoutItem);
            }
          });

          // contentData
          let currentContentData = [...(layoutContentData ?? []), contentItem];

          props?.layoutForm.setFieldsValue({
            contentData: currentContentData,
          });
        } else {
          // layouts
          let currentHeaderLayouts = {};
          Object.keys(headerLayouts)?.forEach((key) => {
            let newLayouts = headerLayouts[key];
            if (previousItemId) {
              let previousItemIndex = newLayouts?.findIndex(
                (item) => item?.i === previousItemId,
              );
              if (previousItemIndex > -1) {
                newLayouts.splice(previousItemIndex + 1, 0, layoutItem);
              } else {
                newLayouts.push(layoutItem);
              }
            } else {
              newLayouts.push(layoutItem);
            }
          });

          // contentData
          let currentHeaderContentData = [
            ...(headerContentData ?? []),
            contentItem,
          ];

          props?.layoutForm.setFieldsValue({
            headerContentData: currentHeaderContentData,
          });
        }

        // setDmrLayoutLoading(true);
      },
    );

    return () => {
      Emitter.off(DmrConfigurationConstants.DMR_CONFIGURATION_ITEM_LAYOUT_ADD);
    };
    // }, [layouts, headerLayouts, layoutContentData, headerContentData]);
  }, [layoutContentData, headerContentData]);

  useEffect(() => {
    props?.valueForm.resetFields();
  }, [formCardInfo, props?.valueForm]);

  const items: MenuProps['items'] = [
    {
      label: '新增',
      key: 'ITEM_ADD',
    },
    {
      label: '修改',
      key: 'EDIT',
    },
    {
      label: '删除',
      key: 'DELETE',
    },
  ];

  const GridItemContainer = (props: any) => {
    console.log('GridItemContainer', props);

    const [gridItemData, setGridItemData] = useState<any>(props?.item);

    let currentGridItem = Utils.getElement(
      `#${props?.id}`,
    ) as GridItemHTMLElement;
    let gridNode = currentGridItem?.gridstackNode ?? props?.item;

    useEffect(() => {
      Emitter.on(
        `${DmrConfigurationConstants.DMR_CONFIGURATION_ITEM_DATA_CHANGE}${props?.item?.gridId}`,
        (payload: any) => {
          let data = payload?.latestComponentData;
          setGridItemData(data);
          // data?.data?.key 会根据用户输入得值更新，所有set得时候要使用i（id）
          props?.layoutForm?.setFieldValue(data?.i, data);
          console.log(
            'GridItemContainer Emitter',
            data,
            props?.layoutForm?.getFieldsValue(),
          );

          instantModelItemsRequest(
            data?.data?.props?.modelDataKey,
            data?.data?.props?.modelDataGroup,
          );
          if (data?.data?.suffixProps) {
            instantModelItemsRequest(
              data?.data?.props?.suffixProps?.modelDataKey,
              data?.data?.props?.suffixProps?.modelDataGroup,
            );
          }

          if (payload?.positionEdited || payload?.sizeEdited) {
            let gridInstance =
              payload?.itemGroup === 'HEADER'
                ? headerGridInstance
                : contentGridInstance;
            let currentContentGridItems = gridInstance?.getGridItems();

            let currentItem = currentContentGridItems?.find(
              (item) => item?.id === data?.i,
            );
            if (currentItem) {
              let elementMoveOpts: GridStackWidget = {};
              // 表示变更过  移动一下element
              if (payload?.positionEdited) {
                elementMoveOpts['x'] = data?.x;
                elementMoveOpts['y'] = data?.y;
              }

              if (payload?.sizeEdited) {
                elementMoveOpts['w'] = data?.w;
                elementMoveOpts['h'] = data?.h;
              }
              console.log(
                'gridInstance update before',
                currentItem,
                gridInstance,
              );
              if (!isEmptyValues(elementMoveOpts)) {
                gridInstance.update(currentItem, elementMoveOpts);
              }
            }
          }
          console.log('after total', contentGridInstance?.getGridItems());
        },
      );

      return () => {
        Emitter.off(
          `${DmrConfigurationConstants.DMR_CONFIGURATION_ITEM_DATA_CHANGE}${props?.item?.gridId}`,
        );
      };
    }, [gridItemData]);

    useEffect(() => {
      if (!isEqual(gridItemData, props?.item)) {
        setGridItemData(props?.item);
      }
    }, [props?.item]);

    const onContextMenuItemClick: MenuProps['onClick'] = ({ item, key }) => {
      console.log('onContextMenuItemClick', key, item, props?.item);

      switch (key) {
        case 'ITEM_ADD':
          Emitter.emit(
            DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_STATUS,
            {
              status: true,
              type: 'ADD',
              itemType: props?.type,
              currentItemId: props?.item?.i,
              defaultParams: {
                x: gridNode?.x + gridNode?.w,
                y: gridNode?.y,
              },
            },
          );
          break;
        case 'EDIT':
          Emitter.emit(
            DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_STATUS,
            {
              status: true,
              type: 'EDIT',
              itemType: props?.type,
              // gridItemRef: responsiveGridLayoutRef,
            },
          );
          Emitter.emit(
            DmrConfigurationConstants.DMR_CONFIGURATION_DRAWER_DATA,
            {
              ...gridItemData,
              x: gridNode?.x,
              y: gridNode?.y,
              h: gridNode?.h,
              w: gridNode?.w,
            },
          );
          break;
        case 'DELETE':
          let componentTitle =
            tableLabelMapping?.[gridItemData?.data?.component] ??
            gridItemData?.data?.prefix;

          Modal.confirm({
            title: `确定删除“${componentTitle}”吗？`,
            content: '',
            onOk: () => {
              console.log('props?.layoutForm', props?.layoutForm);

              let { layouts, contentData, headerContentData, headerLayouts } =
                props?.layoutForm.getFieldsValue(true);

              if (props?.type === 'CONTENT') {
                contentData = contentData?.filter((item) => {
                  return item?.data?.key !== gridItemData?.data?.key;
                });

                Object.keys(layouts)?.forEach((key) => {
                  layouts[key] = layouts[key]?.filter((item) => {
                    return item?.i !== gridItemData?.data?.key;
                  });
                });
                props?.layoutForm?.setFieldValue('contentData', contentData);
              } else {
                headerContentData = headerContentData?.filter((item) => {
                  return item?.i !== gridItemData?.data?.key;
                });
                Object.keys(headerLayouts)?.forEach((key) => {
                  headerLayouts[key] = headerLayouts[key]?.filter((item) => {
                    return item?.i !== gridItemData?.data?.key;
                  });
                });
                // setHeaderContentData(headerContentData);
                // setHeaderLayouts(currentLayouts);
                // setDmrLayoutLoading(true);
                props?.layoutForm?.setFieldValue(
                  'headerContentData',
                  headerContentData,
                );
              }
            },
            onCancel: () => {},
          });

          break;
        default:
          break;
      }
    };

    console.log('DropDown', props?.item);

    const itemPositionTooltipContent = `X: ${gridNode?.x}, Y: ${gridNode?.y}, 宽度: ${gridNode?.w}, 高度: ${gridNode?.h}`;

    return (
      <Dropdown
        key={props?.item?.data?.key}
        autoFocus={true}
        // open={
        //   props?.item?.data?.key === rightClickItem?.item?.data?.key
        // }
        menu={{
          items,
          onClick: onContextMenuItemClick,
        }}
        // TODO 修复popup container 会出现 外层滚动问题
        // getPopupContainer={(triggerNode) =>
        //   triggerNode?.parentNode
        // }
        trigger={['contextMenu']}
      >
        <div
          id={props?.id}
          key={props?.item?.data?.key}
          style={{ ...props?.style }}
          onContextMenu={(event) => {
            console.log('onContextMenu', event);
            // setRightClickItem(props);
          }}
          className={`grid-stack-item ${props?.className ?? ''} `}
          gs-id={props?.id}
          gs-x={gridNode?.x}
          gs-y={gridNode?.y}
          gs-w={gridNode?.w}
        >
          <Tooltip
            className={'grid-item-tooltip-container'}
            title={itemPositionTooltipContent}
            placement={'topLeft'}
            trigger={['hover']}
            getPopupContainer={(triggerNode) => {
              return document.getElementById(
                props?.type === 'CONTENT'
                  ? 'dmr-content-container'
                  : 'dmr-header-container',
              );
            }}
            overlayClassName={'grid-item-tooltip-overlay'}
          >
            <GridItem
              underConfiguration={true}
              containerClassName={props?.className}
              form={props?.valueForm}
              index={props?.index}
              componentId={gridItemData?.data.key}
              key={gridItemData?.data.key}
              data={gridItemData?.data}
            />
          </Tooltip>
          {props?.children}
        </div>
      </Dropdown>
    );
  };

  const dmrConfigurationChildren = React.useMemo(() => {
    return layoutContentData?.map((item, index) => {
      return (
        <GridItemContext.Provider
          value={{
            dynamicComponentsMap: dynamicComponentsMap,
            externalConfig: externalDmrConfig,
            eventNames: {
              HELP_MODAL: EventConstant.DMR_HELP_MODAL,
              TABLE_NEXT_KEY: EventConstant.DMR_TABLE_NEXT_KEY,
            },
            modelGroup: 'Dmr',
            underConfiguration: true,
          }}
        >
          <GridItemContainer
            id={item.data.key}
            key={item.data.key}
            item={item}
            index={index}
            layoutForm={props?.layoutForm}
            type={'CONTENT'}
          />
        </GridItemContext.Provider>
      );
    });
  }, [layoutContentData]);

  const onLayoutChange = (layout, layouts) => {
    // 画完了
    // generateTopMenus();

    // process no blank lines
    processNoBlankLine(layout);
    Object.keys(layouts)?.forEach((key) => {
      processNoBlankLine(layouts[key]);
    });

    setTimeout(() => {
      Emitter.emit(EventConstant.DMR_TABLE_LAYOUT_CHANGE_MENU, layout);
      Emitter.emit(EventConstant.DMR_TABLE_LAYOUT_CHANGE_SEPARATOR, layout);
    }, 0);
    console.log('onLayoutChange layout', layouts, layout);

    // 保存一份到form
    props?.layoutForm?.setFieldValue('layouts', layouts);
    console.log(
      'onLayoutChange layout',
      props?.layoutForm?.getFieldValue('layouts'),
    );

    setDmrLayoutLoading(false);
  };

  const instantModelItemsRequest = (
    modelDataKey: string,
    modelDataGroup?: string,
  ) => {
    // 实时请求module & moduleGroup
    if (modelDataKey) {
      (global?.window as any)?.eventEmitter?.emit('DICT_DATA_FETCH', {
        modelDataKey: modelDataKey,
        modelDataGroup: modelDataGroup,
      });
    }
  };

  const getDmrCardInfo = async (hisId: string) => {
    if (hisId) {
      // setDmrCardInfoLoading(true);

      let { formFieldValue, cardBundleInfo } = await getCardInfoV2(
        hisId,
        dmrProcessorInstance,
      );

      let formCardInfo = mergeWith(
        {},
        defaultFieldValue,
        formFieldValue,
        (objValue, srcValue) => {
          if (srcValue !== null && srcValue !== undefined) {
            return srcValue;
          }

          return objValue;
        },
      );

      props?.valueForm.resetFields();
      setFormCardInfo(formCardInfo);
      // setCurrentDmrHisId(hisId);
      // setDmrCardInfoLoading(false);
    } else {
      message.error('HisId缺失，请检查');
    }
  };

  let defaultWidth =
    document.getElementById('dmr-form-container')?.getBoundingClientRect()
      ?.width - 19;
  console.log('defaultWidth', defaultWidth);

  return (
    <>
      <div className="ant-card">
        <MenuEdit layouts={layouts} breakpoint={breakpoint} />

        <div
          id={'dmr-root-container'}
          className={'dmr-root-container'}
          style={{
            height: document.getElementById('dmr-configuration-wrapper')
              ?.offsetHeight,
            margin: '0px 10px',
          }}
        >
          <div id={'dmr-container'} className={'dmr-container'}>
            {/*operation header*/}

            <div id={'dmr-main-container'} className={'dmr-main-container'}>
              <RightMenu
                defaultTopMenuKeys={topMenuKeys}
                initialLayout={layouts?.[breakpoint]}
              />

              {/*<OperationHeader form={props?.valueForm} />*/}

              <fieldset
                style={{
                  // height: `calc(100% - ${
                  //   document.getElementById('dmr-root-operation-header')
                  //     ?.offsetHeight + 5
                  // }px)`,
                  height: `100%`,
                }}
                className={`${viewMode ? 'view-mode' : ''}`}
                disabled={viewMode}
              >
                <Form
                  initialValues={formCardInfo || undefined}
                  id={'dmr-form-container'}
                  className={'dmr-form-container'}
                  style={{
                    width:
                      BREAKPOINTS_LABEL[props?.resolutionType]?.width ?? '100%',
                    height: '100%',
                  }}
                  form={props?.valueForm}
                  name="basic"
                  autoComplete="off"
                  wrapperCol={{ flex: 1 }}
                  onValuesChange={(changedValues, allValues) => {
                    console.log('onValuesChange', changedValues, allValues);
                  }}
                  onFieldsChange={(changedFields, allFields) => {
                    console.log('onFieldsChange', changedFields, allFields);
                    Emitter.emit(EventConstant.DMR_FORM_VALUE_CHANGE);
                  }}
                >
                  <Spin
                    size={'large'}
                    wrapperClassName={'dmr-layout-loading'}
                    spinning={dmrLayoutLoading}
                  >
                    <div
                      id={'dmr-header-container'}
                      className={'dmr-header-container'}
                      style={{
                        marginLeft: 20,
                        padding: '0px 5px 0px 85px',
                      }}
                    >
                      <DmrHeader
                        form={props?.valueForm}
                        viewMode={true}
                        registerStatusName={''}
                        layoutSizeKey={breakpoint}
                        headerLayouts={headerLayouts}
                        headerContentData={headerContentData}
                        itemWrapper={GridItemContainer}
                        layoutForm={props?.layoutForm}
                        onLayoutChange={(layout, layouts) => {
                          Object.keys(layouts)?.forEach((key) => {
                            processNoBlankLine(layouts[key]);
                          });

                          props?.layoutForm?.setFieldValue(
                            'headerLayouts',
                            layouts,
                          );
                          setDmrLayoutLoading(false);
                        }}
                        onDragStop={(
                          newLayout,
                          oldDragItem,
                          newDragItem,
                          placeHlder,
                          e,
                          node,
                        ) => {
                          // processNoBlankLine(newLayout);
                        }}
                      />
                    </div>

                    <div
                      id={'dmr-content-container'}
                      className={'dmr-content-container'}
                      style={{
                        flex: 1,
                        // height: `calc(100% - ${
                        //   // document.getElementById('dmr-header-container')
                        //   //   ?.offsetHeight + 2
                        //   dmrHeaderHeight + 2
                        // }px)`,
                        minWidth: 324,
                        overflowX: 'hidden',
                        padding: '0px 5px 0px 85px',
                      }}
                    >
                      {/*行号*/}
                      <LineNumber initialLayout={layouts?.[breakpoint]} />

                      <Separators
                        width={defaultWidth}
                        initialLayout={layouts?.[breakpoint]}
                        extra={{}}
                      />

                      <div
                        id={'dmr-content-grid-layout'}
                        className="grid-stack"
                      >
                        {dmrConfigurationChildren}
                      </div>

                      {/*<ReactResizeDetector*/}
                      {/*  handleWidth={true}*/}
                      {/*  handleHeight={false}*/}
                      {/*  refreshMode={'debounce'}*/}
                      {/*  refreshRate={500}*/}
                      {/*  refreshOptions={{*/}
                      {/*    leading: true,*/}
                      {/*    trailing: true,*/}
                      {/*  }}*/}
                      {/*>*/}
                      {/*  {({ width, height }) => {*/}
                      {/*    return (*/}
                      {/*      <>*/}
                      {/*        <Separators*/}
                      {/*          width={width ?? defaultWidth}*/}
                      {/*          initialLayout={layouts?.[breakpoint]}*/}
                      {/*        />*/}

                      {/*        <Responsive*/}
                      {/*          style={{ marginLeft: 20 }}*/}
                      {/*          // innerRef={responsiveGridLayoutRef}*/}
                      {/*          id={'dmr-grid-layout'}*/}
                      {/*          className="layout"*/}
                      {/*          breakpoints={BREAKPOINTS}*/}
                      {/*          layouts={layouts}*/}
                      {/*          width={width ?? defaultWidth}*/}
                      {/*          isDraggable={true}*/}
                      {/*          isResizable={true}*/}
                      {/*          // layouts={{*/}
                      {/*          //   lg: layout,*/}
                      {/*          // }}*/}
                      {/*          cols={LAYOUT_COLUMNS}*/}
                      {/*          rowHeight={ROW_HEIGHT}*/}
                      {/*          verticalCompact={false}*/}
                      {/*          isBounded={true}*/}
                      {/*          margin={[10, 5]}*/}
                      {/*          // cols={COL_NUM}*/}
                      {/*          // width={tableWidth}*/}
                      {/*          onLayoutChange={(layout, layouts) =>*/}
                      {/*            onLayoutChange(layout, layouts)*/}
                      {/*          }*/}
                      {/*          onBreakpointChange={(*/}
                      {/*            newBreakpoint: string,*/}
                      {/*            newCols: number,*/}
                      {/*          ) => {*/}
                      {/*            console.error(*/}
                      {/*              'onBreakpointChange',*/}
                      {/*              newBreakpoint,*/}
                      {/*              newCols,*/}
                      {/*            );*/}
                      {/*            setBreakpoint(newBreakpoint);*/}
                      {/*          }}*/}
                      {/*          onWidthChange={(*/}
                      {/*            containerWidth: number,*/}
                      {/*            margin: [number, number],*/}
                      {/*            cols: number,*/}
                      {/*            containerPadding: [number, number],*/}
                      {/*          ) => {*/}
                      {/*            console.log('onWidthChange', containerWidth);*/}
                      {/*            Emitter.emit(*/}
                      {/*              [EventConstant.DMR_TABLE_LAYOUT_CHANGE],*/}
                      {/*              containerWidth,*/}
                      {/*            );*/}
                      {/*          }}*/}
                      {/*          onDragStop={(*/}
                      {/*            newLayout,*/}
                      {/*            oldDragItem,*/}
                      {/*            newDragItem,*/}
                      {/*            placeHlder,*/}
                      {/*            e,*/}
                      {/*            node,*/}
                      {/*          ) => {*/}
                      {/*            // processNoBlankLine(newLayout);*/}
                      {/*          }}*/}
                      {/*          onResizeStop={(*/}
                      {/*            newLayout,*/}
                      {/*            oldDragItem,*/}
                      {/*            newDragItem,*/}
                      {/*            placeHlder,*/}
                      {/*            e,*/}
                      {/*            node,*/}
                      {/*          ) => {*/}
                      {/*            // processNoBlankLine(newLayout);*/}
                      {/*          }}*/}
                      {/*        >*/}
                      {/*          {dmrConfigurationChildren}*/}
                      {/*        </Responsive>*/}
                      {/*      </>*/}
                      {/*    );*/}
                      {/*  }}*/}
                      {/*</ReactResizeDetector>*/}

                      {/*说明 20250603 陈总要求先干掉*/}
                      {/* <div className={'extra-explanation-container'}>
                        <div className={'flex-row'}>
                          <InputSuffix
                            style={{
                              flexFlow: 'row wrap',
                            }}
                            prefix={'说明：（一）医疗付费方式：'}
                            hideInput={true}
                            formKey={'medical-payment'}
                            suffixModuleKey={'YLFKFS'}
                            suffixModuleGroup={'Dmr'}
                          />
                        </div>

                        <span>
                          （二）凡可由医院信息系统提供住院费⽤清单的,住院病案⾸⻚中可不填写“住院费⽤”
                        </span>
                      </div> */}
                    </div>
                  </Spin>
                </Form>
              </fieldset>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DmrConfiguration;
