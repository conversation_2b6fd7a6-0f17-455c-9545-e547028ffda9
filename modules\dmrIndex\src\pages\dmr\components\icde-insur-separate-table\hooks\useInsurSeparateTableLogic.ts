import { useEffect, useMemo } from 'react';
import { useModel } from 'umi';
import {
  INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY,
  setSessionJSON,
} from '../utils/session';

interface UseInsurSeparateTableLogicProps {
  form: any;
  originDmrCardInfo: any;
}

export function safeJsonParse(
  data,
  throwErrIfParseFail = false,
  response = null,
  request = null,
) {
  try {
    return JSON.parse(data);
  } catch (e) {
    if (throwErrIfParseFail) {
      console.error(response, 'JSON.parse fail', data, request, 'ParseError');
    }
  } // eslint-disable-line no-empty
  return data;
}

export const useInsurSeparateTableLogic = ({
  form,
  originDmrCardInfo,
}: UseInsurSeparateTableLogicProps) => {
  const {
    globalState: { userInfo },
  } = useModel('@@qiankunStateFromMaster');

  // 判断表格联动flag：当LastSubmitInfo中任意角色包含Medicare时为true，否则为false
  const isTableLinkageDisabled = useMemo(() => {
    const lastSubmitInfo = safeJsonParse(
      originDmrCardInfo?.CardFlat?.LastSubmitInfo || '{}',
    );
    console.log('lastSubmitInfo', lastSubmitInfo);
    if (!lastSubmitInfo || !Array.isArray(lastSubmitInfo)) {
      return false;
    }

    return lastSubmitInfo.some((submitInfo) => {
      const roles = submitInfo?.Roles;
      if (!roles || !Array.isArray(roles)) {
        return false;
      }
      return roles.includes('Medicare');
    });
  }, [originDmrCardInfo]);

  // 获取用户权限
  const userRole = useMemo(() => {
    const roles = userInfo?.Roles || [];
    if (roles.includes('Admin')) {
      return 'Admin';
    }
    if (roles.includes('Coder')) {
      return 'Coder';
    }
    if (roles.includes('Medicare')) {
      return 'Medicare';
    }
    return null;
  }, [userInfo]);

  // 判断用户能否编辑Main表格
  const canEditMainTable = useMemo(() => {
    return userRole === 'Admin' || userRole === 'Coder';
  }, [userRole]);

  // 判断用户能否编辑Insur表格
  const canEditInsurTable = useMemo(() => {
    return userRole === 'Admin' || userRole === 'Medicare';
  }, [userRole]);

  // 将联动逻辑写入 sessionStorage，供表格组件优先读取
  useEffect(() => {
    const payload = {
      isTableLinkageDisabled,
      userRole,
      canEditMainTable,
      canEditInsurTable,
    };
    setSessionJSON(INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY, payload);
  }, [isTableLinkageDisabled, userRole, canEditMainTable, canEditInsurTable]);

  console.log('useInsurSeparateTableLogic', isTableLinkageDisabled);
  return {
    // 状态
    isTableLinkageDisabled,
    userRole,
    canEditMainTable,
    canEditInsurTable,
  };
};
