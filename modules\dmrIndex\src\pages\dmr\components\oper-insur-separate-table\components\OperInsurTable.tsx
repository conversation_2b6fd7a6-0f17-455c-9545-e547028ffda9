import React, { useContext, useEffect, useRef, useState } from 'react';
import { Form, Modal } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import { UniDmrDragEditOnlyTable } from '@uni/components/src';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { v4 as uuidv4 } from 'uuid';
import { mergeRefs } from '@uni/utils/src/mergeRefs';
import { tableHotKeys } from '@uni/grid/src/common';
import { useHotkeys } from 'react-hotkeys-hook';
import { mergeColumnsInDmrTable } from '@/pages/dmr/utils';
import { IcdeOperationReadonlyItem } from '@uni/grid/src/components/icde-oper-input/input';
import { isEmptyValues, generateUniqueNumberId } from '@uni/utils/src/utils';
import { getLineArrowUpDownEventKey } from '@uni/components/src/drag-edit-only-table/dmr/UniDmrDragEditOnlyTable';
import { resizeContentById } from '@uni/grid/src/core/custom-utils';
import {
  calculateNextIndex,
  getArrowUpDownEventKey,
  getDeletePressEventKey,
  triggerFormValueChangeEvent,
  waitFocusElementRefocus,
  waitFocusElementRefocusBySelector,
} from '@uni/grid/src/utils';
import GridItemContext from '@uni/commons/src/grid-context';
import { getOperInsurColumns } from '../columns/operInsurColumns';
import {
  getSessionJSON,
  INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY,
} from '../../icde-insur-separate-table/utils/session';

interface OperInsurTableProps {
  form: any;
  id: string;
  parentId?: string;
  className?: string;
  style?: React.CSSProperties;
  columns?: any[];
  underConfiguration?: boolean;
  onChange?: (value: any) => void;
}

const operationCopyKeys =
  (window as any).externalConfig?.['dmr']?.operationCopyKeys ?? [];
const icdeOperFirstMain =
  (window as any).externalConfig?.['dmr']?.icdeOperFirstMain ?? false;
const operCopyFocusKey =
  (window as any).externalConfig?.['dmr']?.operCopyFocusKey ?? undefined;
const operDeleteConfirm =
  (window as any).externalConfig?.['dmr']?.operDeleteConfirm ?? false;
const operationComboDefaultMap =
  (window as any).externalConfig?.['dmr']?.operationComboDefaultMap ?? {};
// 复制键集合
const copyKeys = !isEmptyValues(operationCopyKeys)
  ? operationCopyKeys
  : [
      'OperGroupNo',
      'OprnOprtBegntime',
      'OprnConTime',
      'Operator',
      'Firstasst',
      'Secondasst',
      'AnaType',
      'AnstLvCode',
      'AnaDoc',
      'OprnPatnType',
      'OprnOprtEndtime',
      'OperNote',
      'AnstBegntime',
      'AnstEndtime',
      'OperDept',
      'WoundHealingRateClass',
      'OperAccord',
    ];
const hotKeyToEvents = {
  ADD: (event) => {
    Emitter.emit(EventConstant.DMR_OPER_INSUR_ADD, event.target.id);
  },
  DELETE: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_OPER_INSUR_DELETE, index);
    }
  },
  COPY: (event) => {
    let id = event?.target?.id;
    let indexString = id?.split('#')?.at(2);

    let index = parseInt(indexString);

    if (index > -1) {
      Emitter.emit(EventConstant.DMR_OPER_INSUR_COPY, index);
    }
  },
  SCROLL_LEFT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  },
  SCROLL_RIGHT: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  },
  SCROLL_UP: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: -50,
      behavior: 'smooth',
    });
  },
  SCROLL_DOWN: (event) => {
    let tableBody = document.querySelector(
      "div[id='formItem#operationInsurTable'] div[class='ant-table-body']",
    );
    tableBody?.scrollBy({
      top: 50,
      behavior: 'smooth',
    });
  },
  UP: (event) => {
    Emitter.emit(getArrowUpDownEventKey('operationInsurTable'), {
      event: event,
      type: 'UP',
      trigger: 'hotkey',
    });
  },
  DOWN: (event) => {
    Emitter.emit(getArrowUpDownEventKey('operationInsurTable'), {
      event: event,
      type: 'DOWN',
      trigger: 'hotkey',
    });
  },
};
// 清除键映射
const clearKeysMap = {
  // 仅用于联动删除使用
  OperCode: [
    'OperName',
    'OperCode',
    'InsurName',
    'InsurCode',
    'HqmsName',
    'HqmsCode',
    'OperExtra',
    'HqmsDegree',
    'DegreeRemark',
    'DrgsDegree',
    'RowClassName',
  ],
};

const setFirstItemIsMainOper = (tableData) => {
  if (tableData?.length > 0) {
    tableData?.forEach((item, index) => {
      if (index === 0) {
        item['IsMain'] = true;
      } else {
        item['IsMain'] = false;
      }
    });
  }
};

const OperInsurTable = (props: OperInsurTableProps) => {
  const itemRef = React.useRef<any>();
  const operInsurTableRef = useRef(null);
  const [form] = Form.useForm();
  // 用于触发强制rerender
  const [currentTime, setCurrentTime] = useState(undefined);
  // 仅从 GridItemContext 取权限（诊断实现中还支持 session 切换，这里暂不需要）
  const gridContext = useContext(GridItemContext);
  const insurSeparateTableLogic =
    getSessionJSON(INSUR_SEPARATE_TABLE_LOGIC_SESSION_KEY) ??
    gridContext?.extra?.insurSeparateTableLogic;

  const insurDataSource =
    Form.useWatch('operationInsurTable', props?.form) ?? [];
  const [waitFocusId, setWaitFocusId] = useState<string>(undefined);
  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // 更新行选择状态的函数
  const updateRowSelectionState = () => {
    const tableData = props?.form?.getFieldValue('operation-insur-table') || [];
    const validRows = tableData.filter((row) => row?.id !== 'ADD');

    const selectedRows = validRows.filter((row) => {
      const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
      return rowSelectionValue === true;
    });

    const allSelected =
      selectedRows.length === validRows.length && validRows.length > 0;
    const indeterminate =
      selectedRows.length > 0 && selectedRows.length < validRows.length;
    const hasSelection = selectedRows.length > 0;

    Emitter.emit(`DMR_ROW_SELECTION_STATE_UPDATE_operationInsurTable`, {
      allSelected: allSelected,
      indeterminate: indeterminate,
    });

    // 通知批量删除按钮状态
    Emitter.emit(`DMR_ROW_SELECTION_BATCH_UPDATE_operationInsurTable`, {
      hasSelection: hasSelection,
    });
  };

  useEffect(() => {
    updateRowSelectionState();
  }, [insurDataSource]);

  // 行上下移动事件
  const lineUpDownEvents = {
    LINE_UP: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let focusId = undefined;
      if (index === 0) {
        focusId = itemId;
      } else {
        ids[2] = index - 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('operationInsurTable'), {
        oldIndex: index,
        newIndex: index - 1,
        focusId: focusId,
      });
    },
    LINE_DOWN: (event) => {
      let itemId = event?.target?.id;
      let ids = itemId?.split('#');
      let index = parseInt(ids?.at(2));
      let dataSourceSize = props?.form?.getFieldValue(
        'operation-insur-table',
      ).length;
      let focusId = undefined;
      if (index === dataSourceSize - 1) {
        focusId = itemId;
      } else {
        ids[2] = index + 1;
        focusId = ids?.join('#');
      }
      Emitter.emit(getLineArrowUpDownEventKey('operationInsurTable'), {
        oldIndex: index,
        newIndex: index + 1,
        focusId: focusId,
      });
    },
  };

  const hotKeyRefs = tableHotKeys?.map((item) => {
    return useHotkeys(
      item.key,
      { ...hotKeyToEvents, ...lineUpDownEvents }?.[item?.type],
      {
        preventDefault: true,
        enabled: true,
        enableOnFormTags: true,
        enableOnContentEditable: true,
      },
    );
  });

  const setFirstItemIsMainOper = (tableData: any[], formAdd = false) => {
    // 当且仅当 表格内有主 才给第一条主（并且不是通过添加产生的数据）
    let hasIsMainTrue =
      tableData?.filter((item) => item?.IsMain === true)?.length > 0;
    if (hasIsMainTrue === false && !formAdd) {
      return;
    }

    let firstItem = tableData?.[0];
    tableData?.forEach((item) => {
      item['IsMain'] = false;
      form.setFieldValue([item?.id, 'IsMain'], false);
    });
    if (firstItem) {
      firstItem['IsMain'] = true;
      form.setFieldValue([firstItem?.id, 'IsMain'], true);
      form.setFieldValue([firstItem?.id, 'IsReported'], true);
    }
  };
  // 根据键数组清除值
  const clearValuesByKeys = (keys, index) => {
    const operationDataSource = props?.form?.getFieldValue(
      'operation-insur-table',
    );
    let formItemId = operationDataSource?.[index]?.id;
    let currentFormValues = cloneDeep(form.getFieldsValue(true));
    keys?.forEach((key) => {
      currentFormValues[formItemId][key] = '';
    });

    form.setFieldsValue({
      ...currentFormValues,
    });

    // 更新form
    let tableData = props?.form?.getFieldValue('operation-insur-table');
    keys?.forEach((key) => {
      tableData[index][key] = '';
    });
    // props?.onChange && props?.onChange(tableData);
    props?.form?.setFieldValue('operation-insur-table', cloneDeep(tableData));
    triggerFormValueChangeEvent('operation-insur-table');
  };

  useEffect(() => {
    console.log('insurSeparateTableLogic', insurSeparateTableLogic);
    const base = getOperInsurColumns(insurSeparateTableLogic);
    const columns = mergeColumnsInDmrTable(
      props?.columns,
      base,
      'OperInsurTable',
    );
    setTableColumns(columns);
  }, [props?.columns, insurSeparateTableLogic?.canEditInsurTable]);

  useEffect(() => {
    if (insurSeparateTableLogic?.canEditInsurTable) {
      setTimeout(() => {
        waitFocusElementRefocusBySelector(waitFocusId);
      }, 100);
    }
  }, [insurSeparateTableLogic?.canEditInsurTable, waitFocusId]);

  useEffect(() => {
    // 监听主表格同步事件，用于触发医保表格重新渲染
    Emitter.on(EventConstant.DMR_OPER_INSUR_TABLE_SYNC, (data) => {
      console.log('收到主表格同步事件:', data);
      // 触发重新渲染 - 通过更新状态来强制重新渲染
      setCurrentTime(Date.now());
      // 这里可以通过更新form来触发重新渲染，或者使用forceUpdate等方式
    });

    // delete事件
    Emitter.on(getDeletePressEventKey('operationInsurTable'), (itemId) => {
      let itemIds = itemId?.split('#');
      let index = parseInt(itemIds?.at(2));
      let key = itemIds?.at(1);
      let clearKeys = [key];
      // if (clearKeysMap[key]) {
      //   clearKeys = clearKeysMap[key];
      // }
      clearValuesByKeys(clearKeys, index);
      setTimeout(() => {
        document.getElementById(itemId)?.focus();
      }, 100);
    });

    Emitter.on(EventConstant.DMR_OPER_INSUR_ADD, () => {
      const canEditInsurTable =
        insurSeparateTableLogic?.canEditInsurTable ?? true;
      if (!canEditInsurTable) return;
      let rowData = {
        id: generateUniqueNumberId(),
        IsReported: true,
        UniqueId: uuidv4(),
      };
      let tableData = props?.form?.getFieldValue('operation-insur-table') || [];
      tableData.splice(tableData.length, 0, rowData);
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }
      props?.form?.setFieldValue('operation-insur-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-insur-table');
      setWaitFocusId(
        `div[id=operationInsurTable] tbody > tr:nth-child(${tableData?.length}) > td input`,
      );
      setCurrentTime(Date.now());
    });

    Emitter.on(EventConstant.DMR_OPER_INSUR_DELETE, (index) => {
      const canEditInsurTable =
        insurSeparateTableLogic?.canEditInsurTable ?? true;
      if (!canEditInsurTable) return;
      if (operDeleteConfirm) {
        Modal.confirm({
          title: `确定删除第${index + 1} 条手术数据？`,
          content: '',
          onOk: () => {
            onInsurItemDelete(index);
          },
          getContainer: () => document.getElementById('dmr-main-container'),
        });
      } else {
        console.log('EventConstant.DMR_OPER_INSUR_DELETE', index);
        onInsurItemDelete(index);
      }
    });

    Emitter.on(EventConstant.DMR_OPER_INSUR_COPY, (payload) => {
      let tableData = props?.form?.getFieldValue('operation-insur-table');
      let currentCopyItem = payload?.id
        ? form.getFieldValue(payload?.id)
        : tableData?.[payload?.index];
      let index = payload?.index;

      let copiedItem = {
        id: generateUniqueNumberId(),
        IsReported: true,
        UniqueId: uuidv4(),
      };

      copyKeys?.forEach((key) => {
        copiedItem[key] = currentCopyItem[key];
      });

      tableData.splice(index + 1, 0, copiedItem);

      // TODO 设定主诊为第一个
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }

      if (!isEmptyValues(operCopyFocusKey)) {
        setWaitFocusId(
          `div[id=operationInsurTable] tbody > tr:nth-child(${
            index + 2
          }) > td input[id*=${operCopyFocusKey}]`,
        );
      } else {
        setWaitFocusId(
          `div[id=operationInsurTable] tbody > tr:nth-child(${
            index + 2
          }) > td input`,
        );
      }

      // 更新form
      props?.form?.setFieldValue('operation-insur-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-insur-table');
      setCurrentTime(Date.now());
    });

    // 处理全选/反选事件
    Emitter.on(`DMR_ROW_SELECTION_SELECT_ALL_operationInsurTable`, (data) => {
      const tableData =
        props?.form?.getFieldValue('operation-insur-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 批量更新所有行的选中状态
      validRows.forEach((row) => {
        form.setFieldValue([row.id, 'RowSelection'], data.checked);
      });

      // 触发表单值变化事件
      triggerFormValueChangeEvent('operation-insur-table');

      // 通知选中状态更新
      updateRowSelectionState();
    });

    // 处理单个行选择变化事件
    Emitter.on(`DMR_ROW_SELECTION_ITEM_CHANGE_operationInsurTable`, (data) => {
      // 延迟更新状态，确保表单值已经更新
      setTimeout(() => {
        updateRowSelectionState();
      }, 150);
    });

    // 处理批量删除事件
    Emitter.on(`DMR_BATCH_DELETE_operationInsurTable`, () => {
      const tableData =
        props?.form?.getFieldValue('operation-insur-table') || [];
      const validRows = tableData.filter((row) => row.id !== 'ADD');

      // 获取选中的行索引
      const selectedIndexes = [];
      validRows.forEach((row, index) => {
        const rowSelectionValue = form.getFieldValue([row.id, 'RowSelection']);
        if (rowSelectionValue === true) {
          selectedIndexes.push(index);
        }
      });

      if (selectedIndexes.length > 0) {
        // 从后往前删除，避免索引变化问题
        const sortedIndexes = selectedIndexes.sort((a, b) => b - a);
        let newTableData = [...tableData];

        sortedIndexes.forEach((index) => {
          newTableData.splice(index, 1);
        });

        // TODO 设定主诊为第一个
        if (icdeOperFirstMain && newTableData.length > 0) {
          setFirstItemIsMainOper(newTableData);
        }

        // 更新form
        props?.form?.setFieldValue(
          'operation-insur-table',
          cloneDeep(newTableData),
        );
        triggerFormValueChangeEvent('operation-insur-table');
        setCurrentTime(Date.now());
        // 延迟更新选择状态
        setTimeout(() => {
          updateRowSelectionState();
        }, 100);
      }
    });

    Emitter.on(getArrowUpDownEventKey('operationInsurTable'), (payload) => {
      let type = payload?.type;
      const icdeDataSource = props?.form?.getFieldValue(
        'operation-insur-table',
      );
      console.log('payload', payload);
      if (
        payload?.event?.target?.className?.indexOf('ant-select') > -1 &&
        payload?.trigger === 'hotkey'
      ) {
        // 表示是 下拉框 需要定制
        return;
      }

      payload?.event?.stopPropagation();

      let { nextIndex, activePaths } = calculateNextIndex(type);
      if (type === 'UP') {
        if (nextIndex < 0) {
          nextIndex = undefined;
        }
      }

      if (type === 'DOWN') {
        if (nextIndex > icdeDataSource?.length - 1) {
          nextIndex = undefined;
        }
      }

      if (nextIndex !== undefined) {
        activePaths[2] = nextIndex.toString();
        document.getElementById(activePaths?.join('#'))?.focus();
      }
    });

    let resizeObserver = null;
    if (itemRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Do what you want to do when the size of the element changes
        resizeContentById('operationInsurTable');
      });
      resizeObserver.observe(itemRef.current);
    }

    return () => {
      Emitter.off(getDeletePressEventKey('operationInsurTable'));
      Emitter.off(getArrowUpDownEventKey('operationInsurTable'));
      Emitter.off(EventConstant.DMR_OPER_INSUR_ADD);
      Emitter.off(EventConstant.DMR_OPER_INSUR_DELETE);
      Emitter.off(EventConstant.DMR_OPER_INSUR_COPY);
      Emitter.off(`DMR_ROW_SELECTION_SELECT_ALL_operationInsurTable`);
      Emitter.off(`DMR_ROW_SELECTION_ITEM_CHANGE_operationInsurTable`);
      Emitter.off(`DMR_BATCH_DELETE_operationInsurTable`);
    };
  }, [insurSeparateTableLogic?.canEditInsurTable]);

  const onInsurItemDelete = (index: number) => {
    if (index > -1) {
      const canEditInsurTable =
        insurSeparateTableLogic?.canEditInsurTable ?? true;
      if (!canEditInsurTable) return;
      let tableData = props?.form?.getFieldValue('operation-insur-table');
      tableData.splice(index, 1);
      if (icdeOperFirstMain) {
        setFirstItemIsMainOper(tableData);
      }
      props?.form?.setFieldValue('operation-insur-table', cloneDeep(tableData));
      triggerFormValueChangeEvent('operation-insur-table');
      let dataItems = tableData?.filter((item) => item?.id !== 'ADD');
      if (dataItems?.length > 0) {
        setWaitFocusId(
          `div[id=operationInsurTable] tbody > tr:nth-child(${
            index >= dataItems.length - 1 ? dataItems.length : index + 1
          }) > td input`,
        );
      }
      setCurrentTime(Date.now());
    }
  };

  const columnsProcessor = (columns) => {
    return columns.map((columnItem) => {
      if (!columnItem.onCell) {
        columnItem.onCell = (record, index) => {
          if (record?.id === 'ADD') {
            return {
              colSpan: 0,
            };
          }

          return {};
        };

        if (columnItem.children) {
          columnItem.children = columnsProcessor(columnItem.children);
        }
      }

      return columnItem;
    });
  };

  return (
    <UniDmrDragEditOnlyTable
      {...props}
      dmrTableContainerRef={operInsurTableRef}
      formItemContainerClassName={'form-content-item-container'}
      itemRef={mergeRefs([itemRef, ...hotKeyRefs])}
      form={form}
      key={props?.id}
      id={props?.id}
      tableId={props?.id}
      formKey={'operationInsurTable'}
      tableLayout={'auto'}
      forceColumnsUpdate={props?.underConfiguration ?? false}
      scroll={{ x: 'max-content' }}
      pagination={false}
      className={`table-container ${props?.className || ''}`}
      columns={tableColumns || []}
      theadStyle={{ height: '59px' }}
      dataSource={(() => {
        const canEditInsurTable =
          insurSeparateTableLogic?.canEditInsurTable ?? true;
        const baseDataSource = (
          props?.form?.getFieldValue('operation-insur-table') ?? []
        )
          ?.filter((item) => item?.id !== 'ADD')
          ?.map((item) => {
            if (!item['id']) {
              item['id'] = generateUniqueNumberId();
            }
            return item;
          });
        console.log('dataSource operation-insur-table', baseDataSource);
        // 只有有编辑权限的用户才显示ADD行
        return canEditInsurTable
          ? baseDataSource?.concat({ id: 'ADD' })
          : baseDataSource;
      })()}
      rowKey={'id'}
      onValuesChange={(recordList, changedValues) => {
        const canEditInsurTable =
          insurSeparateTableLogic?.canEditInsurTable ?? true;
        if (!canEditInsurTable) return;
        props?.form?.setFieldValue('operation-insur-table', recordList);
        triggerFormValueChangeEvent('operation-insur-table');
      }}
      onTableDataSourceOrderChange={(tableData, dataOrder, focusId) => {
        const canEditInsurTable =
          insurSeparateTableLogic?.canEditInsurTable ?? true;
        if (!canEditInsurTable) return;
        props?.form?.setFieldValue(
          'operation-insur-table',
          cloneDeep(tableData),
        );
        if (focusId) {
          setTimeout(() => {
            waitFocusElementRefocus(focusId);
          }, 100);
        }
        triggerFormValueChangeEvent('operation-insur-table');
      }}
      enableRowSelection={true}
      allowDragging={insurSeparateTableLogic?.canEditInsurTable ?? true}
      canRowSortable={() => insurSeparateTableLogic?.canEditInsurTable ?? true}
    />
  );
};

export default React.memo(OperInsurTable);
