import {
  PlusOutlined,
  DeleteOutlined,
  AlertOutlined,
  MenuOutlined,
  PlusCircleTwoTone,
  InfoCircleTwoTone,
} from '@ant-design/icons';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import IcdeSelect from '@/pages/dmr/components/icde-select';
import {
  InsurOnlyIcdeExtraMap,
  IcdeExtraTagsItem,
  IcdeOperationInputSelector,
  IcdeOperationReadonlyItem,
} from '@uni/grid/src/components/icde-oper-input/input';
import { RowSelectionCheckbox } from '@uni/grid/src/components/row-selection';
import { RowSelectionHeader } from '@uni/grid/src/components/row-selection-header';
import { BatchDeleteButton } from '@/pages/dmr/components/batch-delete-button';
import IconBtn from '@uni/components/src/iconBtn';
import { extraTitle, DragHandler, nonAddCell } from '../../../columns';
import { noNeedReadOnlyColumns, readOnlyTextCenterColumns } from './constants';
import _ from 'lodash';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['dmr']?.tableOnlyAddIconTrigger ?? false;

const enableTableDropdownNG =
  (window as any).externalConfig?.['dmr']?.enableTableDropdownNG ?? false;

const icdeOperRowSelection =
  (window as any).externalConfig?.['dmr']?.icdeOperRowSelection ?? false;

// 医保表格专用的列定义（只包含医保相关列和关联显示列）
const icdeInsurColumnsBase = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'IcdeExtra',
    title: extraTitle(
      _.pickBy(InsurOnlyIcdeExtraMap, (item) => item.isInsur === true),
      {
        IsObsolete: {
          prompt: '医保置灰',
        },
      },
    ),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    renderColumnFormItem: (node, record, index) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'icde-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_ICDE_INSUR_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-diagnosisInsurTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_ICDE_INSUR_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      }

      return (
        <IcdeExtraTagsItem
          value={record?.['IcdeExtra']}
          nameKey={'IcdeExtra'}
          extraMap={_.pickBy(
            InsurOnlyIcdeExtraMap,
            (item) => item.isInsur === true,
          )}
        />
      );
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          colSpan: 14,
        };
      }

      return {};
    },
  },
  // {
  //   key: 'rowSelection',
  //   dataIndex: 'RowSelection',
  //   title: (
  //     <RowSelectionHeader
  //       tableId="diagnosisInsurTable"
  //       onSelectAll={(checked) => {
  //         console.log('全选/反选:', checked);
  //       }}
  //     />
  //   ),
  //   visible: icdeOperRowSelection || false,
  //   align: 'center',
  //   width: 44,
  //   fixed: 'left',
  //   readonly: false,
  //   renderColumnFormItem: (node, record, index, dataIndex) => {
  //     return (
  //       <RowSelectionCheckbox
  //         id={`formItem#RowSelection#${index}#IcdeInsurTable`}
  //         recordId={record?.id}
  //         dataIndex={dataIndex}
  //         onChangeExtra={(checked) => {
  //           console.log('asddsadadasdacheckbox');
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  {
    key: 'sort',
    dataIndex: 'IcdeSort',
    title: '序',
    visible: true,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      let labelNode = null;
      if (index === 0) {
        labelNode = <span>主</span>;
      } else {
        // labelNode = <span>{`次要诊断${index}`}</span>;
        labelNode = <span style={{ whiteSpace: 'nowrap' }}>{`${index}`}</span>;
      }
      if (record?.id !== 'ADD') {
        const SortDragHandler = DragHandler(labelNode);
        return <SortDragHandler />;
      }
    },
    onCell: nonAddCell,
  },
  // {
  //   dataIndex: 'IsMain',
  //   title: '医保主诊',
  //   visible: true,
  //   width: 50,
  //   align: 'center',
  //   renderColumnFormItem: (node, record, index, dataIndex) => {
  //     return (
  //       <IcdeOperCheckbox
  //         id={`formItem#IsMain#${index}#IcdeInsurTable`}
  //         recordId={record?.id}
  //         dataIndex={dataIndex}
  //         onChangeExtra={(checked) => {
  //           Emitter.emit(EventConstant.DMR_ICDE_INSURE_MAIN, {
  //             id: record?.id,
  //             values: {
  //               IsMain: checked,
  //             },
  //             index: index,
  //           });
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  // {
  //   dataIndex: 'IsReported',
  //   title: '医保上报',
  //   visible: true,
  //   width: 50,
  //   align: 'center',
  //   renderColumnFormItem: (node, record, index, dataIndex, form) => {
  //     return (
  //       <IcdeOperCheckbox
  //         id={`formItem#IsReported#${index}#IcdeInsurTable`}
  //         recordId={record['id']}
  //         dataIndex={dataIndex}
  //         dependencyKey={'IsMain'}
  //         dependencyValue={true}
  //         form={form}
  //         minimumChecked={1}
  //         onChangeExtra={(checked) => {
  //           Emitter.emit(EventConstant.DMR_ICDE_REPORT, checked);
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  {
    dataIndex: 'InsurCode',
    title: '医保编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeSelect
          tableId={'diagnosisInsurTable'}
          componentId={`InsurIcdeCode#${index}`}
          interfaceUrl={'Api/Insur/InsurSearch/Icde'}
          paramKey={'OutHospital'}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          icdeSelectType={'IsDscg'}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
            IcdeExtra: 'IcdeExtra',
          }}
          listHeight={200}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG
              ? {
                  points: ['tl', 'bl'], // 下拉对齐到输入框的左上角 (tl) → 下拉的左下角 (bl)
                  offset: [-124, 4], // y 方向下移 4px，避免顶到输入框
                  overflow: {
                    adjustY: false, // 纵向溢出时自动反向
                  },
                }
              : undefined
          }
        />
        // <IcdeOperationReadonlyItem />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurName',
    title: '医保名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: true,
    align: 'center',
    width: 100,
    conditionDictionaryKey: 'RYBQ',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'diagnosisInsurTable'}
          className={'in-hospital-diagnosis icde-table-item'}
          dataIndex={'IcdeCond'}
          index={index}
          conditionDictionaryKey={'RYBQ'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'operation',
    title: icdeOperRowSelection ? (
      <BatchDeleteButton tableId="diagnosisInsurTable" />
    ) : (
      ''
    ),
    visible: true,
    align: 'center',
    width: 100,
    readonly: true,
    disableComment: true,
    render: (node, record, index, action) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_INSUR_COPY, {
                UniqueId: record?.UniqueId,
                index: index,
              });
            }}
          />
          <IconBtn
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_ICDE_INSUR_DELETE, index);
            }}
          />
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

// 兼容：保留原导出名，指向基础列
export const icdeInsurColumns = icdeInsurColumnsBase;

export const getIcdeInsurColumns = (insurSeparateTableLogic?: {
  canEditInsurTable?: boolean;
}) => {
  const canEditInsurTable = insurSeparateTableLogic?.canEditInsurTable ?? true;

  const processed = (icdeInsurColumnsBase as any[])
    .map((col) => {
      if (!canEditInsurTable) {
        if (
          noNeedReadOnlyColumns?.findIndex((d) => d === col?.dataIndex) === -1
        ) {
          const base: any = { ...col, readonly: true };
          return {
            ...base,
            renderColumnFormItem: (
              node: any,
              record: any,
              index: number,
              dataIndex: string,
              form: any,
              extraItem: any,
            ) => (
              <IcdeOperationReadonlyItem
                conditionDictionaryKey={base?.conditionDictionaryKey}
                conditionDictionaryGroup={base?.conditionDictionaryGroup}
                extraItem={extraItem}
                style={
                  readOnlyTextCenterColumns?.findIndex(
                    (d) => d === col?.dataIndex,
                  ) !== -1
                    ? { justifyContent: 'center' }
                    : {}
                }
              />
            ),
          };
        }
      }
      return col;
    })
    .filter((c) => !(c?.dataIndex === 'operation' && !canEditInsurTable));

  // 当无编辑权限时，禁用“序”列的拖拽手柄，仅显示标签
  const updated = processed.map((col: any) => {
    if (col?.key === 'sort' || col?.dataIndex === 'IcdeSort') {
      if (!canEditInsurTable) {
        return {
          ...col,
          renderColumnFormItem: (node: any, record: any, index: number) => {
            const labelNode =
              index === 0 ? (
                <span>主</span>
              ) : (
                <span style={{ whiteSpace: 'nowrap' }}>{`${index}`}</span>
              );
            // 无权限时不包裹 DragHandler，避免可拖拽
            return labelNode;
          },
        };
      }
    }
    return col;
  });

  return updated;
};
