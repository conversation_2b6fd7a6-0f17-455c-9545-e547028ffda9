import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { Input, Form, Select } from 'antd';
// @ts-ignore
import { useModel } from '@@/plugin-model/useModel';
import { UniAntdSelect, UniSelect } from '@uni/components/src';
import { triggerFormValueChangeEvent } from '../../utils';
import UniDmrSelect from '../dmr-select/UniDmrSelect';
import { isEmptyValues } from '@uni/utils/src/utils';

const { Option } = UniAntdSelect;

export interface SuffixItemProps {
  formItemId?: string;
  className?: string;
  style?: React.CSSProperties;
  label: string;
  value?: string;
  showInput?: boolean;
  showTags?: boolean;
  formKey?: string;

  disableCondition?: (value: any) => boolean;

  conditionKey?: string;

  modelDataKey?: string;
  modelDataGroup?: string;
}

export const SuffixItem = (props: SuffixItemProps) => {
  const [selectDataSource, setSelectDataSource] = useState([]);

  const [suffixSelectStatus, setSuffixSelectStatus] = useState(false);

  const form = Form.useFormInstance();

  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateFromMaster',
  );

  const dictData = props?.modelDataGroup
    ? globalState?.dictData?.[props?.modelDataGroup]
    : globalState?.dictData;

  let conditionValue = undefined;
  if (props?.conditionKey) {
    conditionValue = Form.useWatch(props?.conditionKey, form);
  }

  useEffect(() => {
    if (props?.modelDataKey) {
      if (dictData?.[props?.modelDataKey]) {
        setSelectDataSource(dictData[props?.modelDataKey]);
      }
    }
  }, [dictData]);

  const suffixItemValue = Form.useWatch(props?.formKey) as string;

  return (
    <div
      style={props?.style || {}}
      className={`suffix-item-container ${props?.className || ''} ${
        props?.showInput || props?.showTags ? 'suffix-item-width-100' : ''
      }`}
    >
      <label className={'label'}>{props?.label}</label>
      {props?.showInput && (
        <Form.Item
          name={props?.formKey}
          className={'form-content-item-container'}
        >
          <Input
            id={props?.formItemId}
            className="suffix-input"
            bordered={false}
            value={suffixItemValue ?? ''}
            contentEditable={true}
            disabled={
              props?.disableCondition
                ? props?.disableCondition(conditionValue)
                : false
            }
            onChange={(event) => {
              form.setFieldValue(props?.formKey, event?.target?.value);
            }}
          />
        </Form.Item>
      )}
      {props?.showTags && (
        <div
          className={'suffix-item-tags-container form-content-item-container'}
        >
          <Form.Item name={props?.formKey} hidden={true} />
          <UniDmrSelect
            id={props?.formKey}
            formItemId={`${props?.formItemId}#DmrSelect`}
            showArrow={false}
            showAction={['click']}
            className="suffix-input"
            bordered={false}
            value={suffixItemValue?.replace('，', ',')?.split(',') ?? ''}
            placeholder={'请选择过敏药物'}
            mode={'multiple'}
            tokenSeparators={[',']}
            dataSource={selectDataSource}
            optionNameKey={'Name'}
            optionValueKey={'Name'}
            optionTitleKey={'Name'}
            maxTagCount={'responsive'}
            enablePinyinSearch={true}
            open={suffixSelectStatus}
            disabled={
              props?.disableCondition
                ? props?.disableCondition(conditionValue)
                : false
            }
            onDropdownVisibleChange={(open: boolean) => {
              setSuffixSelectStatus(open);
            }}
            onKeyDown={(event: any) => {
              if (event?.key === 'Enter' && suffixSelectStatus === true) {
                event?.stopPropagation();
              }

              if (event?.key === 'Esc' && suffixSelectStatus === true) {
                event?.preventDefault();
                event?.stopPropagation();
                setSuffixSelectStatus(false);
              }
            }}
            onChange={(event) => {
              if (Array.isArray(event)) {
                form.setFieldValue(
                  props?.formKey,
                  event?.filter((value) => !isEmptyValues(value)).join(','),
                );
              } else {
                form.setFieldValue(props?.formKey, event);
              }
            }}
          />
        </div>
      )}
    </div>
  );
};
