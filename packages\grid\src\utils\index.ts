import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { isNil } from 'lodash';
import { forceClickIds, ROW_HEIGHT } from '../common';

const readonlyInputClickSelectAll =
  (window as any).externalConfig?.['dmr']?.readonlyInputClickSelectAll ?? false;

export const numberInputRestrictKeyDown = (
  event,
  extraAllowKeys?: string[],
) => {
  // 目前仅接受 0-9
  // extraKeys可以接受  .+-等其他操作
  let allowedKeys = [
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '0',
    'ArrowLeft',
    'ArrowRight',
    'Shift',
    'Control',
    'Enter',
    'Backspace',
    'Tab',
    'Alt',
  ]?.concat(extraAllowKeys ?? []);
  let currentEventKey = event?.key;

  if (
    event?.ctrlKey === true ||
    event?.altKey === true ||
    event?.shiftKey === true ||
    event?.metaKey === true
  ) {
    return;
  }

  if (!allowedKeys?.includes(currentEventKey)) {
    event?.preventDefault();
  }
};

// TODO DMR替换掉
export const getDeletePressEventKey = (formKey) => {
  return `${EventConstant.DELETE_PRESS}#${formKey}`;
};

export const getArrowUpDownEventKey = (formKey) => {
  return `${EventConstant.ARROW_UP_DOWN_PRESS}#${formKey}`;
};

export const getSelectorDropdownContainerNode = (parentNodeId?: string) => {
  return document?.getElementById(parentNodeId ?? 'dmr-content-container');
  // return document?.getElementById(parentNodeId ?? 'dmr-form-container');
};

export const triggerFormValueChangeEvent = (containerId?: string) => {
  Emitter.emit(EventConstant.FORM_VALUE_CHANGE, containerId);
};

export const defaultPageUpDownHandler = (up: boolean) => {
  let maxScrollHeight =
    document.getElementById('dmr-content-container')?.scrollHeight -
    document.getElementById('dmr-content-container')?.offsetHeight;
  let scrollHeight = ROW_HEIGHT * 2;
  let currentScrollHeight = document.getElementById(
    'dmr-content-container',
  )?.scrollTop;
  if (!isNil(currentScrollHeight)) {
    if (up) {
      currentScrollHeight -= scrollHeight;
    } else {
      currentScrollHeight += scrollHeight;
    }
  }

  if (currentScrollHeight > maxScrollHeight) {
    currentScrollHeight = maxScrollHeight;
  }

  if (currentScrollHeight < 0) {
    currentScrollHeight = 0;
  }

  document.getElementById('dmr-content-container')?.scrollTo({
    top: currentScrollHeight,
    behavior: 'smooth',
  });
};

export const calculateNextIndex = (type) => {
  let nextIndex = undefined;
  let activePaths = [];
  if (document.activeElement) {
    let currentActiveId = document.activeElement?.id;
    if (currentActiveId) {
      activePaths = currentActiveId?.split('#');
      let activeIndex = activePaths?.at(2);
      if (activeIndex !== undefined) {
        if (type === 'UP') {
          nextIndex = parseInt(activeIndex) - 1;
        }

        if (type === 'DOWN') {
          nextIndex = parseInt(activeIndex) + 1;
        }
      }
    }
  }

  return {
    nextIndex: nextIndex,
    activePaths: activePaths,
  };
};

export const waitFocusElementRefocus = (
  waitFocusId?: string,
  reset?: (value) => void,
  needReset?: boolean,
) => {
  if (waitFocusId) {
    let waitForFocusElement = document?.getElementById(waitFocusId);
    console.log('waitFocusElementRefocus', waitFocusId, waitForFocusElement);
    if (forceClickIds?.includes(waitFocusId)) {
      waitForFocusElement?.click();
    }

    waitForFocusElement?.focus();

    if (needReset) {
      reset && reset(undefined);
    }
  }
};

export const waitFocusElementRefocusBySelector = (
  waitFocusSelector?: string,
  reset?: (value) => void,
  needReset?: boolean,
) => {
  if (waitFocusSelector) {
    let waitForFocusElement = document?.querySelector(waitFocusSelector) as any;

    if (waitForFocusElement) {
      if (forceClickIds?.includes(waitForFocusElement?.id)) {
        waitForFocusElement?.click();
      }

      waitForFocusElement?.focus();

      if (needReset) {
        reset && reset(undefined);
      }
    }
  }
};

export const getTransformHeightByElementId = (elementId: string) => {
  let currentRowItemElement = document.getElementById(elementId);

  if (currentRowItemElement) {
    return currentRowItemElement?.offsetTop;
  }

  return null;
};

export const onReadonlyInputAndReadonlyItemClick = (event: any) => {
  if (readonlyInputClickSelectAll) {
    if (event?.target?.tagName?.toLowerCase() === 'input') {
      if (event?.target?.readOnly === true) {
        let selectionStart = event?.target?.selectionStart;
        let selectionEnd = event?.target?.selectionEnd;
        let textLength = event?.target?.selectionStart;

        if (selectionStart === selectionEnd) {
          event?.target?.select && event?.target?.select();
        }
      }
    }

    if (event?.target?.tagName?.toLowerCase() === 'span') {
      let selection, range;
      if (window.getSelection && document.createRange) {
        selection = window.getSelection();
        range = document.createRange();
        range.selectNodeContents(event?.target);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }
};
