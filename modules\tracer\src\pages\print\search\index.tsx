import { Reducer, useEffect, useMemo, useReducer } from 'react';
import { Dispatch, useDispatch, useRequest, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Divider,
  Modal,
  Popconfirm,
  Row,
  Space,
  TableProps,
  Tooltip,
  message,
} from 'antd';
import { useAsyncEffect, useDebounce, useSafeState } from 'ahooks';
import {
  IReducer,
  ITableState,
  IModalState,
} from '@uni/reducers/src/Interface';
import { SwagPrintRecordItem } from '../../interface';
import { columnsHandler, isRespErr, sortingHandler } from '@/utils/widgets';
import _ from 'lodash';
import { ReqActionType } from '@/Constants';
import {
  InitModalState,
  ModalAction,
  TableAction,
  modalReducer,
  tableReducer,
  InitTableState,
} from '@uni/reducers/src';
import { SorterResult } from 'antd/lib/table/interface';
import PatTimeline from '@/components/PatTimeline';
import { useTimelineReq } from '@/hooks';
import { PictureOutlined, UndoOutlined } from '@ant-design/icons';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import dayjs from 'dayjs';
import { ITableReq } from '@/Interface';
import ImageGallery from 'react-image-gallery';
import GalleryItem from '@/pages/print/register/components/GalleryItem';
import { v4 as uuidv4 } from 'uuid';
import { uniBatchBlobService } from '@uni/services/src/commonService';
import { pickOnlyNeedKeys } from '@uni/utils/src/search-context';

const PrintTraceRecordList = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster'); // qiankun 的主传子，子的获取方式
  const dispatch: Dispatch = useDispatch();
  const columnsList = useSelector((state) => state.global.columns);
  const loadings = useSelector((state) => state.global.loadings);

  const [SearchTable, SearchTableDispatch] = useReducer<
    Reducer<ITableState<SwagPrintRecordItem>, IReducer>
  >(tableReducer, InitTableState);

  const [modalState, modalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, InitModalState);

  // timeline req
  const [timelineItems, { setParams }] = useTimelineReq();

  const [backPagination, setBackPagination] = useSafeState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    hideOnSinglePage: false,
  });

  // 修改签收日期
  const [updateSignInDate, setUpdateSignInDate] = useSafeState(dayjs());

  // 记录下点击的行
  const [clkedRecord, setClkedRecord] = useSafeState(null);
  // 附件图片modal
  const [imagePreviewVisible, setImagePreviewVisible] = useSafeState({
    visible: false,
    records: [],
  });

  // 附件获取
  const {
    data: imageData,
    loading: imageDataLoading,
    run: imageDataReq,
    fetches,
  } = useRequest(
    (id) =>
      uniBatchBlobService('Api/Sys/BlobFileMetadata/DownloadBlobFile', {
        method: 'GET',
        responseType: 'blob',
        params: { BlobId: id },
      }),
    {
      manual: true,
      fetchKey: (id) => id,
      formatResult: (res) => {
        return res;
      }, // 这边不需要response,所以只要data
      // onSuccess: async (data: any, params: any) => {
      //   console.log(data)
      //   const resBlob = await data.blob();
      //   console.log(resBlob)
      //   setBlobList([...blobList, resBlob]);
      //   // console.log(resBlob);
      // },
    },
  );

  // const debounceFetches = useDebounce(fetches, { wait: 100 });

  useEffect(() => {
    // 监听 fetches 的变化
    const allFetches = Object.values(fetches);
    console.log('allFetches', allFetches, clkedRecord);
    // 检查是否所有请求都已完成
    if (
      clkedRecord &&
      allFetches.length > 0 &&
      allFetches.every((fetch) => !fetch?.loading)
    ) {
      const blobs = allFetches
        .filter(
          (fetch: any) =>
            fetch?.data?.result?.type?.includes('image') &&
            clkedRecord?.ApplicationBlobIdList?.findIndex(
              (id) => id === fetch?.data?.blodId,
            ) !== -1,
        )
        .map((fetch: any) => {
          return {
            uid: uuidv4(),
            original: URL.createObjectURL(fetch?.data?.result as any as Blob),
            thumbnail: window.URL.createObjectURL(
              fetch?.data?.result as any as Blob,
            ),
          };
        });

      if (blobs?.length > 0) {
        setImagePreviewVisible({
          visible: true,
          records: blobs,
        });
      } else if (clkedRecord?.ApplicationBlobIdList?.length > 0) {
        message.error('图片获取失败，请联系管理员');
      }
      // console.log(allFetches, blobs);
    }
  }, [fetches, clkedRecord]);

  const backTableOnChange: TableProps<any>['onChange'] = async (
    pagi,
    filter,
    sorter,
    extra,
  ) => {
    tableReq(
      searchParams,
      pagi.current,
      pagi.pageSize,
      sorter as SorterResult<SwagPrintRecordItem>,
    );
  };

  const fetchParamsHandler = (
    params: any,
    cur = undefined,
    size = undefined,
    sorter = SearchTable.sorter,
  ) => {
    return {
      ..._.pick(searchParams, [
        'Sdate',
        'Edate',
        // 'hospCode',
        'PrintApplicationStatus',
      ]),
      SearchableText: params?.searchKeyword,
      // 自定义headerParams
      ...pickOnlyNeedKeys(params, true),
      current: cur ?? undefined,
      pageSize: size ?? undefined,
      sorting: sortingHandler(sorter),
    };
  };

  // 普通的tableReq
  const tableReq = async (
    params,
    cur = 1,
    size = 10,
    sorter = SearchTable.sorter,
  ) => {
    let res: ITableReq | string = await dispatch({
      type: 'global/fetchTableDataLatest',
      payload: {
        name: 'TraceRecord',
        requestParams: [
          {
            url: 'Api/Mr/PrintRecord/GetList',
            method: 'POST',
            data: fetchParamsHandler(params, cur, size, sorter),
            dataType: 'mr',
          },
        ],
      },
    });
    if (typeof res !== 'string') {
      let total = res?.datas[0]?.total;
      SearchTableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: res?.datas[0]?.data ?? [],
        },
      });

      // sorter
      if (!_.isEqual(sorter, SearchTable.sorter)) {
        SearchTableDispatch({
          type: TableAction.sortChange,
          payload: { sorter },
        });
      }

      setBackPagination({
        ...backPagination,
        current: cur,
        pageSize: size,
        total: total ?? 0,
      });
    }
  };

  const reqActionReq = async (
    params: any,
    reqType: ReqActionType,
    selfSearchParams = null,
  ) => {
    if (!params || !reqType) return;
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `Tracing/${reqType}`,
        requestParams: {
          url: `Api/Mr/Tracing/${reqType}`,
          method: 'POST',
          data: params,
          dataType: 'mr',
          requestType: 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      // 批量撤销时
      if (reqType === ReqActionType.mrRoomRevertSignIn) {
        setSelectedRecords([]);
      }
      message.success('操作成功');
      if (modalState.visible) {
        modalStateDispatch({
          type: 'init',
        });
      }
      timeLineReset();

      tableReq(
        selfSearchParams ? selfSearchParams : searchParams,
        backPagination.current,
        backPagination.pageSize,
      );
    }
  };

  // columns 处理，主要用于处理options
  //   const columnsSolver = useMemo(() => {
  //     return SearchTable.columns
  //       ?
  //       : [];
  //   }, [SearchTable.columns, searchParams, backPagination]);

  useEffect(() => {
    tableReq(searchParams);
    timeLineReset();
  }, [searchParams]);

  const timeLineReset = () => {
    SearchTableDispatch({
      type: TableAction.clkChange,
      payload: {
        clkItem: null,
      },
    });
    setParams(null);
  };

  // columns
  const { loading: columnsReqLoading, run: columnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Mr/PrintRecord/GetList', {
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          return;
        }
      },
    },
  );

  // columns处理
  useEffect(() => {
    if (
      columnsList?.['PrintTraceRecordList'] &&
      SearchTable.columns.length < 1
    ) {
      console.log();
      SearchTableDispatch({
        type: TableAction.columnsChange,
        payload: {
          columns: tableColumnBaseProcessor(
            [],
            columnsList['PrintTraceRecordList'],
          ),
        },
      });
    }
  }, [columnsList?.['PrintTraceRecordList'], SearchTable.columns]);

  // 批量 action
  const { loading: selectAllReqLoading, run: selectAllReq } = useRequest(
    (params, length) => {
      return uniCommonService('Api/Mr/TraceRecord/MrRoomSignedInList', {
        method: 'POST',
        data: {
          ...fetchParamsHandler(params),
          MaxResultCount: length,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0) {
          return response?.data?.Items;
        } else {
          return [];
        }
      },
    },
  );

  const [selectedRecords, setSelectedRecords] = useSafeState([]);
  const rowSelection = {
    type: 'checkbox',
    columnWidth: 50,
    preserveSelectedRowKeys: true,
    selectedRowKeys: selectedRecords?.map((d) => d.RecordId),
    onChange: (selectedRowKeys: number[], selectedRows: any[]) => {
      setSelectedRecords(selectedRows);
    },
    selections: [
      {
        key: 'backendSelectAll',
        text: '全选当前所有',
        onSelect: async () => {
          let result: any = await selectAllReq(
            searchParams,
            backPagination?.total,
          );
          if (result?.length) {
            setSelectedRecords(_.union(selectedRecords, result));
          } else {
            console.error('failed');
          }
        },
      },
      {
        key: 'backendClearAll',
        text: '清空当前所有',
        onSelect: async () => {
          let result: any = await selectAllReq(
            searchParams,
            backPagination?.total,
          );
          if (result?.length) {
            console.log(
              selectedRecords,
              result,
              _.difference(selectedRecords, result),
            );
            setSelectedRecords(
              _.differenceBy(selectedRecords, result, 'RecordId'),
            );
          } else {
            console.error('failed');
          }
        },
      },
    ],
  };

  return (
    <>
      <Card
        title="复印记录查询"
        extra={
          <Space>
            <Divider type="vertical" />
            <ExportIconBtn
              isBackend={true}
              backendObj={{
                url: 'Api/Mr/PrintRecord/ExportGetList',
                method: 'POST',
                data: {
                  ..._.pick(searchParams, [
                    'Sdate',
                    'Edate',
                    'hospCode',
                    'SearchableText',
                    'PrintApplicationStatus',
                    'PrintSdate',
                    'PrintEdate',
                  ]),
                  MaxResultCount: 999999,
                },
                fileName: '复印记录',
              }}
              btnDisabled={SearchTable.data?.length < 1}
            />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Mr/PrintRecord/GetList',
                onTableRowSaveSuccess: (columns) => {
                  // 这个columns 存到dva
                  dispatch({
                    type: 'global/saveColumns',
                    payload: {
                      name: 'PrintTraceRecordList',
                      value: columns,
                    },
                  });
                  SearchTableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor([], columns),
                    },
                  });
                },
              }}
            />
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <UniTable
              id="trace_record"
              rowKey="Id"
              showSorterTooltip={false}
              loading={
                loadings['TraceRecord'] ||
                selectAllReqLoading ||
                imageDataLoading ||
                false
              }
              columns={columnsHandler(SearchTable.columns || [], {
                title: '操作',
                dataIndex: 'operation',
                width: 60,
                align: 'center',
                fixed: 'right',
                visible: true,
                render: (text, record) => (
                  <Space>
                    <Tooltip title="查看证件附件">
                      <PictureOutlined
                        disabled={record?.ApplicationBlobIdList?.length < 1}
                        style={{
                          color:
                            record?.ApplicationBlobIdList?.length < 1
                              ? '#ccc'
                              : '#1890ff',
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setClkedRecord(record);
                          record.ApplicationBlobIdList.map((d, i) => {
                            imageDataReq(d);
                          });
                          // setImagePreviewVisible({
                          //   visible: true,
                          //   records: record?.ApplicationBlobIdList,
                          // });
                        }}
                      />
                    </Tooltip>
                  </Space>
                ),
              })} // columnsHandler
              dictionaryData={dictData}
              dataSource={SearchTable.data}
              pagination={backPagination}
              onChange={backTableOnChange}
              forceColumnsUpdate
              scroll={{ x: 'max-content' }}
              // rowClassName={(record) => {
              //   if (record?.Id === SearchTable.clkItem?.Id)
              //     return 'row-selected';
              //   return null;
              // }}
              // onRow={(record) => {
              //   return {
              //     onClick: (event) => {
              //       if (SearchTable.clkItem?.Id !== record?.Id) {
              //         SearchTableDispatch({
              //           type: TableAction.clkChange,
              //           payload: {
              //             clkItem: record,
              //           },
              //         });
              //         setParams({ barCode: record.BarCode });
              //       }
              //     },
              //   };
              // }}
            />
          </Col>
          {/* <Col span={6}>
            <PatTimeline
              item={SearchTable?.clkItem}
              timelineItems={timelineItems}
              loading={loadings['TraceRecord/GetActions']}
            />
          </Col> */}
          <Modal
            width={1000}
            title="证件附件"
            open={imagePreviewVisible.visible}
            onCancel={() => {
              setClkedRecord(null);
              setImagePreviewVisible({
                visible: false,
                records: [],
              });
            }}
            okButtonProps={{ style: { display: 'none' } }}
            closable={false}
            cancelText="关闭"
            destroyOnClose
          >
            <ImageGallery
              items={imagePreviewVisible?.records?.filter(
                (record) => record?.original,
              )}
              showFullscreenButton={false}
              showPlayButton={false}
              renderItem={(item, index) => (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'end',
                  }}
                  key={index}
                >
                  <GalleryItem {...item} />
                </div>
              )}
            />
          </Modal>
        </Row>
      </Card>
    </>
  );
};

export default PrintTraceRecordList;
