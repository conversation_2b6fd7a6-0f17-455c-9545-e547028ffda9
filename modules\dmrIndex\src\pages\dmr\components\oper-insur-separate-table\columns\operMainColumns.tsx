import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  extraTitle,
  operationColumns,
  nonAdd<PERSON><PERSON>,
  DragHandler,
} from '@/pages/dmr/columns';
import {
  IcdeOperationInputSelector,
  IcdeOperationReadonlyItem,
  IcdeOperCheckbox,
  operationExtraMap,
  OperationExtraTagsItem,
  OperationFieldInput,
} from '@uni/grid/src/components/icde-oper-input/input';
import { noNeedReadOnlyColumns, readOnlyTextCenterColumns } from './constants';
import { PlusCircleTwoTone } from '@ant-design/icons';
import { RowSelectionHeader } from '@uni/grid/src/components/row-selection-header/index';
import { RowSelectionCheckbox } from '@uni/grid/src/components/row-selection/index';
import OperationSelect from '../../oper-select/index';
import DateSelect from '@uni/grid/src/components/date-select/index';
import RestrictInputNumber from '@uni/grid/src/components/restrict-number/index';
import { employeeDataSourceProcessor } from '@/pages/dmr/utils';
import { BatchDeleteButton } from '../../batch-delete-button/index';
import IconBtn from '@uni/components/src/iconBtn/index';
import _ from 'lodash';

const tableOnlyAddIconTrigger =
  (window as any).externalConfig?.['dmr']?.tableOnlyAddIconTrigger ?? false;
const enableTableDropdownNG =
  (window as any).externalConfig?.['dmr']?.enableTableDropdownNG ?? false;
const icdeOperRowSelection =
  (window as any).externalConfig?.['dmr']?.icdeOperRowSelection ?? false;

export const operationMainColumnsBase = [
  {
    key: 'CONTAINS_ADD',
    dataIndex: 'OperExtra',
    title: extraTitle(
      _.omitBy(operationExtraMap, (item) => item.isInsur === true),
      {
        InsurIsObsolete: {
          prompt: '医保置灰',
        },
        IsMicro: {
          prompt: '微创手术',
        },
        HqmsDegree: {
          prompt: '国考手术等级',
        },
        DrgsDegree: {
          prompt: 'DRG手术等级',
        },
      },
    ),
    fixed: 'left',
    visible: true,
    align: 'center',
    width: 60,
    readonly: true,
    renderColumnFormItem: (node, record, index, dataIndex, form) => {
      if (record?.id === 'ADD') {
        return (
          <div
            className={'operation-add-container'}
            style={
              tableOnlyAddIconTrigger === false ? {} : { cursor: 'initial' }
            }
            onClick={
              tableOnlyAddIconTrigger === false
                ? () => {
                    Emitter.emit(EventConstant.DMR_OPER_MAIN_ADD);
                  }
                : undefined
            }
          >
            <div
              itemid={`Table-operationMainTable-Missing`}
              className={'flex-row-center'}
              style={
                tableOnlyAddIconTrigger === true
                  ? { cursor: 'pointer', padding: '4px 8px' }
                  : { padding: '4px 8px' }
              }
              onClick={
                tableOnlyAddIconTrigger === true
                  ? () => {
                      Emitter.emit(EventConstant.DMR_OPER_MAIN_ADD);
                    }
                  : undefined
              }
            >
              <PlusCircleTwoTone />
              <span>新增</span>
            </div>
          </div>
        );
      } else {
        if (record?.id !== 'ADD') {
          return (
            <OperationExtraTagsItem
              record={record}
              uniqueId={record?.UniqueId}
              eventName={`${EventConstant.DMR_OPER_MAIN_SELECT_ADD}#${record?.id}`}
              nameKey={'OperExtra'}
              conditionDictionaryKey={'SSJB'}
              conditionDictionaryGroup={'Dmr'}
              form={form}
              extraMap={_.omitBy(
                operationExtraMap,
                (item) => item.isInsur === true,
              )}
            />
          );
        }
      }
    },
    onCell: (record, index) => {
      if (record?.id === 'ADD') {
        return {
          // colSpan: 19,
          colSpan: 18,
        };
      }

      return {};
    },
  },
  {
    key: 'rowSelection',
    dataIndex: 'RowSelection',
    title: (
      <RowSelectionHeader
        tableId="operationMainTable"
        onSelectAll={(checked) => {
          console.log('手术表全选/反选:', checked);
        }}
      />
    ),
    visible: icdeOperRowSelection || false,
    align: 'center',
    width: 44,
    fixed: 'left',
    readonly: false,
    renderColumnFormItem: (node, record, index, dataIndex) => {
      return (
        <RowSelectionCheckbox
          id={`formItem#RowSelection#${index}#OperMainTable`}
          recordId={record?.id}
          dataIndex={dataIndex}
          onChangeExtra={(checked) => {
            console.log('手术行选择:', checked);
          }}
        />
      );
    },
    onCell: nonAddCell,
  },
  {
    key: 'sort',
    dataIndex: 'OperSort',
    title: '序',
    visible: true,
    align: 'center',
    fixed: 'left',
    readonly: true,
    disableComment: true,
    width: 70,
    render: (node, record, index, action) => {
      let labelNode = (
        <span
          style={{ whiteSpace: 'nowrap' }}
          className={'operation-index'}
        >{`${index + 1}`}</span>
      );
      const SortDragHandler = DragHandler(labelNode);
      return <SortDragHandler />;
    },
  },
  {
    dataIndex: 'HqmsDegree',
    title: '国考手术级别',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'DegreeRemark',
    title: '国考手术级别条件',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'DrgsDegree',
    title: 'DRG手术级别',
    visible: false,
    readonly: true,
  },
  {
    dataIndex: 'OperGroupNo',
    title: '台次',
    visible: true,
    align: 'center',
    width: 100,
    disableComment: true,
    renderColumnFormItem: (node, record, index) => {
      return <OperationFieldInput dataIndex={'OperGroupNo'} index={index} />;
    },
  },
  {
    dataIndex: 'OperType',
    title: '手术类型',
    visible: true,
    align: 'center',
    width: 100,
    // fixed: 'left',
    readonly: true,
    conditionDictionaryKey: 'OperType',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      console.log('OperType render');
      return (
        <IcdeOperationReadonlyItem
          className={'dmr-oper-type'}
          conditionDictionaryKey={'OperType'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'OperCode',
    title: '手术及操作编码',
    visible: true,
    fixed: 'left',
    width: 140,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <OperationSelect
          columnType={'Dmr'}
          tableId={'operationMainTable'}
          componentId={`MainOperCode#${index}`}
          rowDataKey={record['id']}
          recordId={record['id']}
          dataIndex={dataIndex}
          form={form}
          parentId={'operation-table-content'}
          instantSelect={true}
          formKeys={{
            OperName: 'Name',
            OperCode: 'Code',
            InsurName: 'InsurName',
            InsurCode: 'InsurCode',
            HqmsName: 'HqmsName',
            HqmsCode: 'HqmsCode',
            OperRate: 'Degree',
            OperType: 'OperType',
            OperExtra: 'OperExtra',
            HqmsDegree: 'HqmsDegree',
            DegreeRemark: 'DegreeRemark',
            DrgsDegree: 'DrgsDegree',
            RowClassName: 'RowClassName',
          }}
          // getPopupContainer={(trigger) => {
          //   return trigger?.closest('table');
          // }}
          listHeight={200}
          // dropdownAlign={{ offset: [140, -30] }}
          onChangeValueProcessor={(fieldsValue) => {
            return fieldsValue[dataIndex];
          }}
          codeColumnWidth={extraItem?.width}
          numberSelectItem={enableTableDropdownNG}
          dropdownAlign={
            enableTableDropdownNG
              ? {
                  points: ['tl', 'bl'], // 下拉对齐到输入框的左上角 (tl) → 下拉的左下角 (bl)
                  offset: [-115, 4], // y 方向下移 4px，避免顶到输入框
                  overflow: {
                    adjustY: false, // 纵向溢出时自动反向
                  },
                }
              : undefined
          }
        />
      );
    },
  },
  {
    dataIndex: 'OperName',
    title: '手术及操作名称',
    visible: true,
    fixed: 'left',
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
  },
  {
    dataIndex: 'OprnOprtBegntime',
    title: '手术及操作日期',
    visible: true,
    align: 'center',
    width: 166,
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#OprnOprtBegntime#${index}#CompactInput`}
          formKey={'OprnOprtBegntime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'OprnConTime',
    title: '手术持续时间（小时）',
    visible: true,
    align: 'center',
    width: 100,
    renderColumnFormItem: (node, record, index) => {
      return (
        <RestrictInputNumber
          itemId={`formItem#OprnConTime#${index}#RestrictInputNumber`}
          min={0}
          max={1000}
          precious={2}
          step={0.01}
          formKey={'OprnConTime'}
        />
      );
    },
  },
  {
    dataIndex: 'OperRate',
    title: '手术级别',
    visible: true,
    align: 'center',
    width: 50,
    readonly: true,
    conditionDictionaryKey: 'SSJB',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationReadonlyItem
          conditionDictionaryKey={'SSJB'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
  },
  // {
  //   dataIndex: 'Operators',
  //   title: '手术及操作医师',
  //   visible: true,
  //   align: 'center',
  //   children: [
  //
  //   ],
  // },
  {
    dataIndex: 'Operator',
    title: '手术者',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      // return (
      //   <OperationFieldInput
      //     record={entity}
      //     dataIndex={'Operator'}
      //     index={index}
      //   />
      // )
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'Operator'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          numberSelectItem={enableTableDropdownNG}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'Firstasst',
    title: 'Ⅰ助',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'Firstasst'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          numberSelectItem={enableTableDropdownNG}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'Secondasst',
    title: 'Ⅱ助',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'Secondasst'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          numberSelectItem={enableTableDropdownNG}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'WoundHealingRateClass',
    title: '手术切口愈合等级',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'QKYHLB',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'WoundHealingRateClass'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'QKYHLB'}
          conditionDictionaryGroup={'Dmr'}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnaType',
    title: '麻醉方式',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'MZFS',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'AnaType'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'MZFS'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnstLvCode',
    title: '麻醉分级',
    visible: true,
    width: 80,
    align: 'center',
    conditionDictionaryKey: 'AnstLv',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'AnstLvCode'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'AnstLv'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'AnaDoc',
    title: '麻醉医师',
    visible: true,
    align: 'center',
    width: 80,
    conditionDictionaryKey: 'Employee',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'AnaDoc'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'Employee'}
          optionNameKey={'label'}
          optionValueKey={'Code'}
          optionTitleKey={'Name'}
          optionLabelProp={'title'}
          dataSourceProcessor={employeeDataSourceProcessor}
          leftOneAutoSelect={true}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'OprnPatnTypeCode',
    title: '手术类别',
    visible: true,
    width: 100,
    align: 'center',
    conditionDictionaryKey: 'OprnPatnType',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'OprnPatnTypeCode'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'OprnPatnType'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'OperNote',
    title: '手术操作描述',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index) => {
      return <OperationFieldInput dataIndex={'OperNote'} index={index} />;
    },
  },
  {
    dataIndex: 'OprnOprtEndtime',
    title: '手术截止日期',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#OprnOprtEndtime#${index}#CompactInput`}
          formKey={'OprnOprtEndtime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'AnstBegntime',
    title: '手术麻醉时间',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#AnstBegntime#${index}#CompactInput`}
          formKey={'AnstBegntime'}
          dataTableIndex={index}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'AnstEndtime',
    title: '麻醉结束时间',
    visible: true,
    width: 160,
    align: 'center',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <DateSelect
          className={'operation-item-container'}
          type={'compact'}
          formItemId={`formItem#AnstEndtime#${index}#CompactInput`}
          formKey={'AnstEndtime'}
          dataTableIndex={index}
          value={record['AnstEndtime']}
          bordered={true}
          showHours={extraItem?.['extraProps']?.['showHours'] ?? true}
          showMinutes={extraItem?.['extraProps']?.['showMinutes'] ?? true}
          showSeconds={extraItem?.['extraProps']?.['showSeconds'] ?? true}
        />
      );
    },
  },
  {
    dataIndex: 'OperDept',
    title: '手术科室',
    visible: true,
    width: 160,
    align: 'center',
    conditionDictionaryKey: 'CliDepts',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'OperDept'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'CliDepts'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'InsurCode',
    title: '医保手术编码',
    visible: false,
    readonly: true,
    width: 0,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'InsurName',
    title: '医保手术名称',
    visible: false,
    readonly: true,
    width: 0,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem className={'dmr-oper-name'} />;
    },
    onCell: nonAddCell,
  },
  // {
  //   dataIndex: 'IsMain',
  //   title: '医保主手术',
  //   visible: true,
  //   width: 70,
  //   align: 'center',
  //   renderColumnFormItem: (node, record, index, dataIndex, form) => {
  //     return (
  //       <IcdeOperCheckbox
  //         id={`IsMain#${index}#OperMainTable`}
  //         recordId={record?.id}
  //         dataIndex={dataIndex}
  //         onChangeExtra={(checked) => {
  //           Emitter.emit(EventConstant.DMR_OPER_INSURE_MAIN, {
  //             id: record?.id,
  //             values: {
  //               IsMain: checked,
  //             },
  //             index: index,
  //           });
  //         }}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  // {
  //   dataIndex: 'IsReported',
  //   title: '医保上报',
  //   visible: true,
  //   width: 50,
  //   align: 'center',
  //   dependencyFormKey: 'IsMain',
  //   propsKey: 'disabled',
  //   dependencyFormValue: true,
  //   renderColumnFormItem: (node, record, index, dataIndex, form) => {
  //     return (
  //       <IcdeOperCheckbox
  //         id={`IsReported#${index}#OperMainTable`}
  //         recordId={record['id']}
  //         dataIndex={dataIndex}
  //         dependencyKey={'IsMain'}
  //         dependencyValue={true}
  //         form={form}
  //       />
  //     );
  //   },
  //   onCell: nonAddCell,
  // },
  {
    dataIndex: 'HqmsCode',
    title: '国家临床版编码',
    visible: true,
    readonly: true,
    width: 140,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'HqmsName',
    title: '国家临床版名称',
    visible: true,
    readonly: true,
    width: 200,
    renderColumnFormItem: (node, record, index) => {
      return <IcdeOperationReadonlyItem />;
    },
    onCell: nonAddCell,
  },
  {
    dataIndex: 'OperAccord',
    title: '符合标志',
    visible: false,
    width: 100,
    align: 'center',
    conditionDictionaryKey: 'OperAccord',
    conditionDictionaryGroup: 'Dmr',
    renderColumnFormItem: (node, record, index, dataIndex, form, extraItem) => {
      return (
        <IcdeOperationInputSelector
          tableId={'operationMainTable'}
          className={'operation-input'}
          dataIndex={'OperAccord'}
          index={index}
          listHeight={180}
          conditionDictionaryKey={'OperAccord'}
          conditionDictionaryGroup={'Dmr'}
          dropdownMatchSelectWidth={false}
          extraItem={extraItem}
        />
      );
    },
  },
  {
    dataIndex: 'operation',
    title: icdeOperRowSelection ? (
      <BatchDeleteButton tableId="operationMainTable" />
    ) : (
      ''
    ),
    visible: true,
    align: 'center',
    width: 100,
    disableComment: true,
    render: (node, record, index) => {
      return (
        <div className={'operation-item'}>
          <IconBtn
            type="copy"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_OPER_MAIN_COPY, {
                id: record?.['id'],
                index: index,
              });
            }}
          />
          <IconBtn
            type="delete"
            onClick={() => {
              Emitter.emit(EventConstant.DMR_OPER_MAIN_DELETE, index);
            }}
          />
        </div>
      );
    },
    onCell: nonAddCell,
    fixed: 'right',
  },
];

export const getOperMainColumns = (insurSeparateTableLogic?: {
  canEditMainTable?: boolean;
}) => {
  const canEditMainTable = insurSeparateTableLogic?.canEditMainTable ?? true;

  const processed = (operationMainColumnsBase as any[])
    .map((col) => {
      if (!canEditMainTable) {
        if (
          noNeedReadOnlyColumns?.findIndex((d) => d === col?.dataIndex) === -1
        ) {
          const base: any = { ...col, readonly: true };
          return {
            ...base,
            renderColumnFormItem: (
              node: any,
              record: any,
              index: number,
              dataIndex: string,
              form: any,
              extraItem: any,
            ) => (
              <IcdeOperationReadonlyItem
                conditionDictionaryKey={base?.conditionDictionaryKey}
                conditionDictionaryGroup={base?.conditionDictionaryGroup}
                extraItem={extraItem}
                style={
                  readOnlyTextCenterColumns?.findIndex(
                    (d) => d === col?.dataIndex,
                  ) !== -1
                    ? { justifyContent: 'center' }
                    : {}
                }
                type={
                  base?.dataIndex?.toLowerCase()?.indexOf('time') > -1
                    ? 'time'
                    : null
                }
              />
            ),
          };
        }
      }
      return col;
    })
    .filter((c) => !(c?.dataIndex === 'operation' && !canEditMainTable));

  // 当无编辑权限时，禁用“序”列的拖拽手柄，仅显示标签
  const updated = processed.map((col: any) => {
    if (col?.key === 'sort' || col?.dataIndex === 'OperSort') {
      if (!canEditMainTable) {
        return {
          ...col,
          render: (_node: any, record: any, index: number) => {
            if (record?.id === 'ADD') {
              return null;
            }
            const labelNode =
              index === 0 ? (
                <span>主</span>
              ) : (
                <span style={{ whiteSpace: 'nowrap' }}>{`${index}`}</span>
              );
            // 无权限时不包裹 DragHandler，避免可拖拽
            return labelNode;
          },
        };
      }
    }
    return col;
  });

  return updated;
};
