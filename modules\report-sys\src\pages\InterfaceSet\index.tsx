import React, {
  useCallback,
  useEffect,
  useState,
  useMemo,
  useReducer,
  useRef,
} from 'react';
import {
  Button,
  Card,
  Col,
  Modal,
  Popconfirm,
  Row,
  Switch,
  Tabs,
  Input,
  Form,
  List,
} from 'antd';
import { Link, useRequest } from 'umi';
import { UniSelect } from '@uni/components/src';
import { uniCommonService } from '@uni/services/src';
import './index.less';
import { useEventEmitter, useSafeState, useUpdateEffect } from 'ahooks';
import _ from 'lodash';
import { ExpandableConfig, SorterResult } from 'antd/lib/table/interface';
import { v4 as uuidv4 } from 'uuid';
import { Dispatch, useDispatch, useSelector } from 'umi';
import { useModel } from '@@/plugin-model/useModel';
import { RespVO } from '@uni/commons/src/interfaces';
import ConfigForm from './components/configForm';
import MultiFormContainer from './components/multiFormContainer';

const InterfaceSet = () => {
  const [pullDmrCardsForm] = Form.useForm();
  const [pullDmrSingleCardForm] = Form.useForm();
  const [pullDmrCardFeesForm] = Form.useForm();

  const [activeKey, setActiveKey] = useState('PullDmrCards');

  const triggersTabsData = [
    {
      label: '病案首页数据批量导入',
      key: 'PullDmrCards',
      children: (
        <MultiFormContainer
          name={'病案首页数据批量导入'}
          trigger={'PullDmrCards'}
        />
      ),
    },
    {
      label: '病案首页数据单份导入',
      key: 'PullDmrSingleCard',
      children: (
        <MultiFormContainer
          name={'病案首页数据单份导入'}
          trigger={'PullDmrSingleCard'}
        />
      ),
    },
    {
      label: '病案首页数据费用导入',
      key: 'PullDmrCardFees',
      children: (
        <MultiFormContainer
          name={'病案首页数据费用导入'}
          trigger={'PullDmrCardFees'}
        />
      ),
    },
    {
      label: '文件单份导入',
      key: 'InterfaceSetUploadTest',
      children: (
        <MultiFormContainer
          name={'文件单份导入'}
          trigger={'InterfaceSetUploadTest'}
          udfType={'InterfaceSetUpload'}
        />
      ),
    },
  ];

  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="数据导入配置">
            <Tabs
              style={{ minHeight: 350 }}
              tabPosition={'left'}
              onTabClick={(key) => setActiveKey(key)}
              activeKey={activeKey}
              items={triggersTabsData}
            />
          </Card>
        </Col>
      </Row>
    </>
  );
};
export default InterfaceSet;
