export const tableToFormKeyAndTableIdMapping = {
  IcdeDragTable: {
    'data.key': 'diagnosisTable',
    'data.props.id': 'diagnosis-table-content',
    'data.props.parentId': 'diagnosisTable',
    'data.props.tableId': 'diagnosis-table-content',
  },
  IcdeMainTable: {
    'data.key': 'diagnosisMainTable',
    'data.props.id': 'diagnosis-main-table-content',
    'data.props.parentId': 'diagnosisMainTable',
    'data.props.tableId': 'diagnosis-main-table-content',
  },
  IcdeInsurTable: {
    'data.key': 'diagnosisInsurTable',
    'data.props.id': 'diagnosis-insur-table-content',
    'data.props.parentId': 'diagnosisInsurTable',
    'data.props.tableId': 'diagnosis-insur-table-content',
  },
  OperationDragTable: {
    'data.key': 'operationTable',
    'data.props.id': 'operation-table-content',
    'data.props.parentId': 'operationTable',
    'data.props.tableId': 'operation-table-content',
  },
  OperMainTable: {
    'data.key': 'operationMainTable',
    'data.props.id': 'operation-main-table-content',
    'data.props.parentId': 'operationMainTable',
    'data.props.tableId': 'operation-main-table-content',
  },
  OperInsurTable: {
    'data.key': 'operationInsurTable',
    'data.props.id': 'operation-insur-table-content',
    'data.props.parentId': 'operationInsurTable',
    'data.props.tableId': 'operation-insur-table-content',
  },
  IcuDragTable: {
    'data.key': 'icuTable',
    'data.props.id': 'icu-table-content',
    'data.props.parentId': 'icuTable',
    'data.props.tableId': 'icu-table-content',
  },
  PathologyIcdeDragTable: {
    'data.key': 'pathologicalDiagnosisTable',
    'data.props.id': 'pathological-diagnosis-table-content',
    'data.props.parentId': 'pathologicalDiagnosisTable',
    'data.props.tableId': 'pathological-diagnosis-table-content',
  },
  TcmIcdeDragTable: {
    'data.key': 'tcmDiagnosisTable',
    'data.props.id': 'tcm-diagnosis-table-content',
    'data.props.parentId': 'tcmDiagnosisTable',
    'data.props.tableId': 'tcm-diagnosis-table-content',
  },
};
