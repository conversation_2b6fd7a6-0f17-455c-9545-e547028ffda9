import { uniCommonService } from '@uni/services/src';
import dayjs from 'dayjs';
import { RespVO } from '@uni/commons/src/interfaces';
import { CardBundleInfo, IcdeOperResp } from '@/pages/dmr/network/interfaces';
import { icuTableResponseParamProcessor } from '@/pages/dmr/processors/icu';
import assign from 'lodash/assign';
import pick from 'lodash/pick';
import keys from 'lodash/keys';
import { isEmptyValues } from '@uni/utils/src/utils';
import { message } from 'antd';

// 独立 第三方质控 按钮
export const dmrBundleThirdPartyCheckReq = async (
  hisId: string,
  originDmrCardInfo: any,
  formFieldsValue: any,
  dmrProcessorInstance: any,
  globalState: any,
) => {
  let data: CardBundleInfo = Object.assign({}, originDmrCardInfo);

  let checkData = await dmrProcessorInstance.cardSaveCheckParamProcessor(
    data,
    originDmrCardInfo,
    formFieldsValue,
    globalState?.dictData?.['Dmr'],
  );

  data = Object.assign({}, checkData);

  let reduced = new CardBundleInfo();
  let checkParams = assign(reduced, pick(data, keys(reduced)));
  checkParams['HisId'] = hisId;

  let thirdPartyCheckResponse = await uniCommonService(
    'Api/Dmr/DmrCardBundle/ThirdPartyCheck',
    {
      method: 'POST',
      data: checkParams,
    },
  );

  console.log('third party response', thirdPartyCheckResponse);
  if (!isEmptyValues(thirdPartyCheckResponse?.data)) {
    window.open(thirdPartyCheckResponse?.data);
  } else {
    message.error('第三方质控结果不存在，请联系管理员');
  }
};

const dmrGetInfoReqV2 = (hisId?: string, getInterfaceUrl?: string) => {
  return uniCommonService(getInterfaceUrl ?? 'Api/Dmr/DmrCardBundle/Get', {
    method: 'POST',
    data: {
      HisId: hisId,
    },
  });
};

export const getCardInfoV2 = async (
  hisId: string,
  dmrProcessorInstance: any,
  getInterfaceUrl?: string,
) => {
  let formFieldValue = {};

  let cardBundleInfo: RespVO<CardBundleInfo> = await dmrGetInfoReqV2(
    hisId,
    getInterfaceUrl,
  );
  if (cardBundleInfo?.code === 0 && cardBundleInfo?.statusCode === 200) {
    console.error('cardBundleInfo', cardBundleInfo);

    formFieldValue = Object.assign({}, cardBundleInfo?.data?.CardFlat);

    // 诊断
    formFieldValue = await dmrProcessorInstance.cardIcdeResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.IcdeResult,
      dmrProcessorInstance.cardIcdeInsuranceHqmsMetaDataProcessor(
        cardBundleInfo?.data?.InsurIcdeDscgs,
        cardBundleInfo?.data?.InsurIcdeOperMetaData?.IcdeMetaData?.Data,
        cardBundleInfo?.data?.HqmsIcdeOperMetaData?.IcdeMetaData?.Data,
      ),
      dmrProcessorInstance.cardIcdeInsuranceHqmsMetaDataProcessor(
        cardBundleInfo?.data?.HqmsIcdeDscgs,
        cardBundleInfo?.data?.InsurIcdeOperMetaData?.IcdeMetaData?.Data,
        cardBundleInfo?.data?.HqmsIcdeOperMetaData?.IcdeMetaData?.Data,
      ),
      cardBundleInfo?.data?.DmrIcdeOperMetaData?.IcdeMetaData,
    );

    // 中医诊断
    formFieldValue = dmrProcessorInstance.cardTcmIcdeResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.TcmIcdeResult,
    );

    // 手术
    formFieldValue = await dmrProcessorInstance.cardOperationResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Opers,
      dmrProcessorInstance.cardOperInsuranceHqmsMetaDataProcessor(
        cardBundleInfo?.data?.InsurOpers,
        cardBundleInfo?.data?.InsurIcdeOperMetaData?.OperMetaData?.Data,
        cardBundleInfo?.data?.HqmsIcdeOperMetaData?.OperMetaData?.Data,
      ),
      dmrProcessorInstance.cardOperInsuranceHqmsMetaDataProcessor(
        cardBundleInfo?.data?.HqmsOpers,
        cardBundleInfo?.data?.InsurIcdeOperMetaData?.OperMetaData?.Data,
        cardBundleInfo?.data?.HqmsIcdeOperMetaData?.OperMetaData?.Data,
      ),
      cardBundleInfo?.data?.DmrIcdeOperMetaData?.OperMetaData,
    );

    // 转科
    formFieldValue = dmrProcessorInstance.cardTransferResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Transfers,
    );

    formFieldValue = dmrProcessorInstance.cardIcuResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Icus,
    );

    // 新生儿
    formFieldValue = dmrProcessorInstance.cardBabyResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.Babys,
      cardBundleInfo?.data?.IcdeResult?.IcdeBabys,
    );
    formFieldValue['pregnantItem'] = cardBundleInfo?.data?.Preg;

    // ExtraProperties
    dmrProcessorInstance.cardExtraPropertiesResponseProcessor(
      formFieldValue,
      cardBundleInfo?.data?.CardFlat?.ExtraProperties,
    );

    // TimeRangeStats  // 用于部分地方可能出现的  仅填写小时这种操作
    formFieldValue =
      dmrProcessorInstance.cardTimeRangeStatsTransformProcessor(formFieldValue);
  }

  console.error('formFieldValue with v2', JSON.stringify(formFieldValue));
  return {
    formFieldValue: formFieldValue,
    cardBundleInfo: cardBundleInfo?.data,
  };
};

export const getDmrIndexLayoutConfig = (
  cliDeptCode,
  hospCode,
  modules?: string[],
) => {
  let identityParam = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
  };

  if (cliDeptCode) {
    identityParam = {
      IdentityCode: cliDeptCode,
      IdentityType: 'CliDepts',
    };
  } else if (hospCode) {
    identityParam = {
      IdentityCode: hospCode,
      IdentityType: 'Hospital',
    };
  }

  let data = {
    ...identityParam,
    configModules: ['DmrLayout', 'DmrHeaderLayout'].concat(modules ?? []),
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

export const getDmrMedicalRecordUrl = (data: any) => {
  return uniCommonService('Api/Dmr/DmrCardBundle/GetCardEmrUrl', {
    method: 'POST',
    data: data,
  });
};

export const getIcdeInfoWithStrictMode = async (icdeCodes: string[]) => {
  let data = {
    Codes: icdeCodes,
    HasInsurCompare: true,
    HasHqmsCompare: true,
    HasDrgsCompare: true,
    HasWtCompare: true,
  };

  let icdeInfos: RespVO<IcdeOperResp> = await uniCommonService(
    'Api/Dmr/DmrSearch/IcdeMetaData',
    {
      method: 'POST',
      data: data,
    },
  );

  return icdeInfos;
};

export const getOperInfoWithStrictMode = async (operCodse: string[]) => {
  let data = {
    Codes: operCodse,
    HasInsurCompare: true,
    HasHqmsCompare: true,
    HasDrgsCompare: true,
    HasWtCompare: true,
  };

  let operInfos: RespVO<IcdeOperResp> = await uniCommonService(
    'Api/Dmr/DmrSearch/OperMetaData',
    {
      method: 'POST',
      data: data,
    },
  );

  return operInfos;
};

export const getDmrNotes = (hisId: string) => {
  return uniCommonService('Api/Dmr/DmrCardBundle/GetNotes', {
    method: 'GET',
    params: {
      hisId: hisId,
    },
  });
};

// 获取数据库中保存的 operator sort 和 enable等字段
export const getDmrOperatorConfig = () => {
  let data = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
    configModules: ['DmrOperatorsConfig'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

// 获取数据库中保存的 rule enable等字段
export const getDmrPreCheckRulesConfig = () => {
  let data = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
    configModules: ['DmrPreCheckRules'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

// 右侧的
export const getDmrPreCheckModuleConfig = () => {
  let data = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
    configModules: ['DmrPreCheckModules'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

export const getDmrSavedMenu = () => {
  let identityParam = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
  };

  let data = {
    ...identityParam,
    configModules: ['DmrLeftMenus'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};

export const getDmrSavedTheme = () => {
  let identityParam = {
    IdentityCode: 'Global',
    IdentityType: 'Global',
  };

  let data = {
    ...identityParam,
    configModules: ['DmrTheme'],
  };
  return uniCommonService('Api/Sys/ClientKitSys/GetValues', {
    method: 'POST',
    requestType: 'json',
    data: data,
  });
};
