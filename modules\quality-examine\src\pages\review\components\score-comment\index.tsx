import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message, Modal, Spin, Card } from 'antd';
import './index.less';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import RightItemScoreContainer from '@/pages/review/components/score-comment/score';
import { useUpdateEffect } from 'ahooks';
import { isEmptyValues } from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src';
import { QualityExamineStatus } from '@/pages/review/components/score-comment/score/constant';
import { Responsive } from 'react-grid-layout';
import RightItemCommentContainer from '@/pages/review/components/score-comment/comment';
import SvgLine from '@/pages/review/components/score-comment/Connector';
// @ts-ignore
import { MicroAppWithMemoHistory } from 'umi';
import { createPortal } from 'react-dom';
import { useReviewTabContext } from '@/pages/review/tab-hoc';
import { BatchMasterSysItem, TaskItem } from '@/pages/review/interface';
import RightItemRuleCommentContainer from '@/pages/review/components/score-comment/comment/rule-comment';
import { useRouteProps } from '@uni/commons/src/route-context';
import TextArea from 'antd/lib/input/TextArea';

export interface TemplateItem {
  AppCode?: string;
  Description?: string;
  DisplayExpr?: string;
  EnableRule?: boolean;
  EnableScore?: boolean;
  Expr?: string;
  ExprProvider?: string;
  FullScore?: number;
  IsAdditiveScore?: boolean;
  IsValid?: boolean;
  Scenario?: string;
  ScoreLineA?: number;
  ScoreLineB?: number;
  ScoreLineC?: number;
  TemplateId?: string;
}

interface ScoreCommentDrawerContainerProps {
  drawerContainerRef: any;
  onScoreCommentReviewEnd?: (taskId: number) => void;
  onScoreCommentClose?: (taskId: number) => void;
  onScoreCommentTableRefresh?: (taskId: number) => void;

  tableReadonly?: boolean;
  dmrReadonly?: boolean;

  getContainer: () => any;
}

const operations = [
  {
    label: '关闭',
    key: 'CLOSE',
  },
  {
    label: '提交评审结果',
    key: 'SUBMIT',
    btnType: 'primary',
  },
  {
    label: '退回返修',
    key: 'REJECT',
    btnType: 'primary',
    danger: true,
  },
  {
    label: '完成复核',
    key: 'TASK_ERROR',
    btnType: 'primary',
  },

  {
    label: '完成整改',
    key: 'REVIEWEE_ACCEPT',
  },
];

const ScoreCommentDrawerContainer = (
  props: ScoreCommentDrawerContainerProps,
) => {
  const taskItemRef = useRef(null);

  const svgLinesContainerRef = useRef(null);

  const dmrGridContainerRef = useRef(null);

  const detailCommentRef = useRef(null);

  const extraDmrContainerRef = useRef(null);

  const [scoreCommentDrawerOpen, setScoreCommentDrawerOpen] = useState(false);

  const [hisId, setHisId] = useState(undefined);

  const [taskId, setTaskId] = useState(undefined);

  const [taskItem, setTaskItem] = useState(undefined);

  const [currentItemTemplateId, setCurrentItemTemplateId] = useState(undefined);

  const [templateItem, setTemplateItem] = useState<TemplateItem>(undefined);

  const { examineMasterId } = useRouteProps();

  const [currentSysMaster, setCurrentSysMaster] =
    useState<BatchMasterSysItem>(undefined);

  const dmrExtra = (currentSysMaster: BatchMasterSysItem) => {
    return {
      isFullScreen: false,
      // type: 'READONLY',
      type: 'QUALITY_EXAMINE',
      anchorShow: false,
      viewMode: true,
      leftContainerShow: false,
      queryHeaderShow: false,
      qualityCheckShow: false,
      preReview: false,
      selectorWithNotValid: false,

      rootContainerHeight: '100%',

      dmrGridContainerRef: dmrGridContainerRef,
      detailCommentRef: detailCommentRef,
      // enableGridItemComment: templateItem?.EnableRule === false,
      // enableGridItemComment: true,

      commonContainerStyle: {},

      svgLinesContainerRef: svgLinesContainerRef,

      source: 'DMR_EXAMINE',

      extraDmrContainerRef: extraDmrContainerRef,

      dmrExamineCheckContainerMountId: 'dmr-examine-check-container',
      instantAudit: true, // 立即质控
    };
  };

  React.useImperativeHandle(props?.drawerContainerRef, () => {
    return {
      showDrawer: async (data: any) => {
        let taskItem = data?.taskItem;
        console.log('批注预备用Console: showDrawer', data);
        // 如果是 otherExamineTaskId 要换 一套taskItem 出来
        if (!isEmptyValues(data?.extraExamineTaskId)) {
          taskItem = await getTaskItem(data?.extraExamineTaskId);
        }

        setHisId(taskItem?.HisId);
        setCurrentItemTemplateId(taskItem?.TemplateId);

        // task
        setTaskId(taskItem?.TaskId);
        setTaskItem(taskItem);

        // 获取template
        // TODO ???
        let templateItem: TemplateItem = await templateInfoReq(
          taskItem?.TemplateId,
        );

        let sysMaster = await getBatchMasterItemReq(
          examineMasterId ?? taskItem?.MasterId,
        );

        let taskStatus = await startReview(taskItem);

        console.log(
          '批注预备用Console: enableGridItemComment data props taskStatus sysMaster',
          data?.readonly === true
            ? false
            : props?.tableReadonly !== true &&
                taskStatus === QualityExamineStatus.Reviewing &&
                sysMaster?.ReviewSetting?.ReviewMode === 'Annotation',
          data,
          props,
          taskStatus,
          sysMaster,
        );
        setTimeout(() => {
          setScoreCommentDrawerOpen(data?.status);
          (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
            status: data?.status,
            hisId: taskItem.HisId,
            parent: {
              className: `dmr-aod-review-container ${
                sysMaster?.ReviewSetting?.ReviewMode === 'Table'
                  ? 'dmr-aod-review-score-container'
                  : 'dmr-aod-review-comment-container'
              }`,
              mask: false,
              closable: false,
            },

            ...dmrExtra(sysMaster),

            enableGridItemComment:
              data?.readonly === true
                ? false
                : props?.tableReadonly !== true &&
                  taskStatus === QualityExamineStatus.Reviewing &&
                  sysMaster?.ReviewSetting?.ReviewMode === 'Annotation',
            // enableGridItemComment:
            //   taskStatus === QualityExamineStatus.Reviewing,

            onCardBundleGetExtraProcess: () => {
              return {
                viewModeByRegisterStatus:
                  data?.readonly === true
                    ? false
                    : !(
                        props?.dmrReadonly === false &&
                        taskStatus === QualityExamineStatus.Rejected
                      ),
              };
            },
            // dmrRegistrationReadOnly:
            //   data?.readonly === true
            //     ? false
            //     : !(
            //         props?.dmrReadonly === false &&
            //         taskStatus === QualityExamineStatus.Rejected
            //       ),

            dmrRegistrationReadOnly: false,

            onOperationExtraProcess: (itemType: string) => {
              return [
                'MEDICAL_RECORDS',
                'SAVE',
                'EDIT',
                'CHSGROUP_AUDIT',
                'DMR_ICDE_DATA',
                'DMR_OPER_DATA',
              ]?.includes(itemType);
              // return ['CHSGROUP_AUDIT']?.includes(itemType);
              // if (data?.readonly === true) {
              //   return false;
              // }
              //
              // return props?.dmrReadonly === false &&
              //   taskStatus === QualityExamineStatus.Rejected
              //   ? ['SAVE', 'EDIT']?.includes(itemType)
              //   : false;
            },
          });
        }, 200);
      },
    };
  });

  React.useImperativeHandle(taskItemRef, () => {
    return {
      updateTaskItem: (taskId: string) => {
        if (!isEmptyValues(taskId)) {
          getTaskItem(taskId);
        }
      },
    };
  });

  const { run: getTaskItem } = useRequest(
    (taskId: string) => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetTask', {
        method: 'POST',
        data: {
          TaskId: taskId,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TaskItem>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setTaskItem(response?.data);
          return response?.data;
        }
      },
    },
  );

  const { loading: getBatchMasterItemLoading, run: getBatchMasterItemReq } =
    useRequest(
      (masterId: string) => {
        return uniCommonService(
          'Api/Sys/QualityExamineSys/GetQualityExamineSys',
          {
            method: 'GET',
            params: {
              MasterId: masterId,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchMasterSysItem>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setCurrentSysMaster(response?.data);
          } else {
            setCurrentSysMaster(undefined);
          }

          return response?.data;
        },
      },
    );

  const startReview = async (taskItem: any) => {
    if (taskItem?.Status === QualityExamineStatus.Pending) {
      let startReviewResponse: RespVO<any> = await uniCommonService(
        'Api/Dmr/DmrCardQualityExamine/StartReview',
        {
          method: 'POST',
          data: {
            TaskId: taskItem?.TaskId,
          },
        },
      );

      if (
        startReviewResponse?.code === 0 &&
        startReviewResponse?.statusCode === 200
      ) {
        setTaskItem({
          ...taskItem,
          Status: QualityExamineStatus.Reviewing,
        });

        // 回调刷新 审核任务表
        props?.onScoreCommentTableRefresh &&
          props?.onScoreCommentTableRefresh(taskItem?.TaskId);

        return QualityExamineStatus?.Reviewing;
      }
    }

    return taskItem?.Status;
  };

  const { loading: templateInfoLoading, run: templateInfoReq } = useRequest(
    (templateId: string) => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetTemplate', {
        method: 'POST',
        data: {
          templateId: templateId,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<TemplateItem>) => {
        setTemplateItem(response?.data);
        return response?.data;
      },
    },
  );

  const { loading: taskScoreCommentEndLoading, run: taskScoreCommentEndReq } =
    useRequest(
      () => {
        return uniCommonService('Api/Dmr/DmrCardQualityExamine/EndReview', {
          method: 'POST',
          data: {
            TaskId: taskId,
          },
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<any>) => {
          message.success('提交成功');
          props?.onScoreCommentReviewEnd &&
            props?.onScoreCommentReviewEnd(taskId);
          onScoreCommentOperationClick('CLOSE');
        },
      },
    );

  const {
    loading: taskScoreCommentRejectLoading,
    run: taskScoreCommentRejectReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/RejectTask', {
        method: 'POST',
        data: {
          TaskId: taskId,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        message.success('通知返修成功');
        props?.onScoreCommentReviewEnd &&
          props?.onScoreCommentReviewEnd(taskId);
        onScoreCommentOperationClick('CLOSE');
      },
    },
  );

  const {
    loading: taskScoreCommentAcceptLoading,
    run: taskScoreCommentAcceptReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/AcceptTask', {
        method: 'POST',
        data: {
          TaskId: taskId,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        message.success('返修通过');
        props?.onScoreCommentReviewEnd &&
          props?.onScoreCommentReviewEnd(taskId);
        onScoreCommentOperationClick('CLOSE');
      },
    },
  );

  const {
    loading: taskScoreCommentTaskErrorLoading,
    run: taskScoreCommentTaskErrorReq,
  } = useRequest(
    (content?: string) => {
      return uniCommonService(
        'Api/Dmr/DmrCardQualityExamine/SetInvalidErrorTask',
        {
          method: 'POST',
          data: {
            TaskId: taskId,
            Content: content,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        message.success('完成复核');
        props?.onScoreCommentReviewEnd &&
          props?.onScoreCommentReviewEnd(taskId);
        onScoreCommentOperationClick('CLOSE');
      },
    },
  );

  const {
    loading: taskScoreCommentRevieweeAcceptLoading,
    run: taskScoreCommentRevieweeAcceptReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/ResolveTask', {
        method: 'POST',
        data: {
          TaskId: taskId,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        message.success('成功接受全部意见');
        props?.onScoreCommentReviewEnd &&
          props?.onScoreCommentReviewEnd(taskId);
        onScoreCommentOperationClick('CLOSE');
      },
    },
  );

  const {
    loading: taskScoreCommentRevieweeReSubmittedLoading,
    run: taskScoreCommentRevieweeReSubmittedReq,
  } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/SubmitTask', {
        method: 'POST',
        data: {
          TaskId: taskId,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        message.success('成功提交');
        props?.onScoreCommentReviewEnd &&
          props?.onScoreCommentReviewEnd(taskId);
        onScoreCommentOperationClick('CLOSE');
      },
    },
  );

  const onScoreCommentOperationClick = (type: string) => {
    switch (type) {
      case 'CLOSE':
        setScoreCommentDrawerOpen(false);
        (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
          status: false,
        });
        props?.onScoreCommentClose && props?.onScoreCommentClose(taskId);
        break;
      case 'SUBMIT':
        // 提交
        Modal.confirm({
          zIndex: 10050,
          title: `提交评审结果`,
          content: (
            <span>
              <span className={'red-warning'}>提交之后不能再修改评审意见</span>
            </span>
          ),
          onOk: () => {
            taskScoreCommentEndReq();
          },
        });
        break;
      case 'REJECT':
        taskScoreCommentRejectReq();
        break;
      case 'ACCEPT':
        taskScoreCommentAcceptReq();
        break;
      case 'TASK_ERROR':
        Modal.confirm({
          zIndex: 10050,
          title: ``,
          okText: '提交',
          cancelText: '返回',
          icon: null,
          className: 'task-error-container',
          content: (
            <Card title={'复核意见'}>
              <TextArea
                id={'task-error-textarea'}
                showCount={false}
                autoFocus={true}
                autoSize={{ minRows: 4, maxRows: 4 }}
                maxLength={-1}
                style={{ minHeight: 100, resize: 'none' }}
                placeholder="请填写意见（留空既无意见）"
              />
            </Card>
          ),
          onOk: () => {
            let value = document?.getElementById('task-error-textarea')?.value;
            taskScoreCommentTaskErrorReq(value?.trim());
          },
        });
        break;
      case 'REVIEWEE_ACCEPT':
        taskScoreCommentRevieweeAcceptReq();
        break;
      case 'REVIEWEE_RESUBMITTED':
        taskScoreCommentRevieweeReSubmittedReq();
        break;
      default:
        break;
    }
  };

  console.log(
    'ReviewMode',
    currentSysMaster?.ReviewSetting?.ReviewMode === 'Annotation'
      ? templateItem?.EnableRule === false
        ? 'COMMENT-ITEM'
        : 'RULE-SCORE-ITEM'
      : '',
  );

  return (
    <Drawer
      placement="right"
      mask={true}
      maskClosable={true}
      width={'100%'}
      open={scoreCommentDrawerOpen}
      className={'score-comment-drawer-container'}
      onClose={() => {
        setScoreCommentDrawerOpen(false);
        (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
          status: false,
        });
        props?.onScoreCommentClose && props?.onScoreCommentClose(taskId);
      }}
      destroyOnClose={true}
      title={`病案首页评审`}
      getContainer={props?.getContainer}
    >
      {createPortal(
        scoreCommentDrawerOpen ? (
          <SvgLine
            currentSysMaster={currentSysMaster}
            templateItem={templateItem}
            detailCommentRef={detailCommentRef}
            containerRef={svgLinesContainerRef}
          />
        ) : null,
        document.body,
        'svg-line-container',
      )}

      <Spin
        spinning={
          taskScoreCommentEndLoading ||
          templateInfoLoading ||
          taskScoreCommentAcceptLoading ||
          taskScoreCommentRejectLoading
        }
      >
        <div
          id={'score-comment-content-container'}
          className={'score-comment-content-container'}
        >
          <div
            className={'full-dmr-container'}
            style={
              currentSysMaster?.ReviewSetting?.ReviewMode === 'Annotation'
                ? { width: '76%', minWidth: '76%' }
                : {}
            }
          ></div>

          {currentSysMaster?.ReviewSetting?.ReviewMode === 'Table' && (
            <div className={'score-content-container'}>
              <RightItemScoreContainer
                scoreTemplateId={currentItemTemplateId}
                hisId={hisId}
                taskId={taskId}
                taskStatus={taskItem?.Status}
                tableReadonly={props?.tableReadonly}
                reviewedTotalScore={taskItem?.Score}
                columnType={'Tree'}
              />
            </div>
          )}

          {currentSysMaster?.ReviewSetting?.ReviewMode === 'Annotation' && (
            <div className={'score-content-container'} style={{ width: '28%' }}>
              {templateItem?.EnableRule === false && (
                <RightItemCommentContainer
                  taskItemRef={taskItemRef}
                  sysMasterItem={currentSysMaster}
                  drawerStatus={scoreCommentDrawerOpen}
                  taskId={taskId}
                  tableReadonly={props?.tableReadonly}
                  commentTemplateId={currentItemTemplateId}
                  taskStatus={taskItem?.Status}
                  detailCommentRef={detailCommentRef}
                  dmrGridContainerRef={dmrGridContainerRef}
                  svgLinesContainerRef={svgLinesContainerRef}
                  onIssueUpdate={() => {
                    getBatchMasterItemReq(
                      examineMasterId ?? taskItem?.MasterId,
                    );
                  }}
                />
              )}

              {templateItem?.EnableRule === true && (
                <RightItemRuleCommentContainer
                  extraDmrContainer={extraDmrContainerRef}
                  taskItemRef={taskItemRef}
                  sysMasterItem={currentSysMaster}
                  drawerStatus={scoreCommentDrawerOpen}
                  taskId={taskId}
                  tableReadonly={props?.tableReadonly}
                  commentTemplateId={currentItemTemplateId}
                  taskStatus={taskItem?.Status}
                  detailCommentRef={detailCommentRef}
                  dmrGridContainerRef={dmrGridContainerRef}
                  svgLinesContainerRef={svgLinesContainerRef}
                  onIssueUpdate={() => {
                    getBatchMasterItemReq(
                      examineMasterId ?? taskItem?.MasterId,
                    );
                  }}
                />
              )}
            </div>
          )}
        </div>

        <div className={'score-comment-operation-container'}>
          {operations
            ?.filter((item) => {
              // 被审核人
              if (props?.tableReadonly === true) {
                if (
                  currentSysMaster?.ReviewSetting?.EnableReject === true ||
                  currentSysMaster?.ReviewSetting?.EnableAutoReject === true
                ) {
                  if (item?.key === 'REVIEWEE_ACCEPT') {
                    return taskItem?.Status === QualityExamineStatus.Rejected;
                  }

                  if (item?.key === 'REVIEWEE_RESUBMITTED') {
                    return taskItem?.Status === QualityExamineStatus.Rejected;
                  }
                }
              }

              // 审核人
              if (props?.tableReadonly === false) {
                if (item?.key === 'SUBMIT') {
                  return (
                    taskItem?.Status === QualityExamineStatus.Reviewing ||
                    taskItem?.Status === QualityExamineStatus.Pending
                  );
                }

                if (item?.key === 'REJECT') {
                  return (
                    currentSysMaster?.ReviewSetting?.EnableReject === true &&
                    (taskItem?.Status === QualityExamineStatus.Reviewed ||
                      taskItem?.Status === QualityExamineStatus.ReSubmitted)
                  );
                }

                if (item?.key === 'TASK_ERROR') {
                  return taskItem?.Status === QualityExamineStatus.ReSubmitted;
                }
              }

              if (item?.key === 'CLOSE') {
                return true;
              }
            })
            ?.map((item) => {
              return (
                <Button
                  className={'score-comment-operation-item'}
                  key={item?.key}
                  type={item?.btnType as any}
                  danger={item?.danger ?? false}
                  onClick={() => {
                    onScoreCommentOperationClick(item?.key);
                  }}
                >
                  {item?.label}
                </Button>
              );
            })}
        </div>
      </Spin>
    </Drawer>
  );
};

export default ScoreCommentDrawerContainer;
